#!/bin/bash

# ProxyHub UI 组件库配置脚本
# 支持快速配置不同的 UI 组件库

set -e

echo "🎨 ProxyHub UI 组件库配置向导"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示选项
echo -e "${BLUE}请选择要配置的 UI 组件库:${NC}"
echo "1) shadcn/ui (推荐)"
echo "2) Ant Design"
echo "3) Chakra UI"
echo "4) Mantine"
echo "5) 保持当前内联样式"

read -p "请输入选项 (1-5): " choice

case $choice in
  1)
    echo -e "${GREEN}正在配置 shadcn/ui...${NC}"
    
    # 安装 Tailwind CSS
    echo "📦 安装 Tailwind CSS..."
    npm install -D tailwindcss postcss autoprefixer
    npx tailwindcss init -p
    
    # 安装 shadcn/ui 依赖
    echo "📦 安装 shadcn/ui 依赖..."
    npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge lucide-react
    
    # 初始化 shadcn/ui
    echo "🔧 初始化 shadcn/ui..."
    npx shadcn-ui@latest init --yes
    
    # 添加常用组件
    echo "📦 添加常用组件..."
    npx shadcn-ui@latest add button
    npx shadcn-ui@latest add card
    npx shadcn-ui@latest add input
    npx shadcn-ui@latest add label
    npx shadcn-ui@latest add select
    npx shadcn-ui@latest add dialog
    npx shadcn-ui@latest add badge
    npx shadcn-ui@latest add alert
    
    echo -e "${GREEN}✅ shadcn/ui 配置完成！${NC}"
    echo -e "${YELLOW}📖 使用指南: docs/UI_COMPONENTS.md${NC}"
    ;;
    
  2)
    echo -e "${GREEN}正在配置 Ant Design...${NC}"
    
    # 安装 Ant Design
    echo "📦 安装 Ant Design..."
    npm install antd @ant-design/nextjs-registry
    
    # 创建配置文件
    echo "🔧 创建配置文件..."
    cat > src/app/providers.tsx << 'EOF'
'use client'

import { AntdRegistry } from '@ant-design/nextjs-registry'

export function Providers({ children }: { children: React.ReactNode }) {
  return <AntdRegistry>{children}</AntdRegistry>
}
EOF
    
    echo -e "${GREEN}✅ Ant Design 配置完成！${NC}"
    echo -e "${YELLOW}⚠️  请在 app/layout.tsx 中添加 Providers 组件${NC}"
    ;;
    
  3)
    echo -e "${GREEN}正在配置 Chakra UI...${NC}"
    
    # 安装 Chakra UI
    echo "📦 安装 Chakra UI..."
    npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion
    
    # 创建配置文件
    echo "🔧 创建配置文件..."
    cat > src/app/providers.tsx << 'EOF'
'use client'

import { ChakraProvider } from '@chakra-ui/react'

export function Providers({ children }: { children: React.ReactNode }) {
  return <ChakraProvider>{children}</ChakraProvider>
}
EOF
    
    echo -e "${GREEN}✅ Chakra UI 配置完成！${NC}"
    echo -e "${YELLOW}⚠️  请在 app/layout.tsx 中添加 Providers 组件${NC}"
    ;;
    
  4)
    echo -e "${GREEN}正在配置 Mantine...${NC}"
    
    # 安装 Mantine
    echo "📦 安装 Mantine..."
    npm install @mantine/core @mantine/hooks @mantine/form @mantine/notifications
    
    # 创建配置文件
    echo "🔧 创建配置文件..."
    cat > src/app/providers.tsx << 'EOF'
'use client'

import { MantineProvider } from '@mantine/core'

export function Providers({ children }: { children: React.ReactNode }) {
  return <MantineProvider>{children}</MantineProvider>
}
EOF
    
    echo -e "${GREEN}✅ Mantine 配置完成！${NC}"
    echo -e "${YELLOW}⚠️  请在 app/layout.tsx 中添加 Providers 组件${NC}"
    ;;
    
  5)
    echo -e "${BLUE}保持当前内联样式系统${NC}"
    echo -e "${GREEN}✅ 当前配置无需更改${NC}"
    echo -e "${YELLOW}📖 内联样式使用指南: docs/UI_COMPONENTS.md${NC}"
    ;;
    
  *)
    echo -e "${RED}❌ 无效选项，请重新运行脚本${NC}"
    exit 1
    ;;
esac

echo ""
echo -e "${GREEN}🎉 配置完成！${NC}"
echo -e "${BLUE}📚 更多信息:${NC}"
echo "- 📖 UI 组件库使用指南: docs/UI_COMPONENTS.md"
echo "- 🚀 项目文档: PROJECT_SUMMARY.md"
echo "- 💻 开发指南: docs/DEVELOPMENT.md"
echo ""
echo -e "${YELLOW}💡 提示: 运行 'npm run dev' 启动开发服务器${NC}"
