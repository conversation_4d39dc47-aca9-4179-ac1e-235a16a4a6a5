"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/__barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!./node_modules/@ant-design/icons/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloudOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('1');\n    // 核心统计数据\n    const stats = [\n        {\n            title: '全球节点',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, this),\n            color: '#3b82f6',\n            description: '覆盖全球主要国家和地区'\n        },\n        {\n            title: '企业用户',\n            value: 10000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, this),\n            color: '#10b981',\n            description: '服务全球企业客户'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__.ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, this),\n            color: '#f59e0b',\n            description: 'SLA 服务等级保证'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 13\n            }, this),\n            color: '#8b5cf6',\n            description: '全天候专业技术支持'\n        }\n    ];\n    // 核心功能特性\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',\n            tags: [\n                'HTTP/HTTPS',\n                'SOCKS5',\n                '高兼容性'\n            ],\n            color: '#3b82f6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 13\n            }, this),\n            title: '全球节点网络',\n            description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',\n            tags: [\n                '50+ 国家',\n                '低延迟',\n                '高速度'\n            ],\n            color: '#10b981'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__.ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，多重安全防护，保障数据传输安全',\n            tags: [\n                '军用级加密',\n                '多重防护',\n                '隐私保护'\n            ],\n            color: '#f59e0b'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 13\n            }, this),\n            title: '智能监控',\n            description: '实时监控系统状态，智能故障检测，确保服务稳定运行',\n            tags: [\n                '实时监控',\n                '智能检测',\n                '自动恢复'\n            ],\n            color: '#8b5cf6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, this),\n            title: 'API 集成',\n            description: '完整的 RESTful API，支持自动化管理和第三方系统集成',\n            tags: [\n                'RESTful API',\n                '自动化',\n                '易集成'\n            ],\n            color: '#ef4444'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 13\n            }, this),\n            title: '灵活配置',\n            description: '支持自定义配置，满足不同业务场景的个性化需求',\n            tags: [\n                '自定义配置',\n                '灵活部署',\n                '场景适配'\n            ],\n            color: '#06b6d4'\n        }\n    ];\n    // 使用步骤\n    const steps = [\n        {\n            title: '注册账户',\n            description: '快速注册，获取专属 API 密钥',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '选择套餐',\n            description: '根据业务需求选择合适的服务套餐',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '配置接入',\n            description: '通过 API 或控制面板配置代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '开始使用',\n            description: '享受稳定高效的全球代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: '某大型电商平台',\n            industry: '电子商务',\n            content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',\n            avatar: 'E',\n            color: '#3b82f6'\n        },\n        {\n            company: '某金融科技公司',\n            industry: '金融科技',\n            content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',\n            avatar: 'F',\n            color: '#10b981'\n        },\n        {\n            company: '某数据分析公司',\n            industry: '数据分析',\n            content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',\n            avatar: 'D',\n            color: '#f59e0b'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                    padding: '0 24px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000,\n                    borderBottom: '1px solid rgba(0,0,0,0.06)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1400px',\n                            margin: '0 auto',\n                            height: '72px'\n                        },\n                        className: \"jsx-2af30dff10d881d1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '16px'\n                                    },\n                                    className: \"jsx-2af30dff10d881d1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '48px',\n                                                height: '48px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                            },\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                style: {\n                                                    fontSize: '24px',\n                                                    color: '#fff'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 3,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#1e293b',\n                                                        fontSize: '24px',\n                                                        fontWeight: '700',\n                                                        lineHeight: '1'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '13px',\n                                                        color: '#64748b',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Enterprise Proxy Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-2af30dff10d881d1\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"开发者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e2e8f0',\n                                                height: '24px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#475569',\n                                                    fontSize: '15px',\n                                                    height: '40px',\n                                                    borderRadius: '8px'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.target.style.color = '#3b82f6';\n                                                    e.target.style.background = '#f1f5f9';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.target.style.color = '#475569';\n                                                    e.target.style.background = 'transparent';\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                    borderColor: 'transparent',\n                                                    borderRadius: '10px',\n                                                    height: '44px',\n                                                    paddingLeft: '24px',\n                                                    paddingRight: '24px',\n                                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                                                    border: 'none'\n                                                },\n                                                children: \"免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                className: \"jsx-2af30dff10d881d1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-2af30dff10d881d1\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '8px',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none',\n                                            fontSize: '18px',\n                                            width: '44px',\n                                            height: '44px',\n                                            borderRadius: '8px'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"2af30dff10d881d1\",\n                        children: \"@media(min-width:1024px){.desktop-nav.jsx-2af30dff10d881d1{display:block!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:1023px)and (min-width:640px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:639px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '10px',\n                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                style: {\n                                    fontSize: '20px',\n                                    color: '#fff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#1e293b',\n                                        fontWeight: '700',\n                                        fontSize: '18px'\n                                    },\n                                    children: \"ProxyHub\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#64748b',\n                                        fontSize: '12px'\n                                    },\n                                    children: \"Enterprise Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 320,\n                styles: {\n                    body: {\n                        padding: '24px'\n                    },\n                    header: {\n                        borderBottom: '1px solid #f1f5f9',\n                        paddingBottom: '16px'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"导航菜单\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#3b82f6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                \"产品特性\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#10b981'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                \"解决方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#f59e0b'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this),\n                                \"价格方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#8b5cf6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 13\n                                }, this),\n                                \"开发者\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            style: {\n                                margin: '24px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"账户操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#475569',\n                                    borderRadius: '12px',\n                                    border: '1px solid #e2e8f0'\n                                },\n                                children: \"登录账户\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 587,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                    borderColor: 'transparent',\n                                    borderRadius: '12px',\n                                    marginTop: '8px',\n                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                },\n                                children: \"免费试用\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',\n                            padding: '140px 24px 120px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.6\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '20%',\n                                    left: '10%',\n                                    width: '300px',\n                                    height: '300px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(40px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 645,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '60%',\n                                    right: '10%',\n                                    width: '200px',\n                                    height: '200px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(30px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '56px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '16px',\n                                                background: 'rgba(255,255,255,0.08)',\n                                                backdropFilter: 'blur(20px)',\n                                                border: '1px solid rgba(255,255,255,0.12)',\n                                                borderRadius: '60px',\n                                                padding: '12px 32px',\n                                                marginBottom: '32px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '10px',\n                                                                height: '10px',\n                                                                borderRadius: '50%',\n                                                                background: '#10b981',\n                                                                boxShadow: '0 0 12px #10b981',\n                                                                animation: 'pulse 2s infinite'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"服务 10,000+ 企业客户\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    type: \"vertical\",\n                                                    style: {\n                                                        borderColor: 'rgba(255,255,255,0.2)',\n                                                        height: '16px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            style: {\n                                                                color: '#f59e0b',\n                                                                fontSize: '16px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"行业领先\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 708,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(3rem, 8vw, 5rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '800',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.03em',\n                                            textShadow: '0 4px 20px rgba(0,0,0,0.3)'\n                                        },\n                                        children: [\n                                            \"企业级全球代理\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #3b82f6, #06b6d4)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text',\n                                                    fontWeight: '800'\n                                                },\n                                                children: \"解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.2rem, 3vw, 1.5rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '56px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 56px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"为企业提供稳定、安全、高速的全球代理网络服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% SLA 保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#34d399',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家节点\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"24/7 技术支持\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                wrap: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/register\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            type: \"primary\",\n                                                            size: \"large\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            style: {\n                                                                height: '64px',\n                                                                fontSize: '18px',\n                                                                fontWeight: '700',\n                                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                                borderColor: 'transparent',\n                                                                borderRadius: '16px',\n                                                                paddingLeft: '40px',\n                                                                paddingRight: '40px',\n                                                                boxShadow: '0 12px 32px rgba(59, 130, 246, 0.4)',\n                                                                border: 'none',\n                                                                minWidth: '200px'\n                                                            },\n                                                            children: \"立即免费试用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 763,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        size: \"large\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 25\n                                                        }, void 0),\n                                                        style: {\n                                                            height: '64px',\n                                                            fontSize: '18px',\n                                                            fontWeight: '600',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            border: '2px solid rgba(255,255,255,0.16)',\n                                                            color: '#ffffff',\n                                                            backdropFilter: 'blur(20px)',\n                                                            borderRadius: '16px',\n                                                            paddingLeft: '40px',\n                                                            paddingRight: '40px',\n                                                            minWidth: '180px'\n                                                        },\n                                                        children: \"观看演示\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '48px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.6)',\n                                                            fontSize: '14px',\n                                                            display: 'block',\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: \"已有数千家企业选择我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: \"large\",\n                                                        wrap: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"10K+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 817,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"企业用户\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"50+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 825,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"国家节点\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 828,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"99.9%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"稳定性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 836,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 832,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        gutter: [\n                                            32,\n                                            32\n                                        ],\n                                        style: {\n                                            marginBottom: '0'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    style: {\n                                                        textAlign: 'center',\n                                                        background: 'rgba(255,255,255,0.98)',\n                                                        backdropFilter: 'blur(20px)',\n                                                        border: '1px solid rgba(255,255,255,0.3)',\n                                                        borderRadius: '20px',\n                                                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                        cursor: 'default',\n                                                        overflow: 'hidden'\n                                                    },\n                                                    bodyStyle: {\n                                                        padding: '32px 24px'\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(-8px)';\n                                                        e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(0)';\n                                                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '20px',\n                                                                display: 'flex',\n                                                                justifyContent: 'center',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '60px',\n                                                                    height: '60px',\n                                                                    borderRadius: '16px',\n                                                                    background: \"linear-gradient(135deg, \".concat(stat.color, \"15, \").concat(stat.color, \"25)\"),\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    justifyContent: 'center',\n                                                                    border: \"2px solid \".concat(stat.color, \"20\")\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '24px',\n                                                                        color: stat.color\n                                                                    },\n                                                                    children: stat.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 876,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 870,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                style: {\n                                                                    color: '#64748b',\n                                                                    fontSize: '14px',\n                                                                    fontWeight: '500',\n                                                                    textTransform: 'uppercase',\n                                                                    letterSpacing: '0.5px'\n                                                                },\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 896,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            value: stat.value,\n                                                            suffix: stat.suffix,\n                                                            valueStyle: {\n                                                                color: '#1e293b',\n                                                                fontWeight: '700',\n                                                                fontSize: '28px',\n                                                                lineHeight: '1.2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 926,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 944,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 948,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 952,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 965,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 986,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 988,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 979,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1006,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 998,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 997,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1022,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1021,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1015,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1040,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1058,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1060,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1052,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1051,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1076,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1075,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1078,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1070,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 962,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1097,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1091,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 624,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1176,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 1149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 1144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"R+rp0qcabHV79vV49OYD5uovxls=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});