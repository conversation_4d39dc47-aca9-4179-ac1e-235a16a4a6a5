'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { 
  Layout, 
  Menu, 
  Avatar, 
  Dropdown, 
  Typography, 
  Space,
  Button,
  Badge,
  Divider,
  Breadcrumb,
  theme
} from 'antd';
import {
  DashboardOutlined,
  DesktopOutlined,
  ShoppingCartOutlined,
  HistoryOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SafetyOutlined,
  ApiOutlined,
  WalletOutlined,
  QuestionCircleOutlined,
  HomeOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, setUser] = useState<User | null>(null);
  const [collapsed, setCollapsed] = useState(false);
  const [notifications, setNotifications] = useState(3);
  const router = useRouter();
  const pathname = usePathname();
  const { token } = theme.useToken();

  useEffect(() => {
    // 检查用户登录状态
    const userData = localStorage.getItem('user');
    const authToken = localStorage.getItem('token');
    
    if (!userData || !authToken) {
      router.push('/login');
      return;
    }

    try {
      setUser(JSON.parse(userData));
    } catch (error) {
      console.error('Failed to parse user data:', error);
      router.push('/login');
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    router.push('/login');
  };

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/dashboard/proxies',
      icon: <DesktopOutlined />,
      label: '代理管理',
    },
    {
      key: '/dashboard/buy',
      icon: <ShoppingCartOutlined />,
      label: '购买代理',
    },
    {
      key: '/dashboard/transactions',
      icon: <HistoryOutlined />,
      label: '交易记录',
    },
    {
      key: '/dashboard/api',
      icon: <ApiOutlined />,
      label: 'API 管理',
    },
    {
      key: '/dashboard/billing',
      icon: <WalletOutlined />,
      label: '账单管理',
    },
    {
      key: '/dashboard/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => router.push('/dashboard/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => router.push('/dashboard/settings'),
    },
    {
      key: 'help',
      icon: <QuestionCircleOutlined />,
      label: '帮助中心',
      onClick: () => window.open('/help', '_blank'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 通知下拉菜单
  const notificationItems = [
    {
      key: '1',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>代理即将到期</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>US-East-1 代理将在3天后到期</div>
          <div style={{ fontSize: '11px', color: '#bfbfbf' }}>2小时前</div>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>购买成功</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>JP-Tokyo HTTP代理购买成功</div>
          <div style={{ fontSize: '11px', color: '#bfbfbf' }}>1天前</div>
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <div style={{ padding: '8px 0' }}>
          <div style={{ fontWeight: 'bold', fontSize: '14px' }}>系统维护通知</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>系统将在今晚进行例行维护</div>
          <div style={{ fontSize: '11px', color: '#bfbfbf' }}>3天前</div>
        </div>
      ),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'all',
      label: (
        <div style={{ textAlign: 'center', padding: '8px 0' }}>
          <Button type="text" size="small">查看全部通知</Button>
        </div>
      ),
    },
  ];

  // 生成面包屑
  const getBreadcrumbItems = () => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbItems = [
      {
        title: (
          <Link href="/">
            <Space>
              <HomeOutlined />
              <span>首页</span>
            </Space>
          </Link>
        ),
      },
    ];

    if (pathSegments.length > 0) {
      breadcrumbItems.push({
        title: (
          <Link href="/dashboard">
            <Space>
              <DashboardOutlined />
              <span>仪表板</span>
            </Space>
          </Link>
        ),
      });
    }

    if (pathSegments.length > 1) {
      const currentPage = pathSegments[pathSegments.length - 1];
      const pageNames: { [key: string]: string } = {
        'proxies': '代理管理',
        'buy': '购买代理',
        'transactions': '交易记录',
        'api': 'API 管理',
        'billing': '账单管理',
        'settings': '系统设置',
        'profile': '个人资料',
      };

      breadcrumbItems.push({
        title: pageNames[currentPage] || currentPage,
      });
    }

    return breadcrumbItems;
  };

  if (!user) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        background: '#f0f2f5'
      }}>
        <div style={{ textAlign: 'center' }}>
          <Avatar 
            size={64} 
            style={{ backgroundColor: token.colorPrimary }}
            icon={<SafetyOutlined />}
          />
          <div style={{ marginTop: '16px', fontSize: '16px' }}>
            正在加载...
          </div>
        </div>
      </div>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: token.colorBgContainer,
          borderRight: `1px solid ${token.colorBorder}`,
        }}
        width={240}
      >
        {/* Logo */}
        <div style={{ 
          padding: '16px',
          textAlign: 'center',
          borderBottom: `1px solid ${token.colorBorder}`
        }}>
          <Link href="/" style={{ textDecoration: 'none' }}>
            <Space direction={collapsed ? 'horizontal' : 'vertical'} size="small">
              <Avatar 
                size={collapsed ? 32 : 48} 
                style={{ backgroundColor: token.colorPrimary }}
                icon={<SafetyOutlined />}
              />
              {!collapsed && (
                <div>
                  <Title level={4} style={{ margin: 0, color: token.colorPrimary }}>
                    ProxyHub
                  </Title>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    企业级代理平台
                  </Text>
                </div>
              )}
            </Space>
          </Link>
        </div>

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[pathname]}
          style={{ borderRight: 0, background: 'transparent' }}
          items={menuItems.map(item => ({
            ...item,
            onClick: () => router.push(item.key),
          }))}
        />
      </Sider>

      <Layout>
        {/* 顶部导航 */}
        <Header style={{ 
          padding: '0 24px',
          background: token.colorBgContainer,
          borderBottom: `1px solid ${token.colorBorder}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px' }}
            />
            
            <Divider type="vertical" />
            
            <Breadcrumb items={getBreadcrumbItems()} />
          </Space>

          <Space size="middle">
            {/* 通知 */}
            <Dropdown
              menu={{ items: notificationItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Badge count={notifications} size="small">
                <Button 
                  type="text" 
                  icon={<BellOutlined />}
                  style={{ fontSize: '16px' }}
                />
              </Badge>
            </Dropdown>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar 
                  size={32}
                  src={user.avatar}
                  style={{ backgroundColor: token.colorPrimary }}
                >
                  {user.username?.charAt(0).toUpperCase()}
                </Avatar>
                <div style={{ display: collapsed ? 'none' : 'block' }}>
                  <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                    {user.username}
                  </div>
                  <div style={{ fontSize: '12px', color: token.colorTextSecondary }}>
                    {user.email}
                  </div>
                </div>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 主要内容区域 */}
        <Content style={{ 
          background: token.colorBgLayout,
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  );
}
