# 部署指南

本文档详细介绍了如何在不同环境中部署代理系统。

## 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+), macOS, Windows
- **Node.js**: 18.0 或更高版本
- **数据库**: PostgreSQL 12 或更高版本
- **内存**: 最少 2GB RAM (推荐 4GB+)
- **存储**: 最少 10GB 可用空间

### 网络要求
- **端口**: 3000 (应用端口), 5432 (数据库端口)
- **域名**: 生产环境需要配置域名和SSL证书
- **防火墙**: 确保必要端口开放

## 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd proxy-system
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件：
```env
DATABASE_URL="postgresql://username:password@localhost:5432/proxy_system"
JWT_SECRET="your-development-jwt-secret"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-development-nextauth-secret"
API_BASE_URL="https://px6.link/api"
NODE_ENV="development"
```

### 4. 数据库设置
```bash
# 创建数据库
createdb proxy_system

# 生成 Prisma 客户端
npx prisma generate

# 推送数据库架构
npx prisma db push
```

### 5. 启动开发服务器
```bash
npm run dev
```

## 生产环境部署

### 方式一：传统部署

#### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装 PM2
sudo npm install -g pm2
```

#### 2. 数据库配置
```bash
# 切换到 postgres 用户
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE proxy_system;
CREATE USER proxy_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE proxy_system TO proxy_user;
\q
```

#### 3. 应用部署
```bash
# 克隆项目
git clone <repository-url>
cd proxy-system

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件

# 构建应用
npm run build

# 生成 Prisma 客户端
npx prisma generate

# 推送数据库架构
npx prisma db push

# 使用 PM2 启动应用
pm2 start npm --name "proxy-system" -- start
pm2 save
pm2 startup
```

#### 4. Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 5. SSL 证书配置
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 方式二：Docker 部署

#### 1. 创建 Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### 2. 创建 docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=***********************************************/proxy_system
      - JWT_SECRET=your-production-jwt-secret
      - NEXTAUTH_URL=https://your-domain.com
      - NEXTAUTH_SECRET=your-production-nextauth-secret
      - API_BASE_URL=https://px6.link/api
      - NODE_ENV=production
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=proxy_system
      - POSTGRES_USER=proxy_user
      - POSTGRES_PASSWORD=secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. 部署命令
```bash
# 构建并启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方式三：云平台部署

#### Vercel 部署

1. **准备工作**
```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login
```

2. **配置环境变量**
在 Vercel 控制台中配置以下环境变量：
- `DATABASE_URL`
- `JWT_SECRET`
- `NEXTAUTH_SECRET`
- `API_BASE_URL`

3. **部署**
```bash
# 部署到 Vercel
vercel --prod
```

#### Railway 部署

1. **连接 GitHub 仓库**
2. **配置环境变量**
3. **自动部署**

#### DigitalOcean App Platform 部署

1. **创建应用**
2. **连接 GitHub 仓库**
3. **配置环境变量**
4. **部署应用**

## 环境变量配置

### 生产环境必需变量
```env
# 数据库配置
DATABASE_URL="postgresql://user:password@host:port/database"

# 安全密钥 (必须更改)
JWT_SECRET="your-super-secure-jwt-secret-key"
NEXTAUTH_SECRET="your-super-secure-nextauth-secret-key"

# 应用配置
NEXTAUTH_URL="https://your-domain.com"
API_BASE_URL="https://px6.link/api"
NODE_ENV="production"
```

### 可选变量
```env
# 日志级别
LOG_LEVEL="info"

# 数据库连接池
DATABASE_POOL_SIZE="10"

# Redis 缓存 (可选)
REDIS_URL="redis://localhost:6379"

# 邮件服务 (可选)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
```

## 数据库迁移

### 生产环境迁移
```bash
# 备份数据库
pg_dump -h localhost -U proxy_user proxy_system > backup.sql

# 运行迁移
npx prisma db push

# 如果需要回滚
psql -h localhost -U proxy_user proxy_system < backup.sql
```

### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_proxies_user_id ON proxies(user_id);
CREATE INDEX idx_proxies_expiry_date ON proxies(expiry_date);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_api_logs_created_at ON api_logs(created_at);

-- 定期清理日志
DELETE FROM api_logs WHERE created_at < NOW() - INTERVAL '30 days';
```

## 监控和日志

### PM2 监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs proxy-system

# 监控面板
pm2 monit

# 重启应用
pm2 restart proxy-system
```

### 日志配置
```javascript
// next.config.js
module.exports = {
  experimental: {
    logging: {
      level: 'info',
    },
  },
}
```

### 健康检查
创建健康检查端点：
```javascript
// pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({ 
    status: 'ok', 
    timestamp: new Date().toISOString() 
  });
}
```

## 性能优化

### 应用优化
```javascript
// next.config.js
module.exports = {
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  experimental: {
    optimizeCss: true,
  },
}
```

### 数据库优化
```sql
-- 配置 PostgreSQL
shared_preload_libraries = 'pg_stat_statements'
max_connections = 100
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
```

## 安全配置

### 防火墙设置
```bash
# UFW 配置
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 应用安全
```javascript
// 安全头配置
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  }
];
```

## 故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库状态
sudo systemctl status postgresql

# 检查连接
psql -h localhost -U proxy_user -d proxy_system
```

2. **应用启动失败**
```bash
# 检查日志
pm2 logs proxy-system

# 检查端口占用
netstat -tulpn | grep :3000
```

3. **内存不足**
```bash
# 检查内存使用
free -h

# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 备份策略

#### 数据库备份
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U proxy_user proxy_system > backup_$DATE.sql
gzip backup_$DATE.sql

# 保留最近7天的备份
find . -name "backup_*.sql.gz" -mtime +7 -delete
```

#### 应用备份
```bash
#!/bin/bash
# 备份应用文件
tar -czf app_backup_$(date +%Y%m%d).tar.gz /path/to/proxy-system

# 备份到远程服务器
rsync -avz /path/to/proxy-system user@backup-server:/backups/
```

## 更新和维护

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm ci

# 重新构建
npm run build

# 重启应用
pm2 restart proxy-system
```

### 系统维护
```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# 清理日志
sudo journalctl --vacuum-time=7d

# 清理包缓存
sudo apt autoremove && sudo apt autoclean
```
