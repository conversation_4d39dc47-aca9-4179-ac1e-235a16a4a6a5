import { NextRequest, NextResponse } from 'next/server';
import { getUser<PERSON>romToken, getUserFromApiKey, logApiCall } from '@/lib/auth';
import { ProxyAPIClient } from '@/lib/api-client';
import { getCountryName } from '@/lib/utils';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 认证
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user || !user.apiKey) {
      await logApiCall(
        null,
        'GET',
        '/api/proxy/countries',
        Object.fromEntries(request.nextUrl.searchParams),
        { error: '未授权访问或未设置API密钥' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问或未设置API密钥' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const version = request.nextUrl.searchParams.get('version') || '6';

    // 创建API客户端
    const proxyClient = new ProxyAPIClient(user.apiKey);

    // 获取可用国家列表
    const countryInfo = await proxyClient.getCountry({
      version: version as any,
    });

    // 为每个国家添加中文名称和可用数量
    const countriesWithInfo = await Promise.all(
      countryInfo.list.map(async (countryCode) => {
        try {
          const countInfo = await proxyClient.getCount({
            country: countryCode,
            version: version as any,
          });
          
          return {
            code: countryCode,
            name: getCountryName(countryCode),
            availableCount: countInfo.count,
          };
        } catch (error) {
          return {
            code: countryCode,
            name: getCountryName(countryCode),
            availableCount: 0,
          };
        }
      })
    );

    // 按可用数量排序
    countriesWithInfo.sort((a, b) => b.availableCount - a.availableCount);

    const response = {
      version,
      countries: countriesWithInfo,
      totalCountries: countriesWithInfo.length,
    };

    await logApiCall(
      user.id,
      'GET',
      '/api/proxy/countries',
      { version },
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get countries error:', error);
    
    let errorMessage = '获取国家列表失败';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API Error')) {
        errorMessage = error.message;
        statusCode = 400;
      }
    }

    await logApiCall(
      user?.id || null,
      'GET',
      '/api/proxy/countries',
      Object.fromEntries(request.nextUrl.searchParams),
      { error: errorMessage },
      statusCode,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
