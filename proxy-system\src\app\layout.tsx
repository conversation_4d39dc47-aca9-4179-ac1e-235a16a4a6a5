import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "代理系统 - Proxy Management System",
  description: "专业的代理服务器管理系统，支持IPv4/IPv6代理购买、管理和监控",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="min-h-screen bg-background font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
