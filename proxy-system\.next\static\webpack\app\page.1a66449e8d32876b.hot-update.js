"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 52\n            }, this),\n            color: '#1890ff'\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 56\n            }, this),\n            color: '#52c41a'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 53\n            }, this),\n            color: '#faad14'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 43\n            }, this),\n            color: '#722ed1'\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#1890ff'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',\n            highlight: 'HTTP/HTTPS & SOCKS5'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#52c41a'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            title: '全球节点覆盖',\n            description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',\n            highlight: '50+ 国家地区'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#faad14'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            title: '极速稳定',\n            description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',\n            highlight: '99.9% 稳定性'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#722ed1'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',\n            highlight: '军用级加密'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#eb2f96'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this),\n            title: '实时监控',\n            description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',\n            highlight: '实时监控面板'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#13c2c2'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, this),\n            title: 'API集成',\n            description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',\n            highlight: 'RESTful API'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 16px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1200px',\n                            margin: '0 auto',\n                            height: '64px'\n                        },\n                        className: \"jsx-24d3f5d8be7854ab\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '12px',\n                                        minWidth: 0\n                                    },\n                                    className: \"jsx-24d3f5d8be7854ab\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 40,\n                                            style: {\n                                                backgroundColor: '#2563eb',\n                                                flexShrink: 0\n                                            },\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: 0\n                                            },\n                                            className: \"jsx-24d3f5d8be7854ab\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 4,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#2563eb',\n                                                        fontSize: '18px',\n                                                        lineHeight: '1.2',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    style: {\n                                                        fontSize: '11px',\n                                                        lineHeight: '1',\n                                                        display: 'block',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"企业级代理服务平台\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"帮助文档\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e5e7eb'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#374151',\n                                                    fontSize: '15px'\n                                                },\n                                                onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                                onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: '#2563eb',\n                                                    borderColor: '#2563eb',\n                                                    borderRadius: '8px',\n                                                    height: '40px',\n                                                    paddingLeft: '20px',\n                                                    paddingRight: '20px'\n                                                },\n                                                children: \"免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"注册\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"24d3f5d8be7854ab\",\n                        children: \"@media(min-width:768px){.desktop-nav.jsx-24d3f5d8be7854ab{display:block!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:none!important}}@media(max-width:767px){.desktop-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}@media(max-width:480px){.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 32,\n                            style: {\n                                backgroundColor: '#2563eb'\n                            },\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 21\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: '#2563eb',\n                                fontWeight: 'bold'\n                            },\n                            children: \"ProxyHub\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 280,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '8px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"产品特性\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"价格方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"帮助文档\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            style: {\n                                margin: '16px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#374151'\n                                },\n                                children: \"登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: '#2563eb',\n                                    borderColor: '#2563eb'\n                                },\n                                children: \"免费注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',\n                            padding: '120px 24px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.4\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '12px',\n                                                background: 'rgba(255,255,255,0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255,255,255,0.2)',\n                                                borderRadius: '50px',\n                                                padding: '8px 24px',\n                                                marginBottom: '24px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        borderRadius: '50%',\n                                                        background: '#10b981',\n                                                        boxShadow: '0 0 8px #10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.9)',\n                                                        fontSize: '14px',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"已服务 100,000+ 企业用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(2.5rem, 6vw, 4rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '700',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.02em'\n                                        },\n                                        children: [\n                                            \"全球代理服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#3b82f6',\n                                                    fontWeight: '700'\n                                                },\n                                                children: \"企业级解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                                            color: 'rgba(255,255,255,0.9)',\n                                            marginBottom: '48px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 48px auto',\n                                            lineHeight: '1.6',\n                                            textShadow: '0 1px 2px rgba(0,0,0,0.2)'\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDD25 提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 52\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"⚡ 99.9%稳定性保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83C\\uDF0D 50+国家覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83D\\uDEE1️ 企业级安全\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        style: {\n                                            marginBottom: '64px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        fontWeight: 'bold',\n                                                        background: 'linear-gradient(45deg, #1890ff, #36cfc9)',\n                                                        border: 'none',\n                                                        boxShadow: '0 4px 15px rgba(24, 144, 255, 0.4)'\n                                                    },\n                                                    children: \"立即免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        background: 'rgba(255,255,255,0.1)',\n                                                        border: '2px solid rgba(255,255,255,0.3)',\n                                                        color: '#fff',\n                                                        backdropFilter: 'blur(10px)'\n                                                    },\n                                                    children: \"观看演示\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        gutter: [\n                                            24,\n                                            24\n                                        ],\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    style: {\n                                                        textAlign: 'center',\n                                                        background: 'rgba(255,255,255,0.95)',\n                                                        backdropFilter: 'blur(10px)',\n                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                        borderRadius: '16px',\n                                                        boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n                                                        transition: 'all 0.3s ease',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    hoverable: true,\n                                                    bodyStyle: {\n                                                        padding: '24px 16px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '12px'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontSize: '2rem',\n                                                                    color: stat.color,\n                                                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                                                },\n                                                                children: stat.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                style: {\n                                                                    color: '#666',\n                                                                    fontSize: '14px',\n                                                                    fontWeight: '500'\n                                                                },\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            value: stat.value,\n                                                            suffix: stat.suffix,\n                                                            valueStyle: {\n                                                                color: stat.color,\n                                                                fontWeight: 'bold',\n                                                                fontSize: '24px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 565,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 634,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 670,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 713,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 703,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 762,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 784,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 788,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 761,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 756,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});