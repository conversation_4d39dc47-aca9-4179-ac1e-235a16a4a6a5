// shadcn/ui 迁移示例
// 展示如何从内联样式迁移到 shadcn/ui 组件

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'

// ============================================================================
// 示例 1: 登录表单迁移
// ============================================================================

// 迁移前 - 内联样式版本
export function LoginFormBefore() {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '0.75rem',
      padding: '2rem',
      boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
      border: '1px solid #e5e7eb',
      maxWidth: '400px',
      margin: '0 auto'
    }}>
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <h1 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: '#111827',
          margin: '0 0 0.5rem 0'
        }}>
          用户登录
        </h1>
        <p style={{
          color: '#6b7280',
          fontSize: '0.875rem',
          margin: 0
        }}>
          请输入您的账户信息登录系统
        </p>
      </div>

      <form style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <label style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151'
          }}>
            邮箱地址
          </label>
          <input
            type="email"
            placeholder="请输入邮箱地址"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          <label style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151'
          }}>
            密码
          </label>
          <input
            type="password"
            placeholder="请输入密码"
            style={{
              width: '100%',
              padding: '0.75rem',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>

        <button
          type="submit"
          style={{
            width: '100%',
            padding: '0.75rem',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '0.375rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            cursor: 'pointer'
          }}
        >
          登录
        </button>
      </form>
    </div>
  )
}

// 迁移后 - shadcn/ui 版本
export function LoginFormAfter() {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">用户登录</CardTitle>
        <CardDescription>
          请输入您的账户信息登录系统
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">邮箱地址</Label>
            <Input
              id="email"
              type="email"
              placeholder="请输入邮箱地址"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">密码</Label>
            <Input
              id="password"
              type="password"
              placeholder="请输入密码"
            />
          </div>
          <Button type="submit" className="w-full">
            登录
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

// ============================================================================
// 示例 2: 统计卡片迁移
// ============================================================================

// 迁移前 - 内联样式版本
export function StatsCardBefore() {
  return (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '0.75rem',
      padding: '1.5rem',
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      border: '1px solid #e5e7eb'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '0.5rem'
      }}>
        <h3 style={{
          fontSize: '0.875rem',
          fontWeight: '500',
          color: '#6b7280',
          margin: 0
        }}>
          总代理数
        </h3>
        <span style={{ fontSize: '1.25rem' }}>🖥️</span>
      </div>
      <div style={{
        fontSize: '1.5rem',
        fontWeight: 'bold',
        color: '#111827'
      }}>
        25
      </div>
      <p style={{
        fontSize: '0.75rem',
        color: '#6b7280',
        margin: '0.25rem 0 0 0'
      }}>
        活跃: 20 | 过期: 2
      </p>
    </div>
  )
}

// 迁移后 - shadcn/ui 版本
export function StatsCardAfter() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">总代理数</CardTitle>
        <span className="text-xl">🖥️</span>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">25</div>
        <p className="text-xs text-muted-foreground">
          活跃: 20 | 过期: 2
        </p>
      </CardContent>
    </Card>
  )
}

// ============================================================================
// 示例 3: 状态徽章迁移
// ============================================================================

// 迁移前 - 内联样式版本
export function StatusBadgeBefore({ status }: { status: 'active' | 'expired' | 'expiring' }) {
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'active':
        return {
          backgroundColor: '#dcfce7',
          color: '#166534',
          border: '1px solid #bbf7d0'
        }
      case 'expired':
        return {
          backgroundColor: '#fef2f2',
          color: '#991b1b',
          border: '1px solid #fecaca'
        }
      case 'expiring':
        return {
          backgroundColor: '#fef3c7',
          color: '#92400e',
          border: '1px solid #fbbf24'
        }
      default:
        return {
          backgroundColor: '#f3f4f6',
          color: '#374151',
          border: '1px solid #d1d5db'
        }
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常'
      case 'expired': return '已过期'
      case 'expiring': return '即将到期'
      default: return '未知'
    }
  }

  return (
    <span style={{
      ...getStatusStyle(status),
      padding: '0.25rem 0.75rem',
      borderRadius: '9999px',
      fontSize: '0.75rem',
      fontWeight: '500'
    }}>
      {getStatusText(status)}
    </span>
  )
}

// 迁移后 - shadcn/ui 版本
export function StatusBadgeAfter({ status }: { status: 'active' | 'expired' | 'expiring' }) {
  const getVariant = (status: string) => {
    switch (status) {
      case 'active': return 'default'
      case 'expired': return 'destructive'
      case 'expiring': return 'secondary'
      default: return 'outline'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常'
      case 'expired': return '已过期'
      case 'expiring': return '即将到期'
      default: return '未知'
    }
  }

  return (
    <Badge variant={getVariant(status) as any}>
      {getStatusText(status)}
    </Badge>
  )
}

// ============================================================================
// 示例 4: 警告提示迁移
// ============================================================================

// 迁移前 - 内联样式版本
export function AlertBefore({ message }: { message: string }) {
  return (
    <div style={{
      padding: '0.75rem',
      backgroundColor: '#fef3c7',
      border: '1px solid #fbbf24',
      borderRadius: '0.375rem',
      display: 'flex',
      alignItems: 'center',
      gap: '0.5rem'
    }}>
      <span style={{ fontSize: '1rem' }}>⚠️</span>
      <span style={{
        fontSize: '0.875rem',
        color: '#92400e'
      }}>
        {message}
      </span>
    </div>
  )
}

// 迁移后 - shadcn/ui 版本
export function AlertAfter({ message }: { message: string }) {
  return (
    <Alert>
      <span className="text-base">⚠️</span>
      <AlertDescription>
        {message}
      </AlertDescription>
    </Alert>
  )
}

// ============================================================================
// 使用示例
// ============================================================================

export default function MigrationExample() {
  return (
    <div className="space-y-8 p-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">shadcn/ui 迁移示例</h2>
        <p className="text-gray-600 mb-8">
          以下展示了从内联样式迁移到 shadcn/ui 组件的对比示例
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-lg font-semibold mb-4">迁移前 (内联样式)</h3>
          <div className="space-y-4">
            <LoginFormBefore />
            <StatsCardBefore />
            <StatusBadgeBefore status="active" />
            <AlertBefore message="3 个代理即将到期" />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">迁移后 (shadcn/ui)</h3>
          <div className="space-y-4">
            <LoginFormAfter />
            <StatsCardAfter />
            <StatusBadgeAfter status="active" />
            <AlertAfter message="3 个代理即将到期" />
          </div>
        </div>
      </div>
    </div>
  )
}
