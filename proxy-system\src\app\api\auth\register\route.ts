import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { hashPassword, generateToken, generateApiKey } from '@/lib/auth';
import { isValidEmail, validatePassword } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const { email, username, password } = await request.json();

    // 验证输入
    if (!email || !username || !password) {
      return NextResponse.json(
        { error: '邮箱、用户名和密码都是必填项' },
        { status: 400 }
      );
    }

    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: '邮箱格式不正确' },
        { status: 400 }
      );
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: passwordValidation.errors.join(', ') },
        { status: 400 }
      );
    }

    // 检查用户是否已存在
    const existingUser = await db.user.findFirst({
      where: {
        OR: [
          { email },
          { username },
        ],
      },
    });

    if (existingUser) {
      if (existingUser.email === email) {
        return NextResponse.json(
          { error: '该邮箱已被注册' },
          { status: 409 }
        );
      } else {
        return NextResponse.json(
          { error: '该用户名已被使用' },
          { status: 409 }
        );
      }
    }

    // 创建用户
    const hashedPassword = await hashPassword(password);
    const apiKey = generateApiKey();

    const user = await db.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        apiKey,
      },
      select: {
        id: true,
        email: true,
        username: true,
        apiKey: true,
        balance: true,
        currency: true,
        createdAt: true,
      },
    });

    // 生成JWT令牌
    const token = generateToken({
      userId: user.id,
      email: user.email,
      username: user.username,
    });

    return NextResponse.json({
      message: '注册成功',
      user,
      token,
    });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
