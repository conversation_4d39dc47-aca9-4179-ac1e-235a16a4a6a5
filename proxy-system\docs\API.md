# API 文档

代理系统提供完整的 RESTful API，支持用户认证、代理管理、交易记录等功能。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: Bearer <PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 认证

### 获取访问令牌

所有需要认证的 API 请求都需要在请求头中包含访问令牌：

```http
Authorization: Bearer <your-jwt-token>
```

## 错误响应

API 使用标准的 HTTP 状态码来表示请求结果：

- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 禁止访问
- `404` - 资源不存在
- `429` - 请求频率超限
- `500` - 服务器内部错误

错误响应格式：
```json
{
  "error": "错误描述信息"
}
```

## 用户认证 API

### 用户注册

创建新用户账户。

**请求**
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123"
}
```

**响应**
```json
{
  "message": "注册成功",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "apiKey": "generated-api-key",
    "balance": 0,
    "currency": "RUB",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "token": "jwt-token"
}
```

### 用户登录

使用邮箱/用户名和密码登录。

**请求**
```http
POST /api/auth/login
Content-Type: application/json

{
  "login": "<EMAIL>",
  "password": "password123"
}
```

**响应**
```json
{
  "message": "登录成功",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "apiKey": "api-key",
    "balance": 100.50,
    "currency": "RUB",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "token": "jwt-token"
}
```

### 获取用户信息

获取当前登录用户的信息。

**请求**
```http
GET /api/auth/me
Authorization: Bearer <token>
```

**响应**
```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "apiKey": "api-key",
    "balance": 100.50,
    "currency": "RUB",
    "isActive": true
  }
}
```

### API 密钥管理

#### 获取 API 密钥

**请求**
```http
GET /api/auth/api-key
Authorization: Bearer <token>
```

**响应**
```json
{
  "apiKey": "current-api-key"
}
```

#### 重新生成 API 密钥

**请求**
```http
POST /api/auth/api-key
Authorization: Bearer <token>
```

**响应**
```json
{
  "message": "API密钥已重新生成",
  "apiKey": "new-api-key"
}
```

## 代理管理 API

### 获取代理列表

获取用户的代理列表，支持分页和筛选。

**请求**
```http
GET /api/proxy/list?page=1&limit=20&status=active&country=ru&type=http
Authorization: Bearer <token>
```

**查询参数**
- `page` (可选) - 页码，默认为 1
- `limit` (可选) - 每页数量，默认为 20
- `status` (可选) - 代理状态：`active`, `expired`, `expiring`, `all`
- `country` (可选) - 国家代码筛选
- `type` (可选) - 代理类型：`http`, `socks`

**响应**
```json
{
  "proxies": [
    {
      "id": "proxy-id",
      "externalId": "external-id",
      "ip": "***********",
      "host": "proxy.example.com",
      "port": "8080",
      "username": "proxy-user",
      "password": "proxy-pass",
      "type": "http",
      "version": "6",
      "country": "ru",
      "description": "测试代理",
      "isActive": true,
      "purchaseDate": "2024-01-01T00:00:00.000Z",
      "expiryDate": "2024-02-01T00:00:00.000Z",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

### 购买代理

购买新的代理服务器。

**请求**
```http
POST /api/proxy/buy
Authorization: Bearer <token>
Content-Type: application/json

{
  "count": 5,
  "period": 30,
  "country": "ru",
  "version": "6",
  "type": "http",
  "description": "测试代理"
}
```

**请求参数**
- `count` (必需) - 代理数量 (1-1000)
- `period` (必需) - 使用期限，天数 (1-365)
- `country` (必需) - 国家代码
- `version` (可选) - 代理版本：`4`, `6`，默认为 `6`
- `type` (可选) - 代理类型：`http`, `socks`，默认为 `http`
- `description` (可选) - 代理描述

**响应**
```json
{
  "message": "代理购买成功",
  "transaction": {
    "id": "transaction-id",
    "type": "buy",
    "amount": -150.00,
    "currency": "RUB",
    "description": "购买 5 个 ru 代理，期限 30 天",
    "status": "completed",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "proxies": [
    {
      "id": "proxy-id",
      "externalId": "external-id",
      "ip": "***********",
      "host": "proxy.example.com",
      "port": "8080",
      "username": "proxy-user",
      "password": "proxy-pass",
      "type": "http",
      "version": "6",
      "country": "ru",
      "description": "测试代理",
      "purchaseDate": "2024-01-01T00:00:00.000Z",
      "expiryDate": "2024-02-01T00:00:00.000Z"
    }
  ],
  "totalCost": 150.00,
  "remainingBalance": 50.00
}
```

### 延期代理

延长代理的使用期限。

**请求**
```http
POST /api/proxy/prolong
Authorization: Bearer <token>
Content-Type: application/json

{
  "proxyIds": ["proxy-id-1", "proxy-id-2"],
  "period": 30
}
```

**请求参数**
- `proxyIds` (必需) - 要延期的代理ID数组
- `period` (必需) - 延期天数 (1-365)

**响应**
```json
{
  "message": "代理延期成功",
  "transaction": {
    "id": "transaction-id",
    "type": "prolong",
    "amount": -60.00,
    "currency": "RUB",
    "description": "延期 2 个代理，期限 30 天",
    "status": "completed",
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "proxies": [
    {
      "id": "proxy-id-1",
      "expiryDate": "2024-03-01T00:00:00.000Z"
    },
    {
      "id": "proxy-id-2",
      "expiryDate": "2024-03-01T00:00:00.000Z"
    }
  ],
  "totalCost": 60.00,
  "remainingBalance": 40.00
}
```

### 删除代理

删除指定的代理。

**请求**
```http
DELETE /api/proxy/delete
Authorization: Bearer <token>
Content-Type: application/json

{
  "proxyIds": ["proxy-id-1", "proxy-id-2"]
}
```

**请求参数**
- `proxyIds` (必需) - 要删除的代理ID数组

**响应**
```json
{
  "message": "成功删除 2 个代理",
  "deletedCount": 2,
  "transaction": {
    "id": "transaction-id",
    "type": "refund",
    "amount": 0,
    "currency": "RUB",
    "description": "删除 2 个代理",
    "status": "completed",
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

## 查询 API

### 获取价格信息

获取指定配置的代理价格。

**请求**
```http
GET /api/proxy/price?count=5&period=30&version=6
Authorization: Bearer <token>
```

**查询参数**
- `count` (必需) - 代理数量 (1-1000)
- `period` (必需) - 使用期限，天数 (1-365)
- `version` (可选) - 代理版本：`4`, `6`，默认为 `6`

**响应**
```json
{
  "count": 5,
  "period": 30,
  "version": "6",
  "totalPrice": 150.00,
  "pricePerProxy": 30.00,
  "currency": "RUB",
  "userBalance": 200.00,
  "canAfford": true
}
```

### 获取可用国家

获取可用的代理国家列表。

**请求**
```http
GET /api/proxy/countries?version=6
Authorization: Bearer <token>
```

**查询参数**
- `version` (可选) - 代理版本：`4`, `6`，默认为 `6`

**响应**
```json
{
  "version": "6",
  "countries": [
    {
      "code": "ru",
      "name": "俄罗斯",
      "availableCount": 1000
    },
    {
      "code": "us",
      "name": "美国",
      "availableCount": 800
    }
  ],
  "totalCountries": 2
}
```

## 速率限制

API 实施速率限制以防止滥用：

- **认证接口**: 每分钟最多 10 次请求
- **代理管理接口**: 每分钟最多 60 次请求
- **查询接口**: 每分钟最多 100 次请求

当超过速率限制时，API 将返回 `429 Too Many Requests` 状态码。

## 示例代码

### JavaScript/Node.js

```javascript
const API_BASE = 'http://localhost:3000/api';
const token = 'your-jwt-token';

// 获取代理列表
async function getProxies() {
  const response = await fetch(`${API_BASE}/proxy/list`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  return data;
}

// 购买代理
async function buyProxies(config) {
  const response = await fetch(`${API_BASE}/proxy/buy`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
  });
  
  const data = await response.json();
  return data;
}
```

### Python

```python
import requests

API_BASE = 'http://localhost:3000/api'
token = 'your-jwt-token'

headers = {
    'Authorization': f'Bearer {token}',
    'Content-Type': 'application/json'
}

# 获取代理列表
def get_proxies():
    response = requests.get(f'{API_BASE}/proxy/list', headers=headers)
    return response.json()

# 购买代理
def buy_proxies(config):
    response = requests.post(f'{API_BASE}/proxy/buy', 
                           headers=headers, 
                           json=config)
    return response.json()
```

### cURL

```bash
# 用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"login":"<EMAIL>","password":"password123"}'

# 获取代理列表
curl -X GET http://localhost:3000/api/proxy/list \
  -H "Authorization: Bearer your-jwt-token"

# 购买代理
curl -X POST http://localhost:3000/api/proxy/buy \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{"count":5,"period":30,"country":"ru","version":"6","type":"http"}'
```
