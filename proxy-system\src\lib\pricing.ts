// 代理商定价配置
export interface PricingConfig {
  basePrice: number; // 基础价格（每个代理每天）
  markup: number; // 加价比例（例如 0.3 表示30%加价）
  minimumOrder: number; // 最小订购数量
  discounts: {
    quantity: number; // 数量阈值
    discount: number; // 折扣比例
  }[];
  vipDiscounts: {
    level: 'basic' | 'pro' | 'enterprise';
    discount: number;
  }[];
}

// 默认定价配置
export const defaultPricingConfig: PricingConfig = {
  basePrice: 2.5, // 每个代理每天2.5元
  markup: 0.4, // 40%加价
  minimumOrder: 1,
  discounts: [
    { quantity: 10, discount: 0.05 }, // 10个以上5%折扣
    { quantity: 50, discount: 0.1 },  // 50个以上10%折扣
    { quantity: 100, discount: 0.15 }, // 100个以上15%折扣
  ],
  vipDiscounts: [
    { level: 'basic', discount: 0 },
    { level: 'pro', discount: 0.05 },
    { level: 'enterprise', discount: 0.1 },
  ],
};

// 计算代理价格
export function calculateProxyPrice(
  count: number,
  period: number,
  version: '4' | '6' = '6',
  userLevel: 'basic' | 'pro' | 'enterprise' = 'basic',
  config: PricingConfig = defaultPricingConfig
): {
  basePrice: number;
  totalPrice: number;
  pricePerProxy: number;
  discount: number;
  finalPrice: number;
} {
  // 基础价格计算
  let basePrice = config.basePrice;
  
  // IPv4 代理价格更高
  if (version === '4') {
    basePrice *= 1.2;
  }
  
  // 总基础价格
  const totalBasePrice = basePrice * count * period;
  
  // 加价后价格
  const totalPrice = totalBasePrice * (1 + config.markup);
  
  // 计算数量折扣
  let quantityDiscount = 0;
  for (const discount of config.discounts) {
    if (count >= discount.quantity) {
      quantityDiscount = discount.discount;
    }
  }
  
  // 计算VIP折扣
  const vipDiscount = config.vipDiscounts.find(d => d.level === userLevel)?.discount || 0;
  
  // 总折扣
  const totalDiscount = quantityDiscount + vipDiscount;
  
  // 最终价格
  const finalPrice = totalPrice * (1 - totalDiscount);
  
  return {
    basePrice: totalBasePrice,
    totalPrice,
    pricePerProxy: totalPrice / count,
    discount: totalDiscount,
    finalPrice,
  };
}

// 获取推荐套餐
export function getRecommendedPackages(): {
  name: string;
  count: number;
  period: number;
  description: string;
  popular?: boolean;
}[] {
  return [
    {
      name: '入门套餐',
      count: 5,
      period: 30,
      description: '适合个人用户和小型项目',
    },
    {
      name: '专业套餐',
      count: 20,
      period: 30,
      description: '适合中小企业和开发团队',
      popular: true,
    },
    {
      name: '企业套餐',
      count: 100,
      period: 30,
      description: '适合大型企业和高并发场景',
    },
    {
      name: '长期优惠',
      count: 10,
      period: 90,
      description: '3个月套餐，享受长期优惠',
    },
  ];
}

// 验证订单
export function validateOrder(count: number, period: number): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (count < 1) {
    errors.push('代理数量不能少于1个');
  }
  
  if (count > 1000) {
    errors.push('单次购买数量不能超过1000个');
  }
  
  if (period < 1) {
    errors.push('使用期限不能少于1天');
  }
  
  if (period > 365) {
    errors.push('使用期限不能超过365天');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 格式化价格显示
export function formatPrice(price: number, currency: string = '¥'): string {
  return `${currency}${price.toFixed(2)}`;
}

// 计算节省金额
export function calculateSavings(originalPrice: number, finalPrice: number): number {
  return originalPrice - finalPrice;
}

// 获取折扣描述
export function getDiscountDescription(discount: number): string {
  if (discount === 0) return '';
  
  const percentage = Math.round(discount * 100);
  return `享受${percentage}%折扣`;
}
