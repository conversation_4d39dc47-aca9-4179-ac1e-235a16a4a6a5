// API响应基础类型
export interface BaseResponse {
  status: 'yes' | 'no';
  user_id?: string;
  balance?: string;
  currency?: 'RUB' | 'USD';
}

// 成功响应类型
export interface SuccessResponse extends BaseResponse {
  status: 'yes';
  user_id: string;
  balance: string;
  currency: 'RUB' | 'USD';
}

// 错误响应类型
export interface ErrorResponse extends BaseResponse {
  status: 'no';
  error_id: number;
  error: string;
}

// 代理版本类型
export type ProxyVersion = '4' | '3' | '6';

// 代理类型
export type ProxyType = 'http' | 'socks';

// 代理状态
export type ProxyState = 'active' | 'expired' | 'expiring' | 'all';

// 代理信息
export interface ProxyInfo {
  id: string;
  ip: string;
  host: string;
  port: string;
  user: string;
  pass: string;
  type: ProxyType;
  country: string;
  date: string;
  date_end: string;
  unixtime: number;
  unixtime_end: number;
  descr: string;
  active: '0' | '1';
}

// 获取价格响应
export interface GetPriceResponse extends SuccessResponse {
  price: number;
  price_single: number;
  period: number;
  count: number;
}

// 获取数量响应
export interface GetCountResponse extends SuccessResponse {
  count: number;
}

// 获取国家列表响应
export interface GetCountryResponse extends SuccessResponse {
  list: string[];
}

// 获取代理列表响应
export interface GetProxyResponse extends SuccessResponse {
  list_count: number;
  list: Record<string, ProxyInfo>;
}

// 购买代理响应
export interface BuyProxyResponse extends SuccessResponse {
  count: number;
  price: number;
  price_single?: number;
  period: number;
  country: string;
  list: Record<string, ProxyInfo>;
}

// 延期代理响应
export interface ProlongProxyResponse extends SuccessResponse {
  price: number;
  price_single?: number;
  period: number;
  count: number;
  list: Record<string, {
    id: number;
    date_end: string;
    unixtime_end: number;
  }>;
}

// 删除代理响应
export interface DeleteProxyResponse extends SuccessResponse {
  count: number;
}

// 设置描述响应
export interface SetDescrResponse extends SuccessResponse {
  count: number;
}

// 检查代理响应
export interface CheckProxyResponse extends SuccessResponse {
  proxy_id: number;
  proxy_status: boolean;
}

// API方法参数类型
export interface GetPriceParams {
  count: number;
  period: number;
  version?: ProxyVersion;
}

export interface GetCountParams {
  country: string;
  version?: ProxyVersion;
}

export interface GetCountryParams {
  version?: ProxyVersion;
}

export interface GetProxyParams {
  state?: ProxyState;
  descr?: string;
  nokey?: boolean;
  page?: number;
  limit?: number;
}

export interface SetTypeParams {
  ids: string;
  type: ProxyType;
}

export interface SetDescrParams {
  new: string;
  old?: string;
  ids?: string;
}

export interface BuyProxyParams {
  count: number;
  period: number;
  country: string;
  version?: ProxyVersion;
  type?: ProxyType;
  descr?: string;
  auto_prolong?: boolean;
  nokey?: boolean;
}

export interface ProlongProxyParams {
  period: number;
  ids: string;
  nokey?: boolean;
}

export interface DeleteProxyParams {
  ids?: string;
  descr?: string;
}

export interface CheckProxyParams {
  ids: string;
}

// 错误代码映射
export const ERROR_CODES = {
  30: 'Error unknown',
  100: 'Error key',
  105: 'Error ip',
  110: 'Error method',
  200: 'Error count',
  210: 'Error period',
  220: 'Error country',
  230: 'Error ids',
  240: 'Error version',
  250: 'Error descr',
  260: 'Error type',
  300: 'Error active proxy allow',
  400: 'Error no money',
  404: 'Error not found',
  410: 'Error price'
} as const;

export type ErrorCode = keyof typeof ERROR_CODES;
