"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RightSquareTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RightSquareTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RightSquareTwoTone = function RightSquareTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RightSquareTwoTone.default
  }));
};

/**![right-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDcyOEgxODRWMTg0aDY1NnY2NTZ6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik0xODQgODQwaDY1NlYxODRIMTg0djY1NnptMjE2LTE5Ni45YzAtMTAuMiA0LjktMTkuOSAxMy4yLTI1LjlMNTU4LjYgNTEyIDQxMy4yIDQwNi44Yy04LjMtNi0xMy4yLTE1LjYtMTMuMi0yNS45VjMzNGMwLTYuNSA3LjQtMTAuMyAxMi43LTYuNWwyNDYgMTc4YzQuNCAzLjIgNC40IDkuNyAwIDEyLjlsLTI0NiAxNzhjLTUuMyAzLjktMTIuNy4xLTEyLjctNi40di00Ni45eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNDEyLjcgNjk2LjRsMjQ2LTE3OGM0LjQtMy4yIDQuNC05LjcgMC0xMi45bC0yNDYtMTc4Yy01LjMtMy44LTEyLjcgMC0xMi43IDYuNXY0Ni45YzAgMTAuMyA0LjkgMTkuOSAxMy4yIDI1LjlMNTU4LjYgNTEyIDQxMy4yIDYxNy4yYy04LjMgNi0xMy4yIDE1LjctMTMuMiAyNS45VjY5MGMwIDYuNSA3LjQgMTAuMyAxMi43IDYuNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RightSquareTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RightSquareTwoTone';
}
var _default = exports.default = RefIcon;