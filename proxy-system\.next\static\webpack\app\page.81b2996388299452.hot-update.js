"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 58,\n                columnNumber: 52\n            }, this),\n            color: '#1890ff'\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 59,\n                columnNumber: 56\n            }, this),\n            color: '#52c41a'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 53\n            }, this),\n            color: '#faad14'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 43\n            }, this),\n            color: '#722ed1'\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#1890ff'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 66,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',\n            highlight: 'HTTP/HTTPS & SOCKS5'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#52c41a'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, this),\n            title: '全球节点覆盖',\n            description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',\n            highlight: '50+ 国家地区'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#faad14'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 13\n            }, this),\n            title: '极速稳定',\n            description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',\n            highlight: '99.9% 稳定性'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#722ed1'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',\n            highlight: '军用级加密'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#eb2f96'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, this),\n            title: '实时监控',\n            description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',\n            highlight: '实时监控面板'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#13c2c2'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 13\n            }, this),\n            title: 'API集成',\n            description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',\n            highlight: 'RESTful API'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 16px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1200px',\n                            margin: '0 auto',\n                            height: '64px'\n                        },\n                        className: \"jsx-24d3f5d8be7854ab\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '12px',\n                                        minWidth: 0\n                                    },\n                                    className: \"jsx-24d3f5d8be7854ab\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 40,\n                                            style: {\n                                                backgroundColor: '#2563eb',\n                                                flexShrink: 0\n                                            },\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: 0\n                                            },\n                                            className: \"jsx-24d3f5d8be7854ab\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 4,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#2563eb',\n                                                        fontSize: '18px',\n                                                        lineHeight: '1.2',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    style: {\n                                                        fontSize: '11px',\n                                                        lineHeight: '1',\n                                                        display: 'block',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"企业级代理服务平台\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#374151',\n                                                fontSize: '15px'\n                                            },\n                                            onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                            onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                            children: \"帮助文档\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e5e7eb'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#374151',\n                                                    fontSize: '15px'\n                                                },\n                                                onMouseEnter: (e)=>e.target.style.color = '#2563eb',\n                                                onMouseLeave: (e)=>e.target.style.color = '#374151',\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: '#2563eb',\n                                                    borderColor: '#2563eb',\n                                                    borderRadius: '8px',\n                                                    height: '40px',\n                                                    paddingLeft: '20px',\n                                                    paddingRight: '20px'\n                                                },\n                                                children: \"免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"注册\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"24d3f5d8be7854ab\",\n                        children: \"@media(min-width:768px){.desktop-nav.jsx-24d3f5d8be7854ab{display:block!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:none!important}}@media(max-width:767px){.desktop-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}@media(max-width:480px){.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 32,\n                            style: {\n                                backgroundColor: '#2563eb'\n                            },\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 21\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: '#2563eb',\n                                fontWeight: 'bold'\n                            },\n                            children: \"ProxyHub\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 280,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '8px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"产品特性\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"价格方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"帮助文档\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            style: {\n                                margin: '16px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#374151'\n                                },\n                                children: \"登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: '#2563eb',\n                                    borderColor: '#2563eb'\n                                },\n                                children: \"免费注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',\n                            padding: '120px 24px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.4\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '12px',\n                                                background: 'rgba(255,255,255,0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255,255,255,0.2)',\n                                                borderRadius: '50px',\n                                                padding: '8px 24px',\n                                                marginBottom: '24px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        borderRadius: '50%',\n                                                        background: '#10b981',\n                                                        boxShadow: '0 0 8px #10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.9)',\n                                                        fontSize: '14px',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"已服务 100,000+ 企业用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(2.5rem, 6vw, 4rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '700',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.02em'\n                                        },\n                                        children: [\n                                            \"全球代理服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#3b82f6',\n                                                    fontWeight: '700'\n                                                },\n                                                children: \"企业级解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.1rem, 2.5vw, 1.3rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '48px',\n                                            maxWidth: '700px',\n                                            margin: '0 auto 48px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"提供高质量的全球代理网络，支持 HTTP/HTTPS 和 SOCKS5 协议\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% 稳定性保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"企业级安全\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        fontWeight: '600',\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '12px',\n                                                        paddingLeft: '32px',\n                                                        paddingRight: '32px',\n                                                        boxShadow: '0 8px 25px rgba(59, 130, 246, 0.3)',\n                                                        border: 'none'\n                                                    },\n                                                    children: \"立即免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '56px',\n                                                    fontSize: '16px',\n                                                    fontWeight: '500',\n                                                    background: 'rgba(255,255,255,0.1)',\n                                                    border: '2px solid rgba(255,255,255,0.2)',\n                                                    color: '#ffffff',\n                                                    backdropFilter: 'blur(10px)',\n                                                    borderRadius: '12px',\n                                                    paddingLeft: '32px',\n                                                    paddingRight: '32px'\n                                                },\n                                                children: \"了解更多\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        gutter: [\n                                            32,\n                                            32\n                                        ],\n                                        style: {\n                                            marginBottom: '0'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    style: {\n                                                        textAlign: 'center',\n                                                        background: 'rgba(255,255,255,0.98)',\n                                                        backdropFilter: 'blur(20px)',\n                                                        border: '1px solid rgba(255,255,255,0.3)',\n                                                        borderRadius: '20px',\n                                                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                        cursor: 'default',\n                                                        overflow: 'hidden'\n                                                    },\n                                                    bodyStyle: {\n                                                        padding: '32px 24px'\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(-8px)';\n                                                        e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(0)';\n                                                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '20px',\n                                                                display: 'flex',\n                                                                justifyContent: 'center',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '60px',\n                                                                    height: '60px',\n                                                                    borderRadius: '16px',\n                                                                    background: \"linear-gradient(135deg, \".concat(stat.color, \"15, \").concat(stat.color, \"25)\"),\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    justifyContent: 'center',\n                                                                    border: \"2px solid \".concat(stat.color, \"20\")\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '24px',\n                                                                        color: stat.color\n                                                                    },\n                                                                    children: stat.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                style: {\n                                                                    color: '#64748b',\n                                                                    fontSize: '14px',\n                                                                    fontWeight: '500',\n                                                                    textTransform: 'uppercase',\n                                                                    letterSpacing: '0.5px'\n                                                                },\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            value: stat.value,\n                                                            suffix: stat.suffix,\n                                                            valueStyle: {\n                                                                color: '#1e293b',\n                                                                fontWeight: '700',\n                                                                fontSize: '28px',\n                                                                lineHeight: '1.2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 643,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 638,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 680,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 673,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 698,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 697,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 715,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 721,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 734,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 620,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 767,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 824,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 834,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 807,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 802,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});