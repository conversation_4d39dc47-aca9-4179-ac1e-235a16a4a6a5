import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getUserFromToken, getUserFromApiKey, logApiCall } from '@/lib/auth';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 支持两种认证方式：Bearer Token 或 API Key
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user) {
      await logApiCall(
        null,
        'GET',
        '/api/proxy/list',
        Object.fromEntries(request.nextUrl.searchParams),
        { error: '未授权访问' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const page = parseInt(request.nextUrl.searchParams.get('page') || '1');
    const limit = parseInt(request.nextUrl.searchParams.get('limit') || '20');
    const status = request.nextUrl.searchParams.get('status'); // active, expired, expiring
    const country = request.nextUrl.searchParams.get('country');
    const type = request.nextUrl.searchParams.get('type');

    // 构建查询条件
    const where: any = {
      userId: user.id,
    };

    if (status) {
      const now = new Date();
      switch (status) {
        case 'active':
          where.isActive = true;
          where.expiryDate = { gt: now };
          break;
        case 'expired':
          where.OR = [
            { isActive: false },
            { expiryDate: { lte: now } },
          ];
          break;
        case 'expiring':
          const threeDaysFromNow = new Date();
          threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
          where.isActive = true;
          where.expiryDate = {
            gt: now,
            lte: threeDaysFromNow,
          };
          break;
      }
    }

    if (country) {
      where.country = country;
    }

    if (type) {
      where.type = type;
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询代理列表
    const [proxies, total] = await Promise.all([
      db.proxy.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdAt: 'desc',
        },
      }),
      db.proxy.count({ where }),
    ]);

    const response = {
      proxies,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };

    await logApiCall(
      user.id,
      'GET',
      '/api/proxy/list',
      Object.fromEntries(request.nextUrl.searchParams),
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get proxy list error:', error);
    
    await logApiCall(
      user?.id || null,
      'GET',
      '/api/proxy/list',
      Object.fromEntries(request.nextUrl.searchParams),
      { error: '获取代理列表失败' },
      500,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: '获取代理列表失败' },
      { status: 500 }
    );
  }
}
