"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/SecurityScanOutlined.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/SecurityScanOutlined.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar SecurityScanOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M866.9 169.9L527.1 54.1C523 52.7 517.5 52 512 52s-11 .7-15.1 2.1L157.1 169.9c-8.3 2.8-15.1 12.4-15.1 21.2v482.4c0 8.8 5.7 20.4 12.6 25.9L499.3 968c3.5 2.7 8 4.1 12.6 4.1s9.2-1.4 12.6-4.1l344.7-268.6c6.9-5.4 12.6-17 12.6-25.9V191.1c.2-8.8-6.6-18.3-14.9-21.2zM810 654.3L512 886.5 214 654.3V226.7l298-101.6 298 101.6v427.6zM402.9 528.8l-77.5 77.5a8.03 8.03 0 000 11.3l34 34c3.1 3.1 8.2 3.1 11.3 0l77.5-77.5c55.7 35.1 130.1 28.4 178.6-20.1 56.3-56.3 56.3-147.5 0-203.8-56.3-56.3-147.5-56.3-203.8 0-48.5 48.5-55.2 123-20.1 178.6zm65.4-133.3c31.3-31.3 82-31.3 113.2 0 31.3 31.3 31.3 82 0 113.2-31.3 31.3-82 31.3-113.2 0s-31.3-81.9 0-113.2z\" } }] }, \"name\": \"security-scan\", \"theme\": \"outlined\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SecurityScanOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1NlY3VyaXR5U2Nhbk91dGxpbmVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBLDZCQUE2QixVQUFVLHlCQUF5QixrREFBa0QsaUJBQWlCLDBCQUEwQixvb0JBQW9vQixHQUFHO0FBQ3B5QixpRUFBZSxvQkFBb0IsRUFBQyIsInNvdXJjZXMiOlsiRTpcXOS7o+eggVxcUHJveHlcXHZzY29kZV9wcm94eVxccHJveHktc3lzdGVtXFxub2RlX21vZHVsZXNcXEBhbnQtZGVzaWduXFxpY29ucy1zdmdcXGVzXFxhc25cXFNlY3VyaXR5U2Nhbk91dGxpbmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFNlY3VyaXR5U2Nhbk91dGxpbmVkID0geyBcImljb25cIjogeyBcInRhZ1wiOiBcInN2Z1wiLCBcImF0dHJzXCI6IHsgXCJ2aWV3Qm94XCI6IFwiNjQgNjQgODk2IDg5NlwiLCBcImZvY3VzYWJsZVwiOiBcImZhbHNlXCIgfSwgXCJjaGlsZHJlblwiOiBbeyBcInRhZ1wiOiBcInBhdGhcIiwgXCJhdHRyc1wiOiB7IFwiZFwiOiBcIk04NjYuOSAxNjkuOUw1MjcuMSA1NC4xQzUyMyA1Mi43IDUxNy41IDUyIDUxMiA1MnMtMTEgLjctMTUuMSAyLjFMMTU3LjEgMTY5LjljLTguMyAyLjgtMTUuMSAxMi40LTE1LjEgMjEuMnY0ODIuNGMwIDguOCA1LjcgMjAuNCAxMi42IDI1LjlMNDk5LjMgOTY4YzMuNSAyLjcgOCA0LjEgMTIuNiA0LjFzOS4yLTEuNCAxMi42LTQuMWwzNDQuNy0yNjguNmM2LjktNS40IDEyLjYtMTcgMTIuNi0yNS45VjE5MS4xYy4yLTguOC02LjYtMTguMy0xNC45LTIxLjJ6TTgxMCA2NTQuM0w1MTIgODg2LjUgMjE0IDY1NC4zVjIyNi43bDI5OC0xMDEuNiAyOTggMTAxLjZ2NDI3LjZ6TTQwMi45IDUyOC44bC03Ny41IDc3LjVhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzQgMzRjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGw3Ny41LTc3LjVjNTUuNyAzNS4xIDEzMC4xIDI4LjQgMTc4LjYtMjAuMSA1Ni4zLTU2LjMgNTYuMy0xNDcuNSAwLTIwMy44LTU2LjMtNTYuMy0xNDcuNS01Ni4zLTIwMy44IDAtNDguNSA0OC41LTU1LjIgMTIzLTIwLjEgMTc4LjZ6bTY1LjQtMTMzLjNjMzEuMy0zMS4zIDgyLTMxLjMgMTEzLjIgMCAzMS4zIDMxLjMgMzEuMyA4MiAwIDExMy4yLTMxLjMgMzEuMy04MiAzMS4zLTExMy4yIDBzLTMxLjMtODEuOSAwLTExMy4yelwiIH0gfV0gfSwgXCJuYW1lXCI6IFwic2VjdXJpdHktc2NhblwiLCBcInRoZW1lXCI6IFwib3V0bGluZWRcIiB9O1xuZXhwb3J0IGRlZmF1bHQgU2VjdXJpdHlTY2FuT3V0bGluZWQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/SecurityScanOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_SecurityScanOutlined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/SecurityScanOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/SecurityScanOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nconst SecurityScanOutlined = (props, ref)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _extends({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_SecurityScanOutlined__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }));\n_c = SecurityScanOutlined;\n/**![security-scan](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnpNNDAyLjkgNTI4LjhsLTc3LjUgNzcuNWE4LjAzIDguMDMgMCAwMDAgMTEuM2wzNCAzNGMzLjEgMy4xIDguMiAzLjEgMTEuMyAwbDc3LjUtNzcuNWM1NS43IDM1LjEgMTMwLjEgMjguNCAxNzguNi0yMC4xIDU2LjMtNTYuMyA1Ni4zLTE0Ny41IDAtMjAzLjgtNTYuMy01Ni4zLTE0Ny41LTU2LjMtMjAzLjggMC00OC41IDQ4LjUtNTUuMiAxMjMtMjAuMSAxNzguNnptNjUuNC0xMzMuM2MzMS4zLTMxLjMgODItMzEuMyAxMTMuMiAwIDMxLjMgMzEuMyAzMS4zIDgyIDAgMTEzLjItMzEuMyAzMS4zLTgyIDMxLjMtMTEzLjIgMHMtMzEuMy04MS45IDAtMTEzLjJ6IiAvPjwvc3ZnPg==) */ const RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(SecurityScanOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'SecurityScanOutlined';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"SecurityScanOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,SecurityScanOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 52\n            }, this),\n            color: '#1890ff'\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 56\n            }, this),\n            color: '#52c41a'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 53\n            }, this),\n            color: '#faad14'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 43\n            }, this),\n            color: '#722ed1'\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#1890ff'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',\n            highlight: 'HTTP/HTTPS & SOCKS5'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#52c41a'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            title: '全球节点覆盖',\n            description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',\n            highlight: '50+ 国家地区'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#faad14'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            title: '极速稳定',\n            description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',\n            highlight: '99.9% 稳定性'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#722ed1'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',\n            highlight: '军用级加密'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#eb2f96'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this),\n            title: '实时监控',\n            description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',\n            highlight: '实时监控面板'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#13c2c2'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, this),\n            title: 'API集成',\n            description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',\n            highlight: 'RESTful API'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 40,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 4,\n                                            style: {\n                                                margin: 0,\n                                                color: '#2563eb'\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: \"middle\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        type: \"text\",\n                                        children: \"登录\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/register\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        type: \"primary\",\n                                        children: \"免费注册\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 1,\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '24px'\n                                    },\n                                    children: [\n                                        \"企业级\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 18\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#2563eb'\n                                            },\n                                            children: \"代理服务解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        color: '#6b7280',\n                                        marginBottom: '48px',\n                                        maxWidth: '800px',\n                                        margin: '0 auto 48px auto'\n                                    },\n                                    children: [\n                                        \"提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议，\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: '#2563eb'\n                                            },\n                                            children: \"99.9%稳定性保证\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"， 助力您的业务全球化发展\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: '48px'\n                                    },\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"small\",\n                                                style: {\n                                                    textAlign: 'center'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    title: stat.title,\n                                                    value: stat.value,\n                                                    suffix: stat.suffix,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '1.2rem',\n                                                            color: '#2563eb'\n                                                        },\n                                                        children: stat.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 31\n                                                    }, void 0),\n                                                    valueStyle: {\n                                                        color: '#2563eb',\n                                                        fontWeight: 'bold'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 160,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: \"large\",\n                                    wrap: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                type: \"primary\",\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"立即开始免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px'\n                                            },\n                                            children: \"观看演示\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: '32px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"无需信用卡\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"7天免费试用\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"随时取消\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_SecurityScanOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});