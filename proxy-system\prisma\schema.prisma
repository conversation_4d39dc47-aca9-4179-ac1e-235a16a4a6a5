// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  apiKey    String?  @unique @map("api_key")
  balance   Float    @default(0)
  currency  String   @default("RUB")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  proxies      Proxy[]
  transactions Transaction[]
  apiLogs      ApiLog[]

  @@map("users")
}

// 代理模型
model Proxy {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  externalId  String   @map("external_id") // px6.me API中的代理ID
  ip          String
  host        String
  port        String
  username    String
  password    String
  type        String   // http 或 socks
  version     String   // 4, 3, 6
  country     String
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  purchaseDate DateTime @map("purchase_date")
  expiryDate   DateTime @map("expiry_date")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, externalId])
  @@map("proxies")
}

// 交易记录模型
model Transaction {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  type        String   // buy, prolong, refund
  amount      Float
  currency    String
  description String?
  status      String   @default("completed") // pending, completed, failed
  externalRef String?  @map("external_ref") // 外部交易引用
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

// API调用日志模型
model ApiLog {
  id         String   @id @default(cuid())
  userId     String?  @map("user_id")
  method     String
  endpoint   String
  params     Json?
  response   Json?
  statusCode Int      @map("status_code")
  duration   Int?     // 毫秒
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("api_logs")
}

// 系统配置模型
model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string") // string, number, boolean, json
  category  String   @default("general")
  description String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("system_configs")
}
