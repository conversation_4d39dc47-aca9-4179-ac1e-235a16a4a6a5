import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ProxyInfo, ProxyVersion, ProxyType } from '@/types/api';

// 合并Tailwind CSS类名
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// 格式化价格
export function formatPrice(price: number, currency: 'RUB' | 'USD' = 'RUB'): string {
  const symbol = currency === 'RUB' ? '₽' : '$';
  return `${price.toFixed(2)} ${symbol}`;
}

// 格式化日期
export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
}

// 计算剩余天数
export function getDaysRemaining(endDate: string): number {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
}

// 获取代理状态文本
export function getProxyStatusText(proxy: ProxyInfo): string {
  if (proxy.active === '0') {
    return '已过期';
  }
  
  const daysRemaining = getDaysRemaining(proxy.date_end);
  if (daysRemaining === 0) {
    return '今日到期';
  } else if (daysRemaining <= 3) {
    return `${daysRemaining}天后到期`;
  } else {
    return '正常';
  }
}

// 获取代理状态颜色
export function getProxyStatusColor(proxy: ProxyInfo): string {
  if (proxy.active === '0') {
    return 'text-red-600 bg-red-50';
  }
  
  const daysRemaining = getDaysRemaining(proxy.date_end);
  if (daysRemaining === 0) {
    return 'text-red-600 bg-red-50';
  } else if (daysRemaining <= 3) {
    return 'text-yellow-600 bg-yellow-50';
  } else {
    return 'text-green-600 bg-green-50';
  }
}

// 获取代理版本文本
export function getProxyVersionText(version: ProxyVersion): string {
  switch (version) {
    case '4':
      return 'IPv4';
    case '3':
      return 'IPv4 Shared';
    case '6':
      return 'IPv6';
    default:
      return 'Unknown';
  }
}

// 获取代理类型文本
export function getProxyTypeText(type: ProxyType): string {
  switch (type) {
    case 'http':
      return 'HTTP/HTTPS';
    case 'socks':
      return 'SOCKS5';
    default:
      return 'Unknown';
  }
}

// 获取国家名称
export function getCountryName(countryCode: string): string {
  const countryNames: Record<string, string> = {
    'ru': '俄罗斯',
    'us': '美国',
    'ua': '乌克兰',
    'de': '德国',
    'fr': '法国',
    'gb': '英国',
    'ca': '加拿大',
    'au': '澳大利亚',
    'jp': '日本',
    'kr': '韩国',
    'cn': '中国',
    'hk': '香港',
    'sg': '新加坡',
    'nl': '荷兰',
    'it': '意大利',
    'es': '西班牙',
    'br': '巴西',
    'in': '印度',
    'tr': '土耳其',
    'pl': '波兰',
  };
  
  return countryNames[countryCode.toLowerCase()] || countryCode.toUpperCase();
}

// 复制到剪贴板
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // 降级方案
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    } catch (err) {
      return false;
    }
  }
}

// 生成代理配置字符串
export function generateProxyConfig(proxy: ProxyInfo, format: 'url' | 'host:port' | 'full' = 'full'): string {
  switch (format) {
    case 'url':
      const protocol = proxy.type === 'socks' ? 'socks5' : 'http';
      return `${protocol}://${proxy.user}:${proxy.pass}@${proxy.host}:${proxy.port}`;
    case 'host:port':
      return `${proxy.host}:${proxy.port}`;
    case 'full':
    default:
      return `${proxy.host}:${proxy.port}:${proxy.user}:${proxy.pass}`;
  }
}

// 验证邮箱格式
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证密码强度
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('密码长度至少8位');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
