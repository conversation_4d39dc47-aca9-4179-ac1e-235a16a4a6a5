/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/register/page";
exports.ids = ["app/register/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'register',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/register/page\",\n        pathname: \"/register\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(rsc)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUE0RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxyZWdpc3RlclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5f85c7f3ad2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc5Luj56CBXFxQcm94eVxcdnNjb2RlX3Byb3h5XFxwcm94eS1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU1Zjg1YzdmM2FkMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"ProxyHub - 企业级代理服务平台\",\n    description: \"专业的代理服务器管理系统，支持IPv4/IPv6代理购买、管理和监控。提供高质量的全球代理网络，99.9%稳定性保证。\",\n    keywords: \"代理服务器,IPv4代理,IPv6代理,企业级代理,代理管理,网络代理\",\n    authors: [\n        {\n            name: \"ProxyHub Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            style: {\n                margin: 0,\n                padding: 0,\n                fontFamily: 'system-ui, -apple-system, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN3QztBQUNqQjtBQUVoQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7SUFDVkMsU0FBUztRQUFDO1lBQUVDLE1BQU07UUFBZ0I7S0FBRTtJQUNwQ0MsVUFBVTtBQUNaLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsT0FBTztnQkFBRUMsUUFBUTtnQkFBR0MsU0FBUztnQkFBR0MsWUFBWTtZQUF1QztzQkFDdkYsNEVBQUNoQixpREFBU0E7MEJBQ1BTOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBQcm92aWRlcnMgfSBmcm9tIFwiLi9wcm92aWRlcnNcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiUHJveHlIdWIgLSDkvIHkuJrnuqfku6PnkIbmnI3liqHlubPlj7BcIixcbiAgZGVzY3JpcHRpb246IFwi5LiT5Lia55qE5Luj55CG5pyN5Yqh5Zmo566h55CG57O757uf77yM5pSv5oyBSVB2NC9JUHY25Luj55CG6LSt5Lmw44CB566h55CG5ZKM55uR5o6n44CC5o+Q5L6b6auY6LSo6YeP55qE5YWo55CD5Luj55CG572R57uc77yMOTkuOSXnqLPlrprmgKfkv53or4HjgIJcIixcbiAga2V5d29yZHM6IFwi5Luj55CG5pyN5Yqh5ZmoLElQdjTku6PnkIYsSVB2NuS7o+eQhizkvIHkuJrnuqfku6PnkIYs5Luj55CG566h55CGLOe9kee7nOS7o+eQhlwiLFxuICBhdXRob3JzOiBbeyBuYW1lOiBcIlByb3h5SHViIFRlYW1cIiB9XSxcbiAgdmlld3BvcnQ6IFwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBzdHlsZT17eyBtYXJnaW46IDAsIHBhZGRpbmc6IDAsIGZvbnRGYW1pbHk6ICdzeXN0ZW0tdWksIC1hcHBsZS1zeXN0ZW0sIHNhbnMtc2VyaWYnIH19PlxuICAgICAgICA8UHJvdmlkZXJzPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9Qcm92aWRlcnM+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlByb3ZpZGVycyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiYXV0aG9ycyIsIm5hbWUiLCJ2aWV3cG9ydCIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsInN0eWxlIiwibWFyZ2luIiwicGFkZGluZyIsImZvbnRGYW1pbHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\register\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/register/page.tsx */ \"(ssr)/./src/app/register/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JlZ2lzdGVyJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUE0RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxyZWdpc3RlclxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cregister%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__.AntdRegistry, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            theme: {\n                token: {\n                    // 主色调\n                    colorPrimary: '#2563eb',\n                    colorSuccess: '#059669',\n                    colorWarning: '#d97706',\n                    colorError: '#dc2626',\n                    colorInfo: '#0891b2',\n                    // 字体\n                    fontFamily: 'system-ui, -apple-system, sans-serif',\n                    fontSize: 14,\n                    // 圆角\n                    borderRadius: 6,\n                    // 间距\n                    padding: 16,\n                    margin: 16,\n                    // 阴影\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    boxShadowSecondary: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                    // 边框\n                    lineWidth: 1,\n                    lineType: 'solid',\n                    colorBorder: '#e5e7eb',\n                    // 背景色\n                    colorBgContainer: '#ffffff',\n                    colorBgLayout: '#f9fafb',\n                    colorBgElevated: '#ffffff'\n                },\n                components: {\n                    // 按钮组件定制\n                    Button: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingContentHorizontal: 16\n                    },\n                    // 输入框组件定制\n                    Input: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingInline: 12\n                    },\n                    // 卡片组件定制\n                    Card: {\n                        borderRadius: 8,\n                        paddingLG: 24\n                    },\n                    // 表格组件定制\n                    Table: {\n                        borderRadius: 8,\n                        cellPaddingBlock: 12,\n                        cellPaddingInline: 16\n                    },\n                    // 菜单组件定制\n                    Menu: {\n                        borderRadius: 6,\n                        itemHeight: 40,\n                        itemPaddingInline: 16\n                    }\n                }\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/register/page.tsx":
/*!***********************************!*\
  !*** ./src/app/register/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RegisterPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/tooltip/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/progress/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/list/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Divider,Form,Input,Layout,List,Progress,Space,Tooltip,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MailOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/InfoCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloseCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserAddOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleOutlined,CloseCircleOutlined,EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,InfoCircleOutlined,LockOutlined,MailOutlined,SafetyOutlined,UserAddOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/HomeOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Content } = _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n// 密码强度验证\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) errors.push('密码长度至少8位');\n    if (!/[A-Z]/.test(password)) errors.push('需要包含大写字母');\n    if (!/[a-z]/.test(password)) errors.push('需要包含小写字母');\n    if (!/[0-9]/.test(password)) errors.push('需要包含数字');\n    if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) errors.push('需要包含特殊字符');\n    return {\n        isValid: errors.length === 0,\n        errors,\n        strength: Math.max(0, 5 - errors.length)\n    };\n};\nconst isValidEmail = (email)=>{\n    return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n};\nfunction RegisterPage() {\n    const [form] = _barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [passwordValidation, setPasswordValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isValid: false,\n        errors: [],\n        strength: 0\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (values)=>{\n        setIsLoading(true);\n        setError('');\n        // 验证表单\n        if (!isValidEmail(values.email)) {\n            setError('请输入有效的邮箱地址');\n            setIsLoading(false);\n            return;\n        }\n        if (!passwordValidation.isValid) {\n            setError('密码不符合要求');\n            setIsLoading(false);\n            return;\n        }\n        if (values.password !== values.confirmPassword) {\n            setError('两次输入的密码不一致');\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const response = await fetch('/api/auth/register', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    email: values.email,\n                    username: values.username,\n                    password: values.password\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // 保存token到localStorage\n                localStorage.setItem('token', data.token);\n                localStorage.setItem('user', JSON.stringify(data.user));\n                // 跳转到仪表板\n                router.push('/dashboard');\n            } else {\n                setError(data.error || '注册失败');\n            }\n        } catch (error) {\n            setError('网络错误，请稍后重试');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePasswordChange = (e)=>{\n        const password = e.target.value;\n        setPasswordValidation(validatePassword(password));\n    };\n    const getPasswordStrengthColor = (strength)=>{\n        if (strength <= 1) return '#ff4d4f';\n        if (strength <= 2) return '#faad14';\n        if (strength <= 3) return '#1890ff';\n        return '#52c41a';\n    };\n    const getPasswordStrengthText = (strength)=>{\n        if (strength <= 1) return '弱';\n        if (strength <= 2) return '中等';\n        if (strength <= 3) return '强';\n        return '很强';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                padding: '24px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '100%',\n                    maxWidth: '480px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginBottom: '32px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            style: {\n                                textDecoration: 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                direction: \"vertical\",\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 64,\n                                        style: {\n                                            backgroundColor: '#2563eb'\n                                        },\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                level: 2,\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#2563eb'\n                                                },\n                                                children: \"ProxyHub\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"企业级代理服务平台\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        style: {\n                            boxShadow: '0 10px 25px rgba(0,0,0,0.1)',\n                            borderRadius: '12px'\n                        },\n                        bodyStyle: {\n                            padding: '32px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    marginBottom: '32px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 3,\n                                        style: {\n                                            marginBottom: '8px'\n                                        },\n                                        children: \"用户注册\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        type: \"secondary\",\n                                        children: \"创建您的账户，开始使用代理服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                message: error,\n                                type: \"error\",\n                                showIcon: true,\n                                style: {\n                                    marginBottom: '24px'\n                                },\n                                closable: true,\n                                onClose: ()=>setError('')\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                form: form,\n                                name: \"register\",\n                                onFinish: handleSubmit,\n                                layout: \"vertical\",\n                                size: \"large\",\n                                autoComplete: \"off\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: \"邮箱地址\",\n                                        name: \"email\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请输入邮箱地址'\n                                            },\n                                            {\n                                                type: 'email',\n                                                message: '请输入有效的邮箱地址'\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请输入邮箱地址\",\n                                            autoComplete: \"email\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: \"用户名\",\n                                        name: \"username\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请输入用户名'\n                                            },\n                                            {\n                                                min: 3,\n                                                message: '用户名至少3个字符'\n                                            },\n                                            {\n                                                max: 20,\n                                                message: '用户名最多20个字符'\n                                            },\n                                            {\n                                                pattern: /^[a-zA-Z0-9_]+$/,\n                                                message: '用户名只能包含字母、数字和下划线'\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请输入用户名\",\n                                            autoComplete: \"username\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"密码\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    title: \"密码需要包含大小写字母、数字和特殊字符，长度至少8位\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        style: {\n                                                            color: '#8c8c8c'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        name: \"password\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请输入密码'\n                                            },\n                                            {\n                                                min: 8,\n                                                message: '密码至少8个字符'\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Password, {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请输入密码\",\n                                            iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 55\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 72\n                                                }, void 0),\n                                            autoComplete: \"new-password\",\n                                            onChange: handlePasswordChange\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    form.getFieldValue('password') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '24px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginBottom: '8px'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                fontSize: '12px'\n                                                            },\n                                                            children: \"密码强度:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                fontSize: '12px',\n                                                                color: getPasswordStrengthColor(passwordValidation.strength),\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: getPasswordStrengthText(passwordValidation.strength)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                percent: passwordValidation.strength / 5 * 100,\n                                                strokeColor: getPasswordStrengthColor(passwordValidation.strength),\n                                                showInfo: false,\n                                                size: \"small\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 19\n                                            }, this),\n                                            passwordValidation.errors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: \"small\",\n                                                style: {\n                                                    marginTop: '8px'\n                                                },\n                                                dataSource: passwordValidation.errors,\n                                                renderItem: (error)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"].Item, {\n                                                        style: {\n                                                            padding: '2px 0',\n                                                            border: 'none'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                fontSize: '12px',\n                                                                color: '#ff4d4f'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    style: {\n                                                                        marginRight: '4px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                error\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 27\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 25\n                                                    }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 21\n                                            }, this),\n                                            passwordValidation.isValid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '8px'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#52c41a'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            style: {\n                                                                marginRight: '4px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"密码强度符合要求\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: \"确认密码\",\n                                        name: \"confirmPassword\",\n                                        dependencies: [\n                                            'password'\n                                        ],\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请确认密码'\n                                            },\n                                            ({ getFieldValue })=>({\n                                                    validator (_, value) {\n                                                        if (!value || getFieldValue('password') === value) {\n                                                            return Promise.resolve();\n                                                        }\n                                                        return Promise.reject(new Error('两次输入的密码不一致'));\n                                                    }\n                                                })\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Password, {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请再次输入密码\",\n                                            iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 55\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 72\n                                                }, void 0),\n                                            autoComplete: \"new-password\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        style: {\n                                            marginBottom: '16px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: isLoading,\n                                            block: true,\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            style: {\n                                                height: '48px',\n                                                fontSize: '16px'\n                                            },\n                                            disabled: !passwordValidation.isValid,\n                                            children: isLoading ? '注册中...' : '注册'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                plain: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    style: {\n                                        fontSize: '12px'\n                                    },\n                                    children: \"其他选项\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    direction: \"vertical\",\n                                    size: \"middle\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            children: [\n                                                \"已有账户？\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    style: {\n                                                        color: '#2563eb',\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"立即登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                type: \"text\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                },\n                                                children: \"返回首页\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Divider_Form_Input_Layout_List_Progress_Space_Tooltip_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\",\n                        style: {\n                            marginTop: '24px',\n                            background: '#f6ffed',\n                            borderColor: '#b7eb8f'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                fontSize: '12px',\n                                color: '#389e0d'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleOutlined_CloseCircleOutlined_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_InfoCircleOutlined_LockOutlined_MailOutlined_SafetyOutlined_UserAddOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    style: {\n                                        marginRight: '4px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                \"注册即表示您同意我们的\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/terms\",\n                                    style: {\n                                        color: '#389e0d',\n                                        textDecoration: 'underline'\n                                    },\n                                    children: \"服务条款\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this),\n                                \"和\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/privacy\",\n                                    style: {\n                                        color: '#389e0d',\n                                        textDecoration: 'underline'\n                                    },\n                                    children: \"隐私政策\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\register\\\\page.tsx\",\n        lineNumber: 146,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/register/page.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@ant-design","vendor-chunks/antd","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-overflow","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/copy-to-clipboard","vendor-chunks/@emotion","vendor-chunks/rc-picker","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/toggle-selection","vendor-chunks/compute-scroll-into-view","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/rc-select","vendor-chunks/rc-virtual-list","vendor-chunks/rc-progress","vendor-chunks/throttle-debounce"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fregister%2Fpage&page=%2Fregister%2Fpage&appPaths=%2Fregister%2Fpage&pagePath=private-next-app-dir%2Fregister%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();