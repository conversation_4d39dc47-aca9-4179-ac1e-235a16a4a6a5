/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUF5RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5f85c7f3ad2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc5Luj56CBXFxQcm94eVxcdnNjb2RlX3Byb3h5XFxwcm94eS1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU1Zjg1YzdmM2FkMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"ProxyHub - 企业级代理服务平台\",\n    description: \"专业的代理服务器管理系统，支持IPv4/IPv6代理购买、管理和监控。提供高质量的全球代理网络，99.9%稳定性保证。\",\n    keywords: \"代理服务器,IPv4代理,IPv6代理,企业级代理,代理管理,网络代理\",\n    authors: [\n        {\n            name: \"ProxyHub Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            style: {\n                margin: 0,\n                padding: 0,\n                fontFamily: 'system-ui, -apple-system, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2xvZ2luJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRKQUF5RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/alert/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/checkbox/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Button,Card,Checkbox,Col,Divider,Form,Input,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/UserOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeTwoTone.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeInvisibleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LoginOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=EyeInvisibleOutlined,EyeTwoTone,HomeOutlined,LockOutlined,LoginOutlined,SafetyOutlined,UserOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/HomeOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Title, Text, Paragraph } = _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Content } = _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction LoginPage() {\n    const [form] = _barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useForm();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const handleSubmit = async (values)=>{\n        setIsLoading(true);\n        setError('');\n        try {\n            const response = await fetch('/api/auth/login', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(values)\n            });\n            const data = await response.json();\n            if (response.ok) {\n                // 保存token到localStorage\n                localStorage.setItem('token', data.token);\n                localStorage.setItem('user', JSON.stringify(data.user));\n                // 跳转到仪表板\n                router.push('/dashboard');\n            } else {\n                setError(data.error || '登录失败');\n            }\n        } catch (error) {\n            setError('网络错误，请稍后重试');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                padding: '24px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '100%',\n                    maxWidth: '480px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: 'center',\n                            marginBottom: '32px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            style: {\n                                textDecoration: 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                direction: \"vertical\",\n                                size: \"small\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 64,\n                                        style: {\n                                            backgroundColor: '#2563eb'\n                                        },\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                level: 2,\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#2563eb'\n                                                },\n                                                children: \"ProxyHub\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: \"企业级代理服务平台\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        style: {\n                            boxShadow: '0 10px 25px rgba(0,0,0,0.1)',\n                            borderRadius: '12px'\n                        },\n                        bodyStyle: {\n                            padding: '32px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    marginBottom: '32px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 3,\n                                        style: {\n                                            marginBottom: '8px',\n                                            color: '#1890ff'\n                                        },\n                                        children: \"\\uD83D\\uDD10 用户登录\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        type: \"secondary\",\n                                        style: {\n                                            fontSize: '15px'\n                                        },\n                                        children: \"欢迎回来！请输入您的账户信息\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                        type: \"secondary\",\n                                        style: {\n                                            fontSize: '13px'\n                                        },\n                                        children: \"登录后即可访问专业的代理服务管理平台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                message: error,\n                                type: \"error\",\n                                showIcon: true,\n                                style: {\n                                    marginBottom: '24px'\n                                },\n                                closable: true,\n                                onClose: ()=>setError('')\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                form: form,\n                                name: \"login\",\n                                onFinish: handleSubmit,\n                                layout: \"vertical\",\n                                size: \"large\",\n                                autoComplete: \"off\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '14px',\n                                                fontWeight: '500'\n                                            },\n                                            children: \"\\uD83D\\uDCE7 用户名或邮箱\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        name: \"login\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请输入用户名或邮箱'\n                                            },\n                                            {\n                                                min: 3,\n                                                message: '用户名至少3个字符'\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请输入用户名或邮箱地址\",\n                                            autoComplete: \"username\",\n                                            style: {\n                                                height: '44px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '14px',\n                                                fontWeight: '500'\n                                            },\n                                            children: \"\\uD83D\\uDD12 登录密码\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        name: \"password\",\n                                        rules: [\n                                            {\n                                                required: true,\n                                                message: '请输入密码'\n                                            },\n                                            {\n                                                min: 6,\n                                                message: '密码至少6个字符'\n                                            }\n                                        ],\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"].Password, {\n                                            prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 27\n                                            }, void 0),\n                                            placeholder: \"请输入您的登录密码\",\n                                            iconRender: (visible)=>visible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 55\n                                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 72\n                                                }, void 0),\n                                            autoComplete: \"current-password\",\n                                            style: {\n                                                height: '44px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        style: {\n                                            marginBottom: '24px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            justify: \"space-between\",\n                                            align: \"middle\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                                        name: \"remember\",\n                                                        valuePropName: \"checked\",\n                                                        noStyle: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            children: \"记住我\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        type: \"link\",\n                                                        style: {\n                                                            padding: 0,\n                                                            fontSize: '14px'\n                                                        },\n                                                        children: \"忘记密码？\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Item, {\n                                        style: {\n                                            marginBottom: '16px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            type: \"primary\",\n                                            htmlType: \"submit\",\n                                            loading: isLoading,\n                                            block: true,\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            style: {\n                                                height: '48px',\n                                                fontSize: '16px'\n                                            },\n                                            children: isLoading ? '登录中...' : '登录'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                plain: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    type: \"secondary\",\n                                    style: {\n                                        fontSize: '12px'\n                                    },\n                                    children: \"其他选项\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    direction: \"vertical\",\n                                    size: \"middle\",\n                                    style: {\n                                        width: '100%'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            children: [\n                                                \"还没有账户？\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/register\",\n                                                    style: {\n                                                        color: '#2563eb',\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"立即注册\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                type: \"text\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                style: {\n                                                    color: '#8c8c8c'\n                                                },\n                                                children: \"返回首页\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\",\n                        style: {\n                            marginTop: '24px',\n                            background: '#f0f9ff',\n                            borderColor: '#91d5ff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                    strong: true,\n                                    style: {\n                                        color: '#1890ff',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"\\uD83D\\uDE80 登录即可享受\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: '12px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        gutter: [\n                                            16,\n                                            8\n                                        ],\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1890ff'\n                                                    },\n                                                    children: \"✓ 专业代理服务\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1890ff'\n                                                    },\n                                                    children: \"✓ 实时监控面板\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1890ff'\n                                                    },\n                                                    children: \"✓ API 接口调用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                span: 12,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1890ff'\n                                                    },\n                                                    children: \"✓ 24/7 技术支持\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\",\n                        style: {\n                            marginTop: '24px',\n                            background: '#f6ffed',\n                            borderColor: '#b7eb8f'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Button_Card_Checkbox_Col_Divider_Form_Input_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EyeInvisibleOutlined_EyeTwoTone_HomeOutlined_LockOutlined_LoginOutlined_SafetyOutlined_UserOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    style: {\n                                        color: '#52c41a'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: '#389e0d',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"安全提示\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: \"我们使用企业级加密技术保护您的账户安全\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__.AntdRegistry, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            theme: {\n                token: {\n                    // 主色调\n                    colorPrimary: '#2563eb',\n                    colorSuccess: '#059669',\n                    colorWarning: '#d97706',\n                    colorError: '#dc2626',\n                    colorInfo: '#0891b2',\n                    // 字体\n                    fontFamily: 'system-ui, -apple-system, sans-serif',\n                    fontSize: 14,\n                    // 圆角\n                    borderRadius: 6,\n                    // 间距\n                    padding: 16,\n                    margin: 16,\n                    // 阴影\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    boxShadowSecondary: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                    // 边框\n                    lineWidth: 1,\n                    lineType: 'solid',\n                    colorBorder: '#e5e7eb',\n                    // 背景色\n                    colorBgContainer: '#ffffff',\n                    colorBgLayout: '#f9fafb',\n                    colorBgElevated: '#ffffff'\n                },\n                components: {\n                    // 按钮组件定制\n                    Button: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingContentHorizontal: 16\n                    },\n                    // 输入框组件定制\n                    Input: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingInline: 12\n                    },\n                    // 卡片组件定制\n                    Card: {\n                        borderRadius: 8,\n                        paddingLG: 24\n                    },\n                    // 表格组件定制\n                    Table: {\n                        borderRadius: 8,\n                        cellPaddingBlock: 12,\n                        cellPaddingInline: 16\n                    },\n                    // 菜单组件定制\n                    Menu: {\n                        borderRadius: 6,\n                        itemHeight: 40,\n                        itemPaddingInline: 16\n                    }\n                }\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-overflow","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/copy-to-clipboard","vendor-chunks/@emotion","vendor-chunks/rc-picker","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/toggle-selection","vendor-chunks/compute-scroll-into-view","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/rc-checkbox"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();