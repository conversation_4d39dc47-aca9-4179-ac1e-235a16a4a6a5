'use client'

import { AntdRegistry } from '@ant-design/nextjs-registry'
import { ConfigProvider, theme } from 'antd'
import zhCN from 'antd/locale/zh_CN'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AntdRegistry>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            // 主色调
            colorPrimary: '#2563eb',
            colorSuccess: '#059669',
            colorWarning: '#d97706',
            colorError: '#dc2626',
            colorInfo: '#0891b2',
            
            // 字体
            fontFamily: 'system-ui, -apple-system, sans-serif',
            fontSize: 14,
            
            // 圆角
            borderRadius: 6,
            
            // 间距
            padding: 16,
            margin: 16,
            
            // 阴影
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            boxShadowSecondary: '0 4px 6px rgba(0, 0, 0, 0.1)',
            
            // 边框
            lineWidth: 1,
            lineType: 'solid',
            colorBorder: '#e5e7eb',
            
            // 背景色
            colorBgContainer: '#ffffff',
            colorBgLayout: '#f9fafb',
            colorBgElevated: '#ffffff',
          },
          components: {
            // 按钮组件定制
            Button: {
              borderRadius: 6,
              controlHeight: 40,
              paddingContentHorizontal: 16,
            },
            // 输入框组件定制
            Input: {
              borderRadius: 6,
              controlHeight: 40,
              paddingInline: 12,
            },
            // 卡片组件定制
            Card: {
              borderRadius: 8,
              paddingLG: 24,
            },
            // 表格组件定制
            Table: {
              borderRadius: 8,
              cellPaddingBlock: 12,
              cellPaddingInline: 16,
            },
            // 菜单组件定制
            Menu: {
              borderRadius: 6,
              itemHeight: 40,
              itemPaddingInline: 16,
            },
          },
        }}
      >
        {children}
      </ConfigProvider>
    </AntdRegistry>
  )
}
