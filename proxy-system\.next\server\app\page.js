/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(rsc)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBa0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXOS7o+eggVxcXFxQcm94eVxcXFx2c2NvZGVfcHJveHlcXFxccHJveHktc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e5f85c7f3ad2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJFOlxc5Luj56CBXFxQcm94eVxcdnNjb2RlX3Byb3h5XFxwcm94eS1zeXN0ZW1cXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU1Zjg1YzdmM2FkMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"ProxyHub - 企业级代理服务平台\",\n    description: \"专业的代理服务器管理系统，支持IPv4/IPv6代理购买、管理和监控。提供高质量的全球代理网络，99.9%稳定性保证。\",\n    keywords: \"代理服务器,IPv4代理,IPv6代理,企业级代理,代理管理,网络代理\",\n    authors: [\n        {\n            name: \"ProxyHub Team\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            style: {\n                margin: 0,\n                padding: 0,\n                fontFamily: 'system-ui, -apple-system, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_1__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\代码\\Proxy\\vscode_proxy\\proxy-system\\src\\app\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUFzSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiRTpcXFxc5Luj56CBXFxcXFByb3h5XFxcXHZzY29kZV9wcm94eVxcXFxwcm94eS1zeXN0ZW1cXFxcc3JjXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUMlRTQlQkIlQTMlRTclQTAlODElNUMlNUNQcm94eSU1QyU1Q3ZzY29kZV9wcm94eSU1QyU1Q3Byb3h5LXN5c3RlbSU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBa0ciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXOS7o+eggVxcXFxQcm94eVxcXFx2c2NvZGVfcHJveHlcXFxccHJveHktc3lzdGVtXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5C%E4%BB%A3%E7%A0%81%5C%5CProxy%5C%5Cvscode_proxy%5C%5Cproxy-system%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(ssr)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(ssr)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CloudOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(ssr)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('1');\n    // 核心统计数据\n    const stats = [\n        {\n            title: '全球节点',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            color: '#3b82f6',\n            description: '覆盖全球主要国家和地区'\n        },\n        {\n            title: '企业用户',\n            value: 10000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            color: '#10b981',\n            description: '服务全球企业客户'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, this),\n            color: '#f59e0b',\n            description: 'SLA 服务等级保证'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, this),\n            color: '#8b5cf6',\n            description: '全天候专业技术支持'\n        }\n    ];\n    // 核心功能特性\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',\n            tags: [\n                'HTTP/HTTPS',\n                'SOCKS5',\n                '高兼容性'\n            ],\n            color: '#3b82f6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, this),\n            title: '全球节点网络',\n            description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',\n            tags: [\n                '50+ 国家',\n                '低延迟',\n                '高速度'\n            ],\n            color: '#10b981'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，多重安全防护，保障数据传输安全',\n            tags: [\n                '军用级加密',\n                '多重防护',\n                '隐私保护'\n            ],\n            color: '#f59e0b'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, this),\n            title: '智能监控',\n            description: '实时监控系统状态，智能故障检测，确保服务稳定运行',\n            tags: [\n                '实时监控',\n                '智能检测',\n                '自动恢复'\n            ],\n            color: '#8b5cf6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 13\n            }, this),\n            title: 'API 集成',\n            description: '完整的 RESTful API，支持自动化管理和第三方系统集成',\n            tags: [\n                'RESTful API',\n                '自动化',\n                '易集成'\n            ],\n            color: '#ef4444'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, this),\n            title: '灵活配置',\n            description: '支持自定义配置，满足不同业务场景的个性化需求',\n            tags: [\n                '自定义配置',\n                '灵活部署',\n                '场景适配'\n            ],\n            color: '#06b6d4'\n        }\n    ];\n    // 使用步骤\n    const steps = [\n        {\n            title: '注册账户',\n            description: '快速注册，获取专属 API 密钥',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '选择套餐',\n            description: '根据业务需求选择合适的服务套餐',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '配置接入',\n            description: '通过 API 或控制面板配置代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '开始使用',\n            description: '享受稳定高效的全球代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: '某大型电商平台',\n            industry: '电子商务',\n            content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',\n            avatar: 'E',\n            color: '#3b82f6'\n        },\n        {\n            company: '某金融科技公司',\n            industry: '金融科技',\n            content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',\n            avatar: 'F',\n            color: '#10b981'\n        },\n        {\n            company: '某数据分析公司',\n            industry: '数据分析',\n            content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',\n            avatar: 'D',\n            color: '#f59e0b'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                    padding: '0 24px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000,\n                    borderBottom: '1px solid rgba(0,0,0,0.06)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1400px',\n                            margin: '0 auto',\n                            height: '72px'\n                        },\n                        className: \"jsx-2af30dff10d881d1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '16px'\n                                    },\n                                    className: \"jsx-2af30dff10d881d1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '48px',\n                                                height: '48px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                            },\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                style: {\n                                                    fontSize: '24px',\n                                                    color: '#fff'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 3,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#1e293b',\n                                                        fontSize: '24px',\n                                                        fontWeight: '700',\n                                                        lineHeight: '1'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '13px',\n                                                        color: '#64748b',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Enterprise Proxy Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-2af30dff10d881d1\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"开发者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e2e8f0',\n                                                height: '24px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#475569',\n                                                    fontSize: '15px',\n                                                    height: '40px',\n                                                    borderRadius: '8px'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.target.style.color = '#3b82f6';\n                                                    e.target.style.background = '#f1f5f9';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.target.style.color = '#475569';\n                                                    e.target.style.background = 'transparent';\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                    borderColor: 'transparent',\n                                                    borderRadius: '10px',\n                                                    height: '44px',\n                                                    paddingLeft: '24px',\n                                                    paddingRight: '24px',\n                                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                                                    border: 'none'\n                                                },\n                                                children: \"免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                className: \"jsx-2af30dff10d881d1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-2af30dff10d881d1\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '8px',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none',\n                                            fontSize: '18px',\n                                            width: '44px',\n                                            height: '44px',\n                                            borderRadius: '8px'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"2af30dff10d881d1\",\n                        children: \"@media(min-width:1024px){.desktop-nav.jsx-2af30dff10d881d1{display:block!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:1023px)and (min-width:640px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:639px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '10px',\n                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                style: {\n                                    fontSize: '20px',\n                                    color: '#fff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#1e293b',\n                                        fontWeight: '700',\n                                        fontSize: '18px'\n                                    },\n                                    children: \"ProxyHub\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#64748b',\n                                        fontSize: '12px'\n                                    },\n                                    children: \"Enterprise Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 320,\n                styles: {\n                    body: {\n                        padding: '24px'\n                    },\n                    header: {\n                        borderBottom: '1px solid #f1f5f9',\n                        paddingBottom: '16px'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"导航菜单\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#3b82f6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                \"产品特性\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#10b981'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                \"解决方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#f59e0b'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                \"价格方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#8b5cf6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                \"开发者\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            style: {\n                                margin: '24px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"账户操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#475569',\n                                    borderRadius: '12px',\n                                    border: '1px solid #e2e8f0'\n                                },\n                                children: \"登录账户\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                    borderColor: 'transparent',\n                                    borderRadius: '12px',\n                                    marginTop: '8px',\n                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                },\n                                children: \"免费试用\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',\n                            padding: '140px 24px 120px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n                                    opacity: 0.6\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '20%',\n                                    left: '10%',\n                                    width: '300px',\n                                    height: '300px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(40px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '60%',\n                                    right: '10%',\n                                    width: '200px',\n                                    height: '200px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(30px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '56px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '16px',\n                                                background: 'rgba(255,255,255,0.08)',\n                                                backdropFilter: 'blur(20px)',\n                                                border: '1px solid rgba(255,255,255,0.12)',\n                                                borderRadius: '60px',\n                                                padding: '12px 32px',\n                                                marginBottom: '32px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '10px',\n                                                                height: '10px',\n                                                                borderRadius: '50%',\n                                                                background: '#10b981',\n                                                                boxShadow: '0 0 12px #10b981',\n                                                                animation: 'pulse 2s infinite'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"服务 10,000+ 企业客户\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    type: \"vertical\",\n                                                    style: {\n                                                        borderColor: 'rgba(255,255,255,0.2)',\n                                                        height: '16px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            style: {\n                                                                color: '#f59e0b',\n                                                                fontSize: '16px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"行业领先\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(3rem, 8vw, 5rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '800',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.03em',\n                                            textShadow: '0 4px 20px rgba(0,0,0,0.3)'\n                                        },\n                                        children: [\n                                            \"企业级全球代理\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #3b82f6, #06b6d4)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text',\n                                                    fontWeight: '800'\n                                                },\n                                                children: \"解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.2rem, 3vw, 1.5rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '56px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 56px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"为企业提供稳定、安全、高速的全球代理网络服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% SLA 保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#34d399',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家节点\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"24/7 技术支持\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                wrap: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/register\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            type: \"primary\",\n                                                            size: \"large\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            style: {\n                                                                height: '64px',\n                                                                fontSize: '18px',\n                                                                fontWeight: '700',\n                                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                                borderColor: 'transparent',\n                                                                borderRadius: '16px',\n                                                                paddingLeft: '40px',\n                                                                paddingRight: '40px',\n                                                                boxShadow: '0 12px 32px rgba(59, 130, 246, 0.4)',\n                                                                border: 'none',\n                                                                minWidth: '200px'\n                                                            },\n                                                            children: \"立即免费试用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        size: \"large\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 25\n                                                        }, void 0),\n                                                        style: {\n                                                            height: '64px',\n                                                            fontSize: '18px',\n                                                            fontWeight: '600',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            border: '2px solid rgba(255,255,255,0.16)',\n                                                            color: '#ffffff',\n                                                            backdropFilter: 'blur(20px)',\n                                                            borderRadius: '16px',\n                                                            paddingLeft: '40px',\n                                                            paddingRight: '40px',\n                                                            minWidth: '180px'\n                                                        },\n                                                        children: \"观看演示\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '48px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.6)',\n                                                            fontSize: '14px',\n                                                            display: 'block',\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: \"已有数千家企业选择我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: \"large\",\n                                                        wrap: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"10K+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"企业用户\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"50+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"国家节点\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"99.9%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"稳定性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: 'rgba(255,255,255,0.05)',\n                                            backdropFilter: 'blur(20px)',\n                                            border: '1px solid rgba(255,255,255,0.1)',\n                                            borderRadius: '32px',\n                                            padding: '48px 32px',\n                                            marginBottom: '0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            gutter: [\n                                                32,\n                                                32\n                                            ],\n                                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    xs: 12,\n                                                    lg: 6,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            textAlign: 'center',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            backdropFilter: 'blur(20px)',\n                                                            border: '1px solid rgba(255,255,255,0.12)',\n                                                            borderRadius: '24px',\n                                                            padding: '40px 24px',\n                                                            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                            cursor: 'default',\n                                                            position: 'relative',\n                                                            overflow: 'hidden'\n                                                        },\n                                                        onMouseEnter: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.12)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)';\n                                                        },\n                                                        onMouseLeave: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.08)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.12)';\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'absolute',\n                                                                    top: '50%',\n                                                                    left: '50%',\n                                                                    transform: 'translate(-50%, -50%)',\n                                                                    width: '120px',\n                                                                    height: '120px',\n                                                                    borderRadius: '50%',\n                                                                    background: `radial-gradient(circle, ${stat.color}15 0%, transparent 70%)`,\n                                                                    filter: 'blur(20px)',\n                                                                    zIndex: 0\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'relative',\n                                                                    zIndex: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '24px',\n                                                                            display: 'flex',\n                                                                            justifyContent: 'center',\n                                                                            alignItems: 'center'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                width: '72px',\n                                                                                height: '72px',\n                                                                                borderRadius: '20px',\n                                                                                background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}30)`,\n                                                                                display: 'flex',\n                                                                                alignItems: 'center',\n                                                                                justifyContent: 'center',\n                                                                                border: `2px solid ${stat.color}40`,\n                                                                                boxShadow: `0 8px 32px ${stat.color}20`\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    fontSize: '32px',\n                                                                                    color: stat.color,\n                                                                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                                                                },\n                                                                                children: stat.icon\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 912,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 901,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '12px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: '#ffffff',\n                                                                                fontSize: '36px',\n                                                                                fontWeight: '800',\n                                                                                lineHeight: '1',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: [\n                                                                                stat.value,\n                                                                                stat.suffix\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: 'rgba(255,255,255,0.9)',\n                                                                                fontSize: '16px',\n                                                                                fontWeight: '600',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: stat.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 936,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '13px',\n                                                                            lineHeight: '1.4'\n                                                                        },\n                                                                        children: stat.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1022,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 1186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQ0o7QUFvQmY7QUF3QmE7QUFFM0IsTUFBTSxFQUFFNkIsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sRUFBRSxHQUFHdkIscUlBQU1BO0FBQzFDLE1BQU0sRUFBRXdCLEtBQUssRUFBRUMsU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBRzVCLHFJQUFVQTtBQUU5QixTQUFTNkI7SUFDdEIsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHckMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDc0MsV0FBV0MsYUFBYSxHQUFHdkMsK0NBQVFBLENBQUM7SUFFM0MsU0FBUztJQUNULE1BQU13QyxRQUFRO1FBQ1o7WUFDRUMsT0FBTztZQUNQQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsb0JBQU0sOERBQUMvQixvVkFBY0E7Ozs7O1lBQ3JCZ0MsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFTCxPQUFPO1lBQ1BDLE9BQU87WUFDUEMsUUFBUTtZQUNSQyxvQkFBTSw4REFBQ25CLG9WQUFZQTs7Ozs7WUFDbkJvQixPQUFPO1lBQ1BDLGFBQWE7UUFDZjtRQUNBO1lBQ0VMLE9BQU87WUFDUEMsT0FBTztZQUNQQyxRQUFRO1lBQ1JDLG9CQUFNLDhEQUFDaEMsb1ZBQWNBOzs7OztZQUNyQmlDLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsT0FBTztZQUNQQyxPQUFPO1lBQ1BFLG9CQUFNLDhEQUFDaEMsb1ZBQWNBOzs7OztZQUNyQmlDLE9BQU87WUFDUEMsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxTQUFTO0lBQ1QsTUFBTUMsV0FBVztRQUNmO1lBQ0VILG9CQUFNLDhEQUFDckIsb1ZBQWFBOzs7OztZQUNwQmtCLE9BQU87WUFDUEssYUFBYTtZQUNiRSxNQUFNO2dCQUFDO2dCQUFjO2dCQUFVO2FBQU87WUFDdENILE9BQU87UUFDVDtRQUNBO1lBQ0VELG9CQUFNLDhEQUFDL0Isb1ZBQWNBOzs7OztZQUNyQjRCLE9BQU87WUFDUEssYUFBYTtZQUNiRSxNQUFNO2dCQUFDO2dCQUFVO2dCQUFPO2FBQU07WUFDOUJILE9BQU87UUFDVDtRQUNBO1lBQ0VELG9CQUFNLDhEQUFDN0IscVZBQVlBOzs7OztZQUNuQjBCLE9BQU87WUFDUEssYUFBYTtZQUNiRSxNQUFNO2dCQUFDO2dCQUFTO2dCQUFRO2FBQU87WUFDL0JILE9BQU87UUFDVDtRQUNBO1lBQ0VELG9CQUFNLDhEQUFDcEIscVZBQWlCQTs7Ozs7WUFDeEJpQixPQUFPO1lBQ1BLLGFBQWE7WUFDYkUsTUFBTTtnQkFBQztnQkFBUTtnQkFBUTthQUFPO1lBQzlCSCxPQUFPO1FBQ1Q7UUFDQTtZQUNFRCxvQkFBTSw4REFBQzNCLHFWQUFXQTs7Ozs7WUFDbEJ3QixPQUFPO1lBQ1BLLGFBQWE7WUFDYkUsTUFBTTtnQkFBQztnQkFBZTtnQkFBTzthQUFNO1lBQ25DSCxPQUFPO1FBQ1Q7UUFDQTtZQUNFRCxvQkFBTSw4REFBQ2hCLHFWQUFlQTs7Ozs7WUFDdEJhLE9BQU87WUFDUEssYUFBYTtZQUNiRSxNQUFNO2dCQUFDO2dCQUFTO2dCQUFRO2FBQU87WUFDL0JILE9BQU87UUFDVDtLQUNEO0lBRUQsT0FBTztJQUNQLE1BQU1JLFFBQVE7UUFDWjtZQUNFUixPQUFPO1lBQ1BLLGFBQWE7WUFDYkYsb0JBQU0sOERBQUNqQixxVkFBYUE7Ozs7O1FBQ3RCO1FBQ0E7WUFDRWMsT0FBTztZQUNQSyxhQUFhO1lBQ2JGLG9CQUFNLDhEQUFDaEIscVZBQWVBOzs7OztRQUN4QjtRQUNBO1lBQ0VhLE9BQU87WUFDUEssYUFBYTtZQUNiRixvQkFBTSw4REFBQzNCLHFWQUFXQTs7Ozs7UUFDcEI7UUFDQTtZQUNFd0IsT0FBTztZQUNQSyxhQUFhO1lBQ2JGLG9CQUFNLDhEQUFDdEIscVZBQWNBOzs7OztRQUN2QjtLQUNEO0lBRUQsT0FBTztJQUNQLE1BQU00QixlQUFlO1FBQ25CO1lBQ0VDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxTQUFTO1lBQ1RDLFFBQVE7WUFDUlQsT0FBTztRQUNUO1FBQ0E7WUFDRU0sU0FBUztZQUNUQyxVQUFVO1lBQ1ZDLFNBQVM7WUFDVEMsUUFBUTtZQUNSVCxPQUFPO1FBQ1Q7UUFDQTtZQUNFTSxTQUFTO1lBQ1RDLFVBQVU7WUFDVkMsU0FBUztZQUNUQyxRQUFRO1lBQ1JULE9BQU87UUFDVDtLQUNEO0lBRUQscUJBQ0UsOERBQUNyQyxxSUFBTUE7UUFBQytDLE9BQU87WUFBRUMsV0FBVztRQUFROzswQkFFbEMsOERBQUMzQjtnQkFBTzBCLE9BQU87b0JBQ2JFLFlBQVk7b0JBQ1pDLGdCQUFnQjtvQkFDaEJDLFdBQVc7b0JBQ1hDLFNBQVM7b0JBQ1RDLFVBQVU7b0JBQ1ZDLEtBQUs7b0JBQ0xDLFFBQVE7b0JBQ1JDLGNBQWM7Z0JBQ2hCOztrQ0FDRSw4REFBQ0M7d0JBQUlWLE9BQU87NEJBQ1ZXLFNBQVM7NEJBQ1RDLFlBQVk7NEJBQ1pDLGdCQUFnQjs0QkFDaEJDLFVBQVU7NEJBQ1ZDLFFBQVE7NEJBQ1JDLFFBQVE7d0JBQ1Y7OzswQ0FFRSw4REFBQ3RFLGtEQUFJQTtnQ0FBQ3VFLE1BQUs7Z0NBQUlqQixPQUFPO29DQUFFa0IsZ0JBQWdCO2dDQUFPOzBDQUM3Qyw0RUFBQ1I7b0NBQUlWLE9BQU87d0NBQUVXLFNBQVM7d0NBQVFDLFlBQVk7d0NBQVVPLEtBQUs7b0NBQU87OztzREFDL0QsOERBQUNUOzRDQUFJVixPQUFPO2dEQUNWb0IsT0FBTztnREFDUEosUUFBUTtnREFDUkssY0FBYztnREFDZG5CLFlBQVk7Z0RBQ1pTLFNBQVM7Z0RBQ1RDLFlBQVk7Z0RBQ1pDLGdCQUFnQjtnREFDaEJULFdBQVc7NENBQ2I7O3NEQUNFLDRFQUFDL0Msb1ZBQWNBO2dEQUFDMkMsT0FBTztvREFBRXNCLFVBQVU7b0RBQVFoQyxPQUFPO2dEQUFPOzs7Ozs7Ozs7OztzREFFM0QsOERBQUNvQjs7OzhEQUNDLDhEQUFDakM7b0RBQU04QyxPQUFPO29EQUFHdkIsT0FBTzt3REFDdEJlLFFBQVE7d0RBQ1J6QixPQUFPO3dEQUNQZ0MsVUFBVTt3REFDVkUsWUFBWTt3REFDWkMsWUFBWTtvREFDZDs4REFBRzs7Ozs7OzhEQUdILDhEQUFDOUM7b0RBQUtxQixPQUFPO3dEQUNYc0IsVUFBVTt3REFDVmhDLE9BQU87d0RBQ1BrQyxZQUFZO29EQUNkOzhEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRVCw4REFBQ2Q7Z0NBQUlWLE9BQU87b0NBQUVXLFNBQVM7Z0NBQU87MEVBQWE7MENBQ3pDLDRFQUFDM0Qsc0lBQUtBO29DQUFDMEUsTUFBSzs7c0RBQ1YsOERBQUMvRSxzSUFBTUE7NENBQ0xnRixNQUFLOzRDQUNMM0IsT0FBTztnREFDTHdCLFlBQVk7Z0RBQ1psQyxPQUFPO2dEQUNQZ0MsVUFBVTtnREFDVk4sUUFBUTtnREFDUkssY0FBYzs0Q0FDaEI7NENBQ0FPLGNBQWMsQ0FBQ0M7Z0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO2dEQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM5Qjs0Q0FDQTZCLGNBQWMsQ0FBQ0Y7Z0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO2dEQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM5QjtzREFDRDs7Ozs7O3NEQUdELDhEQUFDdkQsc0lBQU1BOzRDQUNMZ0YsTUFBSzs0Q0FDTDNCLE9BQU87Z0RBQ0x3QixZQUFZO2dEQUNabEMsT0FBTztnREFDUGdDLFVBQVU7Z0RBQ1ZOLFFBQVE7Z0RBQ1JLLGNBQWM7NENBQ2hCOzRDQUNBTyxjQUFjLENBQUNDO2dEQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNWLEtBQUssR0FBRztnREFDdkJ1QyxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRzs0Q0FDOUI7NENBQ0E2QixjQUFjLENBQUNGO2dEQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNWLEtBQUssR0FBRztnREFDdkJ1QyxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRzs0Q0FDOUI7c0RBQ0Q7Ozs7OztzREFHRCw4REFBQ3ZELHNJQUFNQTs0Q0FDTGdGLE1BQUs7NENBQ0wzQixPQUFPO2dEQUNMd0IsWUFBWTtnREFDWmxDLE9BQU87Z0RBQ1BnQyxVQUFVO2dEQUNWTixRQUFRO2dEQUNSSyxjQUFjOzRDQUNoQjs0Q0FDQU8sY0FBYyxDQUFDQztnREFDYkEsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDVixLQUFLLEdBQUc7Z0RBQ3ZCdUMsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDRSxVQUFVLEdBQUc7NENBQzlCOzRDQUNBNkIsY0FBYyxDQUFDRjtnREFDYkEsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDVixLQUFLLEdBQUc7Z0RBQ3ZCdUMsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDRSxVQUFVLEdBQUc7NENBQzlCO3NEQUNEOzs7Ozs7c0RBR0QsOERBQUN2RCxzSUFBTUE7NENBQ0xnRixNQUFLOzRDQUNMM0IsT0FBTztnREFDTHdCLFlBQVk7Z0RBQ1psQyxPQUFPO2dEQUNQZ0MsVUFBVTtnREFDVk4sUUFBUTtnREFDUkssY0FBYzs0Q0FDaEI7NENBQ0FPLGNBQWMsQ0FBQ0M7Z0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO2dEQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM5Qjs0Q0FDQTZCLGNBQWMsQ0FBQ0Y7Z0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO2dEQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM5QjtzREFDRDs7Ozs7O3NEQUdELDhEQUFDOUMsc0lBQU9BOzRDQUFDdUUsTUFBSzs0Q0FBVzNCLE9BQU87Z0RBQUVnQyxhQUFhO2dEQUFXaEIsUUFBUTs0Q0FBTzs7Ozs7O3NEQUN6RSw4REFBQ3RFLGtEQUFJQTs0Q0FBQ3VFLE1BQUs7c0RBQ1QsNEVBQUN0RSxzSUFBTUE7Z0RBQ0xnRixNQUFLO2dEQUNMM0IsT0FBTztvREFDTHdCLFlBQVk7b0RBQ1psQyxPQUFPO29EQUNQZ0MsVUFBVTtvREFDVk4sUUFBUTtvREFDUkssY0FBYztnREFDaEI7Z0RBQ0FPLGNBQWMsQ0FBQ0M7b0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO29EQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHO2dEQUM5QjtnREFDQTZCLGNBQWMsQ0FBQ0Y7b0RBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ1YsS0FBSyxHQUFHO29EQUN2QnVDLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHO2dEQUM5QjswREFDRDs7Ozs7Ozs7Ozs7c0RBSUgsOERBQUN4RCxrREFBSUE7NENBQUN1RSxNQUFLO3NEQUNULDRFQUFDdEUsc0lBQU1BO2dEQUNMZ0YsTUFBSztnREFDTDNCLE9BQU87b0RBQ0x3QixZQUFZO29EQUNaRixVQUFVO29EQUNWcEIsWUFBWTtvREFDWjhCLGFBQWE7b0RBQ2JYLGNBQWM7b0RBQ2RMLFFBQVE7b0RBQ1JpQixhQUFhO29EQUNiQyxjQUFjO29EQUNkOUIsV0FBVztvREFDWCtCLFFBQVE7Z0RBQ1Y7MERBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBUVAsOERBQUN6QjtnQ0FBSVYsT0FBTztvQ0FBRVcsU0FBUztvQ0FBUUMsWUFBWTtvQ0FBVU8sS0FBSztnQ0FBTzs7O2tEQUMvRCw4REFBQ1Q7d0NBQUlWLE9BQU87NENBQUVXLFNBQVM7NENBQVFRLEtBQUs7d0NBQU07a0ZBQWE7OzBEQUNyRCw4REFBQ3pFLGtEQUFJQTtnREFBQ3VFLE1BQUs7MERBQ1QsNEVBQUN0RSxzSUFBTUE7b0RBQUNnRixNQUFLO29EQUFPRCxNQUFLO29EQUFTMUIsT0FBTzt3REFBRXdCLFlBQVk7b0RBQU07OERBQUc7Ozs7Ozs7Ozs7OzBEQUlsRSw4REFBQzlFLGtEQUFJQTtnREFBQ3VFLE1BQUs7MERBQ1QsNEVBQUN0RSxzSUFBTUE7b0RBQ0xnRixNQUFLO29EQUNMRCxNQUFLO29EQUNMMUIsT0FBTzt3REFDTEUsWUFBWTt3REFDWjhCLGFBQWE7d0RBQ2JYLGNBQWM7d0RBQ2RHLFlBQVk7b0RBQ2Q7OERBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU1MLDhEQUFDN0Usc0lBQU1BO3dDQUNMZ0YsTUFBSzt3Q0FDTHRDLG9CQUFNLDhEQUFDdkIscVZBQVlBOzs7Ozt3Q0FDbkJzRSxTQUFTLElBQU10RCxrQkFBa0I7d0NBQ2pDa0IsT0FBTzs0Q0FDTFcsU0FBUzs0Q0FDVFcsVUFBVTs0Q0FDVkYsT0FBTzs0Q0FDUEosUUFBUTs0Q0FDUkssY0FBYzt3Q0FDaEI7d0NBQ0FnQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQTBCbEIsOERBQUNsRixzSUFBTUE7Z0JBQ0wrQixxQkFDRSw4REFBQ3dCO29CQUFJVixPQUFPO3dCQUFFVyxTQUFTO3dCQUFRQyxZQUFZO3dCQUFVTyxLQUFLO29CQUFPOztzQ0FDL0QsOERBQUNUOzRCQUFJVixPQUFPO2dDQUNWb0IsT0FBTztnQ0FDUEosUUFBUTtnQ0FDUkssY0FBYztnQ0FDZG5CLFlBQVk7Z0NBQ1pTLFNBQVM7Z0NBQ1RDLFlBQVk7Z0NBQ1pDLGdCQUFnQjs0QkFDbEI7c0NBQ0UsNEVBQUN4RCxvVkFBY0E7Z0NBQUMyQyxPQUFPO29DQUFFc0IsVUFBVTtvQ0FBUWhDLE9BQU87Z0NBQU87Ozs7Ozs7Ozs7O3NDQUUzRCw4REFBQ29COzs4Q0FDQyw4REFBQ0E7b0NBQUlWLE9BQU87d0NBQUVWLE9BQU87d0NBQVdrQyxZQUFZO3dDQUFPRixVQUFVO29DQUFPOzhDQUFHOzs7Ozs7OENBR3ZFLDhEQUFDWjtvQ0FBSVYsT0FBTzt3Q0FBRVYsT0FBTzt3Q0FBV2dDLFVBQVU7b0NBQU87OENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFNMURnQixXQUFVO2dCQUNWQyxTQUFTLElBQU16RCxrQkFBa0I7Z0JBQ2pDMEQsTUFBTTNEO2dCQUNOdUMsT0FBTztnQkFDUHFCLFFBQVE7b0JBQ05DLE1BQU07d0JBQUVyQyxTQUFTO29CQUFPO29CQUN4QnNDLFFBQVE7d0JBQUVsQyxjQUFjO3dCQUFxQm1DLGVBQWU7b0JBQU87Z0JBQ3JFOzBCQUVBLDRFQUFDbEM7b0JBQUlWLE9BQU87d0JBQUVXLFNBQVM7d0JBQVFrQyxlQUFlO3dCQUFVMUIsS0FBSztvQkFBTzs7c0NBQ2xFLDhEQUFDVDs0QkFBSVYsT0FBTztnQ0FBRThDLGNBQWM7NEJBQU87c0NBQ2pDLDRFQUFDbkU7Z0NBQUtxQixPQUFPO29DQUFFc0IsVUFBVTtvQ0FBUWhDLE9BQU87b0NBQVdrQyxZQUFZO2dDQUFNOzBDQUFHOzs7Ozs7Ozs7OztzQ0FLMUUsOERBQUM3RSxzSUFBTUE7NEJBQ0xnRixNQUFLOzRCQUNMb0IsS0FBSzs0QkFDTC9DLE9BQU87Z0NBQ0xnRCxXQUFXO2dDQUNYaEMsUUFBUTtnQ0FDUk0sVUFBVTtnQ0FDVkUsWUFBWTtnQ0FDWmxDLE9BQU87Z0NBQ1B1QixnQkFBZ0I7Z0NBQ2hCUSxjQUFjO2dDQUNkYyxRQUFROzRCQUNWOzRCQUNBUCxjQUFjLENBQUNDO2dDQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRztnQ0FDNUIyQixFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNnQyxXQUFXLEdBQUc7NEJBQy9COzRCQUNBRCxjQUFjLENBQUNGO2dDQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRztnQ0FDNUIyQixFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNnQyxXQUFXLEdBQUc7NEJBQy9COzs4Q0FFQSw4REFBQzFFLG9WQUFjQTtvQ0FBQzBDLE9BQU87d0NBQUVpRCxhQUFhO3dDQUFRM0QsT0FBTztvQ0FBVTs7Ozs7O2dDQUFLOzs7Ozs7O3NDQUl0RSw4REFBQzNDLHNJQUFNQTs0QkFDTGdGLE1BQUs7NEJBQ0xvQixLQUFLOzRCQUNML0MsT0FBTztnQ0FDTGdELFdBQVc7Z0NBQ1hoQyxRQUFRO2dDQUNSTSxVQUFVO2dDQUNWRSxZQUFZO2dDQUNabEMsT0FBTztnQ0FDUHVCLGdCQUFnQjtnQ0FDaEJRLGNBQWM7Z0NBQ2RjLFFBQVE7NEJBQ1Y7NEJBQ0FQLGNBQWMsQ0FBQ0M7Z0NBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHO2dDQUM1QjJCLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ2dDLFdBQVcsR0FBRzs0QkFDL0I7NEJBQ0FELGNBQWMsQ0FBQ0Y7Z0NBQ2JBLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ0UsVUFBVSxHQUFHO2dDQUM1QjJCLEVBQUVDLE1BQU0sQ0FBQzlCLEtBQUssQ0FBQ2dDLFdBQVcsR0FBRzs0QkFDL0I7OzhDQUVBLDhEQUFDM0QscVZBQWVBO29DQUFDMkIsT0FBTzt3Q0FBRWlELGFBQWE7d0NBQVEzRCxPQUFPO29DQUFVOzs7Ozs7Z0NBQUs7Ozs7Ozs7c0NBSXZFLDhEQUFDM0Msc0lBQU1BOzRCQUNMZ0YsTUFBSzs0QkFDTG9CLEtBQUs7NEJBQ0wvQyxPQUFPO2dDQUNMZ0QsV0FBVztnQ0FDWGhDLFFBQVE7Z0NBQ1JNLFVBQVU7Z0NBQ1ZFLFlBQVk7Z0NBQ1psQyxPQUFPO2dDQUNQdUIsZ0JBQWdCO2dDQUNoQlEsY0FBYztnQ0FDZGMsUUFBUTs0QkFDVjs0QkFDQVAsY0FBYyxDQUFDQztnQ0FDYkEsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDRSxVQUFVLEdBQUc7Z0NBQzVCMkIsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDZ0MsV0FBVyxHQUFHOzRCQUMvQjs0QkFDQUQsY0FBYyxDQUFDRjtnQ0FDYkEsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDRSxVQUFVLEdBQUc7Z0NBQzVCMkIsRUFBRUMsTUFBTSxDQUFDOUIsS0FBSyxDQUFDZ0MsV0FBVyxHQUFHOzRCQUMvQjs7OENBRUEsOERBQUM3RCxxVkFBY0E7b0NBQUM2QixPQUFPO3dDQUFFaUQsYUFBYTt3Q0FBUTNELE9BQU87b0NBQVU7Ozs7OztnQ0FBSzs7Ozs7OztzQ0FJdEUsOERBQUMzQyxzSUFBTUE7NEJBQ0xnRixNQUFLOzRCQUNMb0IsS0FBSzs0QkFDTC9DLE9BQU87Z0NBQ0xnRCxXQUFXO2dDQUNYaEMsUUFBUTtnQ0FDUk0sVUFBVTtnQ0FDVkUsWUFBWTtnQ0FDWmxDLE9BQU87Z0NBQ1B1QixnQkFBZ0I7Z0NBQ2hCUSxjQUFjO2dDQUNkYyxRQUFROzRCQUNWOzRCQUNBUCxjQUFjLENBQUNDO2dDQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRztnQ0FDNUIyQixFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNnQyxXQUFXLEdBQUc7NEJBQy9COzRCQUNBRCxjQUFjLENBQUNGO2dDQUNiQSxFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNFLFVBQVUsR0FBRztnQ0FDNUIyQixFQUFFQyxNQUFNLENBQUM5QixLQUFLLENBQUNnQyxXQUFXLEdBQUc7NEJBQy9COzs4Q0FFQSw4REFBQ3RFLHFWQUFXQTtvQ0FBQ3NDLE9BQU87d0NBQUVpRCxhQUFhO3dDQUFRM0QsT0FBTztvQ0FBVTs7Ozs7O2dDQUFLOzs7Ozs7O3NDQUluRSw4REFBQ2xDLHNJQUFPQTs0QkFBQzRDLE9BQU87Z0NBQUVlLFFBQVE7NEJBQVM7Ozs7OztzQ0FFbkMsOERBQUNMOzRCQUFJVixPQUFPO2dDQUFFOEMsY0FBYzs0QkFBTztzQ0FDakMsNEVBQUNuRTtnQ0FBS3FCLE9BQU87b0NBQUVzQixVQUFVO29DQUFRaEMsT0FBTztvQ0FBV2tDLFlBQVk7Z0NBQU07MENBQUc7Ozs7Ozs7Ozs7O3NDQUsxRSw4REFBQzlFLGtEQUFJQTs0QkFBQ3VFLE1BQUs7c0NBQ1QsNEVBQUN0RSxzSUFBTUE7Z0NBQ0xnRixNQUFLO2dDQUNMb0IsS0FBSztnQ0FDTC9DLE9BQU87b0NBQ0xnQixRQUFRO29DQUNSTSxVQUFVO29DQUNWRSxZQUFZO29DQUNabEMsT0FBTztvQ0FDUCtCLGNBQWM7b0NBQ2RjLFFBQVE7Z0NBQ1Y7MENBQ0Q7Ozs7Ozs7Ozs7O3NDQUtILDhEQUFDekYsa0RBQUlBOzRCQUFDdUUsTUFBSztzQ0FDVCw0RUFBQ3RFLHNJQUFNQTtnQ0FDTGdGLE1BQUs7Z0NBQ0xvQixLQUFLO2dDQUNML0MsT0FBTztvQ0FDTGdCLFFBQVE7b0NBQ1JNLFVBQVU7b0NBQ1ZFLFlBQVk7b0NBQ1p0QixZQUFZO29DQUNaOEIsYUFBYTtvQ0FDYlgsY0FBYztvQ0FDZDZCLFdBQVc7b0NBQ1g5QyxXQUFXO2dDQUNiOzBDQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9QLDhEQUFDN0I7O2tDQUVDLDhEQUFDbUM7d0JBQUlWLE9BQU87NEJBQ1ZFLFlBQVk7NEJBQ1pHLFNBQVM7NEJBQ1QyQyxXQUFXOzRCQUNYMUMsVUFBVTs0QkFDVjZDLFVBQVU7d0JBQ1o7OzBDQUVFLDhEQUFDekM7Z0NBQUlWLE9BQU87b0NBQ1ZNLFVBQVU7b0NBQ1ZDLEtBQUs7b0NBQ0w2QyxNQUFNO29DQUNOQyxPQUFPO29DQUNQQyxRQUFRO29DQUNSQyxpQkFBaUIsQ0FBQyxtUUFBbVEsQ0FBQztvQ0FDdFJDLFNBQVM7Z0NBQ1g7Ozs7OzswQ0FHQSw4REFBQzlDO2dDQUFJVixPQUFPO29DQUNWTSxVQUFVO29DQUNWQyxLQUFLO29DQUNMNkMsTUFBTTtvQ0FDTmhDLE9BQU87b0NBQ1BKLFFBQVE7b0NBQ1JLLGNBQWM7b0NBQ2RuQixZQUFZO29DQUNadUQsUUFBUTtnQ0FDVjs7Ozs7OzBDQUNBLDhEQUFDL0M7Z0NBQUlWLE9BQU87b0NBQ1ZNLFVBQVU7b0NBQ1ZDLEtBQUs7b0NBQ0w4QyxPQUFPO29DQUNQakMsT0FBTztvQ0FDUEosUUFBUTtvQ0FDUkssY0FBYztvQ0FDZG5CLFlBQVk7b0NBQ1p1RCxRQUFRO2dDQUNWOzs7Ozs7MENBRUEsOERBQUMvQztnQ0FBSVYsT0FBTztvQ0FBRWMsVUFBVTtvQ0FBVUMsUUFBUTtvQ0FBVVQsVUFBVTtvQ0FBWUUsUUFBUTtnQ0FBRTs7a0RBRWxGLDhEQUFDRTt3Q0FBSVYsT0FBTzs0Q0FBRThDLGNBQWM7d0NBQU87a0RBQ2pDLDRFQUFDcEM7NENBQUlWLE9BQU87Z0RBQ1ZXLFNBQVM7Z0RBQ1RDLFlBQVk7Z0RBQ1pPLEtBQUs7Z0RBQ0xqQixZQUFZO2dEQUNaQyxnQkFBZ0I7Z0RBQ2hCZ0MsUUFBUTtnREFDUmQsY0FBYztnREFDZGhCLFNBQVM7Z0RBQ1R5QyxjQUFjOzRDQUNoQjs7OERBQ0UsOERBQUNwQztvREFBSVYsT0FBTzt3REFDVlcsU0FBUzt3REFDVEMsWUFBWTt3REFDWk8sS0FBSztvREFDUDs7c0VBQ0UsOERBQUNUOzREQUFJVixPQUFPO2dFQUNWb0IsT0FBTztnRUFDUEosUUFBUTtnRUFDUkssY0FBYztnRUFDZG5CLFlBQVk7Z0VBQ1pFLFdBQVc7Z0VBQ1hzRCxXQUFXOzREQUNiOzs7Ozs7c0VBQ0EsOERBQUMvRTs0REFBS3FCLE9BQU87Z0VBQ1hWLE9BQU87Z0VBQ1BnQyxVQUFVO2dFQUNWRSxZQUFZOzREQUNkO3NFQUFHOzs7Ozs7Ozs7Ozs7OERBSUwsOERBQUNwRSxzSUFBT0E7b0RBQUN1RSxNQUFLO29EQUFXM0IsT0FBTzt3REFBRWdDLGFBQWE7d0RBQXlCaEIsUUFBUTtvREFBTzs7Ozs7OzhEQUN2Riw4REFBQ047b0RBQUlWLE9BQU87d0RBQ1ZXLFNBQVM7d0RBQ1RDLFlBQVk7d0RBQ1pPLEtBQUs7b0RBQ1A7O3NFQUNFLDhEQUFDaEQscVZBQWNBOzREQUFDNkIsT0FBTztnRUFBRVYsT0FBTztnRUFBV2dDLFVBQVU7NERBQU87Ozs7OztzRUFDNUQsOERBQUMzQzs0REFBS3FCLE9BQU87Z0VBQ1hWLE9BQU87Z0VBQ1BnQyxVQUFVO2dFQUNWRSxZQUFZOzREQUNkO3NFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFRVCw4REFBQy9DO3dDQUFNOEMsT0FBTzt3Q0FBR3ZCLE9BQU87NENBQ3RCc0IsVUFBVTs0Q0FDVndCLGNBQWM7NENBQ2R4RCxPQUFPOzRDQUNQa0MsWUFBWTs0Q0FDWkMsWUFBWTs0Q0FDWmtDLGVBQWU7NENBQ2ZDLFlBQVk7d0NBQ2Q7OzRDQUFHOzBEQUVELDhEQUFDQzs7Ozs7MERBQ0QsOERBQUNsRjtnREFBS3FCLE9BQU87b0RBQ1hFLFlBQVk7b0RBQ1o0RCxzQkFBc0I7b0RBQ3RCQyxxQkFBcUI7b0RBQ3JCQyxnQkFBZ0I7b0RBQ2hCeEMsWUFBWTtnREFDZDswREFBRzs7Ozs7Ozs7Ozs7O2tEQU1MLDhEQUFDOUM7d0NBQVVzQixPQUFPOzRDQUNoQnNCLFVBQVU7NENBQ1ZoQyxPQUFPOzRDQUNQd0QsY0FBYzs0Q0FDZGhDLFVBQVU7NENBQ1ZDLFFBQVE7NENBQ1JVLFlBQVk7NENBQ1pELFlBQVk7d0NBQ2Q7OzRDQUFHOzBEQUVELDhEQUFDcUM7Ozs7OzBEQUNELDhEQUFDbEY7Z0RBQUtxQixPQUFPO29EQUFFVixPQUFPO29EQUFXa0MsWUFBWTtnREFBTTswREFBRzs7Ozs7OzRDQUFtQjswREFDekUsOERBQUM3QztnREFBS3FCLE9BQU87b0RBQUVWLE9BQU87b0RBQVdrQyxZQUFZO2dEQUFNOzBEQUFHOzs7Ozs7NENBQWU7MERBQ3JFLDhEQUFDN0M7Z0RBQUtxQixPQUFPO29EQUFFVixPQUFPO29EQUFXa0MsWUFBWTtnREFBTTswREFBRzs7Ozs7Ozs7Ozs7O2tEQUl4RCw4REFBQ2Q7d0NBQUlWLE9BQU87NENBQUU4QyxjQUFjO3dDQUFPOzswREFDakMsOERBQUM5RixzSUFBS0E7Z0RBQUMwRSxNQUFLO2dEQUFRdUMsSUFBSTs7a0VBQ3RCLDhEQUFDdkgsa0RBQUlBO3dEQUFDdUUsTUFBSztrRUFDVCw0RUFBQ3RFLHNJQUFNQTs0REFDTGdGLE1BQUs7NERBQ0xELE1BQUs7NERBQ0xyQyxvQkFBTSw4REFBQ3RCLHFWQUFjQTs7Ozs7NERBQ3JCaUMsT0FBTztnRUFDTGdCLFFBQVE7Z0VBQ1JNLFVBQVU7Z0VBQ1ZFLFlBQVk7Z0VBQ1p0QixZQUFZO2dFQUNaOEIsYUFBYTtnRUFDYlgsY0FBYztnRUFDZFksYUFBYTtnRUFDYkMsY0FBYztnRUFDZDlCLFdBQVc7Z0VBQ1grQixRQUFRO2dFQUNSK0IsVUFBVTs0REFDWjtzRUFDRDs7Ozs7Ozs7Ozs7a0VBSUgsOERBQUN2SCxzSUFBTUE7d0RBQ0wrRSxNQUFLO3dEQUNMckMsb0JBQU0sOERBQUMxQixxVkFBa0JBOzs7Ozt3REFDekJxQyxPQUFPOzREQUNMZ0IsUUFBUTs0REFDUk0sVUFBVTs0REFDVkUsWUFBWTs0REFDWnRCLFlBQVk7NERBQ1ppQyxRQUFROzREQUNSN0MsT0FBTzs0REFDUGEsZ0JBQWdCOzREQUNoQmtCLGNBQWM7NERBQ2RZLGFBQWE7NERBQ2JDLGNBQWM7NERBQ2RnQyxVQUFVO3dEQUNaO2tFQUNEOzs7Ozs7Ozs7Ozs7MERBTUgsOERBQUN4RDtnREFBSVYsT0FBTztvREFBRWtELFdBQVc7Z0RBQU87O2tFQUM5Qiw4REFBQ3ZFO3dEQUFLcUIsT0FBTzs0REFDWFYsT0FBTzs0REFDUGdDLFVBQVU7NERBQ1ZYLFNBQVM7NERBQ1RtQyxjQUFjO3dEQUNoQjtrRUFBRzs7Ozs7O2tFQUdILDhEQUFDOUYsc0lBQUtBO3dEQUFDMEUsTUFBSzt3REFBUXVDLElBQUk7OzBFQUN0Qiw4REFBQ3ZEO2dFQUFJVixPQUFPO29FQUFFZ0QsV0FBVztnRUFBUzs7a0ZBQ2hDLDhEQUFDckU7d0VBQUtxQixPQUFPOzRFQUFFVixPQUFPOzRFQUFXZ0MsVUFBVTs0RUFBUUUsWUFBWTs0RUFBT2IsU0FBUzt3RUFBUTtrRkFBRzs7Ozs7O2tGQUcxRiw4REFBQ2hDO3dFQUFLcUIsT0FBTzs0RUFBRVYsT0FBTzs0RUFBeUJnQyxVQUFVO3dFQUFPO2tGQUFHOzs7Ozs7Ozs7Ozs7MEVBSXJFLDhEQUFDWjtnRUFBSVYsT0FBTztvRUFBRWdELFdBQVc7Z0VBQVM7O2tGQUNoQyw4REFBQ3JFO3dFQUFLcUIsT0FBTzs0RUFBRVYsT0FBTzs0RUFBV2dDLFVBQVU7NEVBQVFFLFlBQVk7NEVBQU9iLFNBQVM7d0VBQVE7a0ZBQUc7Ozs7OztrRkFHMUYsOERBQUNoQzt3RUFBS3FCLE9BQU87NEVBQUVWLE9BQU87NEVBQXlCZ0MsVUFBVTt3RUFBTztrRkFBRzs7Ozs7Ozs7Ozs7OzBFQUlyRSw4REFBQ1o7Z0VBQUlWLE9BQU87b0VBQUVnRCxXQUFXO2dFQUFTOztrRkFDaEMsOERBQUNyRTt3RUFBS3FCLE9BQU87NEVBQUVWLE9BQU87NEVBQVdnQyxVQUFVOzRFQUFRRSxZQUFZOzRFQUFPYixTQUFTO3dFQUFRO2tGQUFHOzs7Ozs7a0ZBRzFGLDhEQUFDaEM7d0VBQUtxQixPQUFPOzRFQUFFVixPQUFPOzRFQUF5QmdDLFVBQVU7d0VBQU87a0ZBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFTM0UsOERBQUNaO3dDQUFJVixPQUFPOzRDQUNWRSxZQUFZOzRDQUNaQyxnQkFBZ0I7NENBQ2hCZ0MsUUFBUTs0Q0FDUmQsY0FBYzs0Q0FDZGhCLFNBQVM7NENBQ1R5QyxjQUFjO3dDQUNoQjtrREFDRSw0RUFBQ2pHLHNJQUFHQTs0Q0FBQ3NILFFBQVE7Z0RBQUM7Z0RBQUk7NkNBQUc7c0RBQ2xCbEYsTUFBTW1GLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDaEIsOERBQUN4SCxzSUFBR0E7b0RBQUN5SCxJQUFJO29EQUFJQyxJQUFJOzhEQUNmLDRFQUFDOUQ7d0RBQ0NWLE9BQU87NERBQ0xnRCxXQUFXOzREQUNYOUMsWUFBWTs0REFDWkMsZ0JBQWdCOzREQUNoQmdDLFFBQVE7NERBQ1JkLGNBQWM7NERBQ2RoQixTQUFTOzREQUNUb0UsWUFBWTs0REFDWkMsUUFBUTs0REFDUnBFLFVBQVU7NERBQ1Y2QyxVQUFVO3dEQUNaO3dEQUNBdkIsY0FBYyxDQUFDQzs0REFDYkEsRUFBRThDLGFBQWEsQ0FBQzNFLEtBQUssQ0FBQzRFLFNBQVMsR0FBRzs0REFDbEMvQyxFQUFFOEMsYUFBYSxDQUFDM0UsS0FBSyxDQUFDRSxVQUFVLEdBQUc7NERBQ25DMkIsRUFBRThDLGFBQWEsQ0FBQzNFLEtBQUssQ0FBQ2dDLFdBQVcsR0FBRzt3REFDdEM7d0RBQ0FELGNBQWMsQ0FBQ0Y7NERBQ2JBLEVBQUU4QyxhQUFhLENBQUMzRSxLQUFLLENBQUM0RSxTQUFTLEdBQUc7NERBQ2xDL0MsRUFBRThDLGFBQWEsQ0FBQzNFLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzREQUNuQzJCLEVBQUU4QyxhQUFhLENBQUMzRSxLQUFLLENBQUNnQyxXQUFXLEdBQUc7d0RBQ3RDOzswRUFHQSw4REFBQ3RCO2dFQUFJVixPQUFPO29FQUNWTSxVQUFVO29FQUNWQyxLQUFLO29FQUNMNkMsTUFBTTtvRUFDTndCLFdBQVc7b0VBQ1h4RCxPQUFPO29FQUNQSixRQUFRO29FQUNSSyxjQUFjO29FQUNkbkIsWUFBWSxDQUFDLHdCQUF3QixFQUFFbUUsS0FBSy9FLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQztvRUFDMUVtRSxRQUFRO29FQUNSakQsUUFBUTtnRUFDVjs7Ozs7OzBFQUVBLDhEQUFDRTtnRUFBSVYsT0FBTztvRUFBRU0sVUFBVTtvRUFBWUUsUUFBUTtnRUFBRTs7a0ZBRTVDLDhEQUFDRTt3RUFBSVYsT0FBTzs0RUFDVjhDLGNBQWM7NEVBQ2RuQyxTQUFTOzRFQUNURSxnQkFBZ0I7NEVBQ2hCRCxZQUFZO3dFQUNkO2tGQUNFLDRFQUFDRjs0RUFBSVYsT0FBTztnRkFDVm9CLE9BQU87Z0ZBQ1BKLFFBQVE7Z0ZBQ1JLLGNBQWM7Z0ZBQ2RuQixZQUFZLENBQUMsd0JBQXdCLEVBQUVtRSxLQUFLL0UsS0FBSyxDQUFDLElBQUksRUFBRStFLEtBQUsvRSxLQUFLLENBQUMsR0FBRyxDQUFDO2dGQUN2RXFCLFNBQVM7Z0ZBQ1RDLFlBQVk7Z0ZBQ1pDLGdCQUFnQjtnRkFDaEJzQixRQUFRLENBQUMsVUFBVSxFQUFFa0MsS0FBSy9FLEtBQUssQ0FBQyxFQUFFLENBQUM7Z0ZBQ25DYyxXQUFXLENBQUMsV0FBVyxFQUFFaUUsS0FBSy9FLEtBQUssQ0FBQyxFQUFFLENBQUM7NEVBQ3pDO3NGQUNFLDRFQUFDdUY7Z0ZBQUs3RSxPQUFPO29GQUNYc0IsVUFBVTtvRkFDVmhDLE9BQU8rRSxLQUFLL0UsS0FBSztvRkFDakJtRSxRQUFRO2dGQUNWOzBGQUNHWSxLQUFLaEYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztrRkFNaEIsOERBQUNxQjt3RUFBSVYsT0FBTzs0RUFBRThDLGNBQWM7d0VBQU87a0ZBQ2pDLDRFQUFDbkU7NEVBQUtxQixPQUFPO2dGQUNYVixPQUFPO2dGQUNQZ0MsVUFBVTtnRkFDVkUsWUFBWTtnRkFDWkMsWUFBWTtnRkFDWmQsU0FBUzs0RUFDWDs7Z0ZBQ0cwRCxLQUFLbEYsS0FBSztnRkFBRWtGLEtBQUtqRixNQUFNOzs7Ozs7Ozs7Ozs7a0ZBSzVCLDhEQUFDc0I7d0VBQUlWLE9BQU87NEVBQUU4QyxjQUFjO3dFQUFNO2tGQUNoQyw0RUFBQ25FOzRFQUFLcUIsT0FBTztnRkFDWFYsT0FBTztnRkFDUGdDLFVBQVU7Z0ZBQ1ZFLFlBQVk7Z0ZBQ1piLFNBQVM7NEVBQ1g7c0ZBQ0cwRCxLQUFLbkYsS0FBSzs7Ozs7Ozs7Ozs7a0ZBS2YsOERBQUNQO3dFQUFLcUIsT0FBTzs0RUFDWFYsT0FBTzs0RUFDUGdDLFVBQVU7NEVBQ1ZHLFlBQVk7d0VBQ2Q7a0ZBQ0c0QyxLQUFLOUUsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7O21EQW5HQStFOzs7Ozs7Ozs7Ozs7Ozs7a0RBNkcvQiw4REFBQ3RILHNJQUFLQTt3Q0FBQzBFLE1BQUs7d0NBQVF1QyxJQUFJOzswREFDdEIsOERBQUN2SCxrREFBSUE7Z0RBQUN1RSxNQUFLOzBEQUNULDRFQUFDdEUsc0lBQU1BO29EQUNMZ0YsTUFBSztvREFDTEQsTUFBSztvREFDTHJDLG9CQUFNLDhEQUFDMUIscVZBQWtCQTs7Ozs7b0RBQ3pCcUMsT0FBTzt3REFBRWdCLFFBQVE7d0RBQVFYLFNBQVM7d0RBQVVpQixVQUFVO29EQUFPOzhEQUM5RDs7Ozs7Ozs7Ozs7MERBSUgsOERBQUMzRSxzSUFBTUE7Z0RBQ0wrRSxNQUFLO2dEQUNMckMsb0JBQU0sOERBQUN6QixxVkFBV0E7Ozs7O2dEQUNsQm9DLE9BQU87b0RBQUVnQixRQUFRO29EQUFRWCxTQUFTO29EQUFVaUIsVUFBVTtnREFBTzswREFDOUQ7Ozs7Ozs7Ozs7OztrREFNSCw4REFBQ1o7d0NBQUlWLE9BQU87NENBQUVrRCxXQUFXO3dDQUFPO2tEQUM5Qiw0RUFBQ2xHLHNJQUFLQTs0Q0FBQzBFLE1BQUs7NENBQVF1QyxJQUFJOzs4REFDdEIsOERBQUN0RjtvREFBS2dELE1BQUs7O3NFQUNULDhEQUFDOUQscVZBQW1CQTs0REFBQ21DLE9BQU87Z0VBQUVWLE9BQU87Z0VBQVcyRCxhQUFhOzREQUFNOzs7Ozs7d0RBQUs7Ozs7Ozs7OERBRzFFLDhEQUFDdEU7b0RBQUtnRCxNQUFLOztzRUFDVCw4REFBQzlELHFWQUFtQkE7NERBQUNtQyxPQUFPO2dFQUFFVixPQUFPO2dFQUFXMkQsYUFBYTs0REFBTTs7Ozs7O3dEQUFLOzs7Ozs7OzhEQUcxRSw4REFBQ3RFO29EQUFLZ0QsTUFBSzs7c0VBQ1QsOERBQUM5RCxxVkFBbUJBOzREQUFDbUMsT0FBTztnRUFBRVYsT0FBTztnRUFBVzJELGFBQWE7NERBQU07Ozs7Ozt3REFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVNsRiw4REFBQ3ZDO3dCQUFJVixPQUFPOzRCQUFFSyxTQUFTOzRCQUFhSCxZQUFZO3dCQUFPO2tDQUNyRCw0RUFBQ1E7NEJBQUlWLE9BQU87Z0NBQUVjLFVBQVU7Z0NBQVVDLFFBQVE7NEJBQVM7OzhDQUNqRCw4REFBQ0w7b0NBQUlWLE9BQU87d0NBQUVnRCxXQUFXO3dDQUFVRixjQUFjO29DQUFPOztzREFDdEQsOERBQUNyRTs0Q0FBTThDLE9BQU87NENBQUd2QixPQUFPO2dEQUFFc0IsVUFBVTtnREFBVXdCLGNBQWM7NENBQU87c0RBQUc7Ozs7OztzREFHdEUsOERBQUNwRTs0Q0FBVXNCLE9BQU87Z0RBQ2hCc0IsVUFBVTtnREFDVmhDLE9BQU87Z0RBQ1B3QixVQUFVO2dEQUNWQyxRQUFROzRDQUNWO3NEQUFHOzs7Ozs7Ozs7Ozs7OENBS0wsOERBQUNsRSxzSUFBR0E7b0NBQUNzSCxRQUFRO3dDQUFDO3dDQUFJO3FDQUFHOztzREFDbkIsOERBQUNySCxzSUFBR0E7NENBQUN5SCxJQUFJOzRDQUFJTyxJQUFJOzRDQUFJTixJQUFJO3NEQUN2Qiw0RUFBQzVILHNJQUFJQTtnREFDSG1JLFNBQVM7Z0RBQ1QvRSxPQUFPO29EQUFFZ0IsUUFBUTtvREFBUWdDLFdBQVc7Z0RBQVM7Z0RBQzdDZ0MsV0FBVztvREFBRTNFLFNBQVM7Z0RBQVk7O2tFQUVsQyw4REFBQ0s7d0RBQUlWLE9BQU87NERBQUU4QyxjQUFjO3dEQUFPO2tFQUNqQyw0RUFBQ3hGLG9WQUFjQTs0REFBQzBDLE9BQU87Z0VBQUVzQixVQUFVO2dFQUFRaEMsT0FBTzs0REFBVTs7Ozs7Ozs7Ozs7a0VBRTlELDhEQUFDYjt3REFBTThDLE9BQU87d0RBQUd2QixPQUFPOzREQUFFOEMsY0FBYzt3REFBTztrRUFBRzs7Ozs7O2tFQUdsRCw4REFBQ3BFO3dEQUFVc0IsT0FBTzs0REFBRVYsT0FBTzs0REFBV21DLFlBQVk7d0RBQU07a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU0vRCw4REFBQzNFLHNJQUFHQTs0Q0FBQ3lILElBQUk7NENBQUlPLElBQUk7NENBQUlOLElBQUk7c0RBQ3ZCLDRFQUFDNUgsc0lBQUlBO2dEQUNIbUksU0FBUztnREFDVC9FLE9BQU87b0RBQUVnQixRQUFRO29EQUFRZ0MsV0FBVztnREFBUztnREFDN0NnQyxXQUFXO29EQUFFM0UsU0FBUztnREFBWTs7a0VBRWxDLDhEQUFDSzt3REFBSVYsT0FBTzs0REFBRThDLGNBQWM7d0RBQU87a0VBQ2pDLDRFQUFDekYsb1ZBQWNBOzREQUFDMkMsT0FBTztnRUFBRXNCLFVBQVU7Z0VBQVFoQyxPQUFPOzREQUFVOzs7Ozs7Ozs7OztrRUFFOUQsOERBQUNiO3dEQUFNOEMsT0FBTzt3REFBR3ZCLE9BQU87NERBQUU4QyxjQUFjO3dEQUFPO2tFQUFHOzs7Ozs7a0VBR2xELDhEQUFDcEU7d0RBQVVzQixPQUFPOzREQUFFVixPQUFPOzREQUFXbUMsWUFBWTt3REFBTTtrRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTS9ELDhEQUFDM0Usc0lBQUdBOzRDQUFDeUgsSUFBSTs0Q0FBSU8sSUFBSTs0Q0FBSU4sSUFBSTtzREFDdkIsNEVBQUM1SCxzSUFBSUE7Z0RBQ0htSSxTQUFTO2dEQUNUL0UsT0FBTztvREFBRWdCLFFBQVE7b0RBQVFnQyxXQUFXO2dEQUFTO2dEQUM3Q2dDLFdBQVc7b0RBQUUzRSxTQUFTO2dEQUFZOztrRUFFbEMsOERBQUNLO3dEQUFJVixPQUFPOzREQUFFOEMsY0FBYzt3REFBTztrRUFDakMsNEVBQUN2RixxVkFBbUJBOzREQUFDeUMsT0FBTztnRUFBRXNCLFVBQVU7Z0VBQVFoQyxPQUFPOzREQUFVOzs7Ozs7Ozs7OztrRUFFbkUsOERBQUNiO3dEQUFNOEMsT0FBTzt3REFBR3ZCLE9BQU87NERBQUU4QyxjQUFjO3dEQUFPO2tFQUFHOzs7Ozs7a0VBR2xELDhEQUFDcEU7d0RBQVVzQixPQUFPOzREQUFFVixPQUFPOzREQUFXbUMsWUFBWTt3REFBTTtrRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTS9ELDhEQUFDM0Usc0lBQUdBOzRDQUFDeUgsSUFBSTs0Q0FBSU8sSUFBSTs0Q0FBSU4sSUFBSTtzREFDdkIsNEVBQUM1SCxzSUFBSUE7Z0RBQ0htSSxTQUFTO2dEQUNUL0UsT0FBTztvREFBRWdCLFFBQVE7b0RBQVFnQyxXQUFXO2dEQUFTO2dEQUM3Q2dDLFdBQVc7b0RBQUUzRSxTQUFTO2dEQUFZOztrRUFFbEMsOERBQUNLO3dEQUFJVixPQUFPOzREQUFFOEMsY0FBYzt3REFBTztrRUFDakMsNEVBQUN0RixxVkFBWUE7NERBQUN3QyxPQUFPO2dFQUFFc0IsVUFBVTtnRUFBUWhDLE9BQU87NERBQVU7Ozs7Ozs7Ozs7O2tFQUU1RCw4REFBQ2I7d0RBQU04QyxPQUFPO3dEQUFHdkIsT0FBTzs0REFBRThDLGNBQWM7d0RBQU87a0VBQUc7Ozs7OztrRUFHbEQsOERBQUNwRTt3REFBVXNCLE9BQU87NERBQUVWLE9BQU87NERBQVdtQyxZQUFZO3dEQUFNO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztzREFNL0QsOERBQUMzRSxzSUFBR0E7NENBQUN5SCxJQUFJOzRDQUFJTyxJQUFJOzRDQUFJTixJQUFJO3NEQUN2Qiw0RUFBQzVILHNJQUFJQTtnREFDSG1JLFNBQVM7Z0RBQ1QvRSxPQUFPO29EQUFFZ0IsUUFBUTtvREFBUWdDLFdBQVc7Z0RBQVM7Z0RBQzdDZ0MsV0FBVztvREFBRTNFLFNBQVM7Z0RBQVk7O2tFQUVsQyw4REFBQ0s7d0RBQUlWLE9BQU87NERBQUU4QyxjQUFjO3dEQUFPO2tFQUNqQyw0RUFBQ3JGLHFWQUFnQkE7NERBQUN1QyxPQUFPO2dFQUFFc0IsVUFBVTtnRUFBUWhDLE9BQU87NERBQVU7Ozs7Ozs7Ozs7O2tFQUVoRSw4REFBQ2I7d0RBQU04QyxPQUFPO3dEQUFHdkIsT0FBTzs0REFBRThDLGNBQWM7d0RBQU87a0VBQUc7Ozs7OztrRUFHbEQsOERBQUNwRTt3REFBVXNCLE9BQU87NERBQUVWLE9BQU87NERBQVdtQyxZQUFZO3dEQUFNO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztzREFNL0QsOERBQUMzRSxzSUFBR0E7NENBQUN5SCxJQUFJOzRDQUFJTyxJQUFJOzRDQUFJTixJQUFJO3NEQUN2Qiw0RUFBQzVILHNJQUFJQTtnREFDSG1JLFNBQVM7Z0RBQ1QvRSxPQUFPO29EQUFFZ0IsUUFBUTtvREFBUWdDLFdBQVc7Z0RBQVM7Z0RBQzdDZ0MsV0FBVztvREFBRTNFLFNBQVM7Z0RBQVk7O2tFQUVsQyw4REFBQ0s7d0RBQUlWLE9BQU87NERBQUU4QyxjQUFjO3dEQUFPO2tFQUNqQyw0RUFBQ3BGLHFWQUFXQTs0REFBQ3NDLE9BQU87Z0VBQUVzQixVQUFVO2dFQUFRaEMsT0FBTzs0REFBVTs7Ozs7Ozs7Ozs7a0VBRTNELDhEQUFDYjt3REFBTThDLE9BQU87d0RBQUd2QixPQUFPOzREQUFFOEMsY0FBYzt3REFBTztrRUFBRzs7Ozs7O2tFQUdsRCw4REFBQ3BFO3dEQUFVc0IsT0FBTzs0REFBRVYsT0FBTzs0REFBV21DLFlBQVk7d0RBQU07a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBVXJFLDhEQUFDZjt3QkFBSVYsT0FBTzs0QkFDVkUsWUFBWTs0QkFDWkcsU0FBUzs0QkFDVDJDLFdBQVc7NEJBQ1gxRCxPQUFPO3dCQUNUO2tDQUNFLDRFQUFDb0I7NEJBQUlWLE9BQU87Z0NBQUVjLFVBQVU7Z0NBQVNDLFFBQVE7NEJBQVM7OzhDQUNoRCw4REFBQ3RDO29DQUFNOEMsT0FBTztvQ0FBR3ZCLE9BQU87d0NBQUVWLE9BQU87d0NBQVNnQyxVQUFVO3dDQUFVd0IsY0FBYztvQ0FBTzs4Q0FBRzs7Ozs7OzhDQUd0Riw4REFBQ3BFO29DQUFVc0IsT0FBTzt3Q0FDaEJzQixVQUFVO3dDQUNWd0IsY0FBYzt3Q0FDZFUsU0FBUzt3Q0FDVGxFLE9BQU87b0NBQ1Q7OENBQUc7Ozs7Ozs4Q0FHSCw4REFBQ3RDLHNJQUFLQTtvQ0FBQzBFLE1BQUs7O3NEQUNWLDhEQUFDaEYsa0RBQUlBOzRDQUFDdUUsTUFBSztzREFDVCw0RUFBQ3RFLHNJQUFNQTtnREFDTCtFLE1BQUs7Z0RBQ0wxQixPQUFPO29EQUNMZ0IsUUFBUTtvREFDUlgsU0FBUztvREFDVGlCLFVBQVU7b0RBQ1ZwQixZQUFZO29EQUNaWixPQUFPO29EQUNQNkMsUUFBUTtnREFDVjswREFDRDs7Ozs7Ozs7Ozs7c0RBSUgsOERBQUN4RixzSUFBTUE7NENBQ0wrRSxNQUFLOzRDQUNMdUQsS0FBSzs0Q0FDTGpGLE9BQU87Z0RBQ0xnQixRQUFRO2dEQUNSWCxTQUFTO2dEQUNUaUIsVUFBVTtnREFDVlUsYUFBYTtnREFDYjFDLE9BQU87NENBQ1Q7c0RBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNULDhEQUFDZDtnQkFBT3dCLE9BQU87b0JBQ2JFLFlBQVk7b0JBQ1paLE9BQU87b0JBQ1BlLFNBQVM7Z0JBQ1g7MEJBQ0UsNEVBQUNLO29CQUFJVixPQUFPO3dCQUFFYyxVQUFVO3dCQUFVQyxRQUFRO3dCQUFVaUMsV0FBVztvQkFBUzs7c0NBQ3RFLDhEQUFDdEM7NEJBQUlWLE9BQU87Z0NBQ1ZXLFNBQVM7Z0NBQ1RDLFlBQVk7Z0NBQ1pDLGdCQUFnQjtnQ0FDaEJNLEtBQUs7Z0NBQ0wyQixjQUFjOzRCQUNoQjs7OENBQ0UsOERBQUM1RixzSUFBTUE7b0NBQ0x3RSxNQUFNO29DQUNOMUIsT0FBTzt3Q0FBRWtGLGlCQUFpQjtvQ0FBVTtvQ0FDcEM3RixvQkFBTSw4REFBQ2hDLG9WQUFjQTs7Ozs7Ozs7Ozs4Q0FFdkIsOERBQUNxRDs7c0RBQ0MsOERBQUNqQzs0Q0FBTThDLE9BQU87NENBQUd2QixPQUFPO2dEQUFFVixPQUFPO2dEQUFXeUIsUUFBUTs0Q0FBRTtzREFBRzs7Ozs7O3NEQUd6RCw4REFBQ3BDOzRDQUFLcUIsT0FBTztnREFBRVYsT0FBTztnREFBV2dDLFVBQVU7NENBQU87c0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNekQsOERBQUM1Qzs0QkFBVXNCLE9BQU87Z0NBQUVWLE9BQU87Z0NBQVd3RCxjQUFjOzRCQUFPO3NDQUFHOzs7Ozs7c0NBSTlELDhEQUFDbkU7NEJBQUtxQixPQUFPO2dDQUFFVixPQUFPO2dDQUFXZ0MsVUFBVTs0QkFBTztzQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0QiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQge1xuICBCdXR0b24sXG4gIENhcmQsXG4gIFJvdyxcbiAgQ29sLFxuICBUeXBvZ3JhcGh5LFxuICBTcGFjZSxcbiAgU3RhdGlzdGljLFxuICBMYXlvdXQsXG4gIEF2YXRhcixcbiAgRHJhd2VyLFxuICBCYWRnZSxcbiAgRGl2aWRlcixcbiAgRmxleCxcbiAgU3RlcHMsXG4gIFRpbWVsaW5lLFxuICBUYWJzLFxuICBMaXN0LFxuICBUYWdcbn0gZnJvbSAnYW50ZCc7XG5pbXBvcnQge1xuICBTYWZldHlPdXRsaW5lZCxcbiAgR2xvYmFsT3V0bGluZWQsXG4gIFRodW5kZXJib2x0T3V0bGluZWQsXG4gIExvY2tPdXRsaW5lZCxcbiAgQmFyQ2hhcnRPdXRsaW5lZCxcbiAgQXBpT3V0bGluZWQsXG4gIFBsYXlDaXJjbGVPdXRsaW5lZCxcbiAgRXllT3V0bGluZWQsXG4gIENoZWNrQ2lyY2xlT3V0bGluZWQsXG4gIE1lbnVPdXRsaW5lZCxcbiAgU3RhckZpbGxlZCxcbiAgUm9ja2V0T3V0bGluZWQsXG4gIFNlY3VyaXR5U2Nhbk91dGxpbmVkLFxuICBDbG91ZE91dGxpbmVkLFxuICBEYXNoYm9hcmRPdXRsaW5lZCxcbiAgVGVhbU91dGxpbmVkLFxuICBUcm9waHlPdXRsaW5lZCxcbiAgQXJyb3dSaWdodE91dGxpbmVkLFxuICBDaGVja091dGxpbmVkLFxuICBTZXR0aW5nT3V0bGluZWQsXG4gIE1vbml0b3JPdXRsaW5lZCxcbiAgRGF0YWJhc2VPdXRsaW5lZFxufSBmcm9tICdAYW50LWRlc2lnbi9pY29ucyc7XG5cbmNvbnN0IHsgSGVhZGVyLCBDb250ZW50LCBGb290ZXIgfSA9IExheW91dDtcbmNvbnN0IHsgVGl0bGUsIFBhcmFncmFwaCwgVGV4dCB9ID0gVHlwb2dyYXBoeTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW21vYmlsZU1lbnVPcGVuLCBzZXRNb2JpbGVNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZSgnMScpO1xuXG4gIC8vIOaguOW/g+e7n+iuoeaVsOaNrlxuICBjb25zdCBzdGF0cyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogJ+WFqOeQg+iKgueCuScsXG4gICAgICB2YWx1ZTogNTAsXG4gICAgICBzdWZmaXg6ICcrJyxcbiAgICAgIGljb246IDxHbG9iYWxPdXRsaW5lZCAvPixcbiAgICAgIGNvbG9yOiAnIzNiODJmNicsXG4gICAgICBkZXNjcmlwdGlvbjogJ+imhuebluWFqOeQg+S4u+imgeWbveWutuWSjOWcsOWMuidcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5LyB5Lia55So5oi3JyxcbiAgICAgIHZhbHVlOiAxMDAwMCxcbiAgICAgIHN1ZmZpeDogJysnLFxuICAgICAgaWNvbjogPFRlYW1PdXRsaW5lZCAvPixcbiAgICAgIGNvbG9yOiAnIzEwYjk4MScsXG4gICAgICBkZXNjcmlwdGlvbjogJ+acjeWKoeWFqOeQg+S8geS4muWuouaItydcbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn56iz5a6a5oCnJyxcbiAgICAgIHZhbHVlOiA5OS45LFxuICAgICAgc3VmZml4OiAnJScsXG4gICAgICBpY29uOiA8U2FmZXR5T3V0bGluZWQgLz4sXG4gICAgICBjb2xvcjogJyNmNTllMGInLFxuICAgICAgZGVzY3JpcHRpb246ICdTTEEg5pyN5Yqh562J57qn5L+d6K+BJ1xuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfmioDmnK/mlK/mjIEnLFxuICAgICAgdmFsdWU6ICcyNC83JyxcbiAgICAgIGljb246IDxTYWZldHlPdXRsaW5lZCAvPixcbiAgICAgIGNvbG9yOiAnIzhiNWNmNicsXG4gICAgICBkZXNjcmlwdGlvbjogJ+WFqOWkqeWAmeS4k+S4muaKgOacr+aUr+aMgSdcbiAgICB9XG4gIF07XG5cbiAgLy8g5qC45b+D5Yqf6IO954m55oCnXG4gIGNvbnN0IGZlYXR1cmVzID0gW1xuICAgIHtcbiAgICAgIGljb246IDxDbG91ZE91dGxpbmVkIC8+LFxuICAgICAgdGl0bGU6ICflpJrljY/orq7mlK/mjIEnLFxuICAgICAgZGVzY3JpcHRpb246ICfmlK/mjIEgSFRUUC9IVFRQU+OAgVNPQ0tTNSDnrYnlpJrnp43ljY/orq7vvIzmu6HotrPkuI3lkIzkuJrliqHlnLrmma/pnIDmsYInLFxuICAgICAgdGFnczogWydIVFRQL0hUVFBTJywgJ1NPQ0tTNScsICfpq5jlhbzlrrnmgKcnXSxcbiAgICAgIGNvbG9yOiAnIzNiODJmNidcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IDxHbG9iYWxPdXRsaW5lZCAvPixcbiAgICAgIHRpdGxlOiAn5YWo55CD6IqC54K5572R57ucJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn6KaG55uWIDUwKyDkuKrlm73lrrblkozlnLDljLrnmoTpq5jotKjph4/oioLngrnvvIznoa7kv53mnIDkvbPov57mjqXkvZPpqownLFxuICAgICAgdGFnczogWyc1MCsg5Zu95a62JywgJ+S9juW7tui/nycsICfpq5jpgJ/luqYnXSxcbiAgICAgIGNvbG9yOiAnIzEwYjk4MSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGljb246IDxMb2NrT3V0bGluZWQgLz4sXG4gICAgICB0aXRsZTogJ+S8geS4mue6p+WuieWFqCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ+mHh+eUqOWGm+eUqOe6p+WKoOWvhuaKgOacr++8jOWkmumHjeWuieWFqOmYsuaKpO+8jOS/nemanOaVsOaNruS8oOi+k+WuieWFqCcsXG4gICAgICB0YWdzOiBbJ+WGm+eUqOe6p+WKoOWvhicsICflpJrph43pmLLmiqQnLCAn6ZqQ56eB5L+d5oqkJ10sXG4gICAgICBjb2xvcjogJyNmNTllMGInXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiA8RGFzaGJvYXJkT3V0bGluZWQgLz4sXG4gICAgICB0aXRsZTogJ+aZuuiDveebkeaOpycsXG4gICAgICBkZXNjcmlwdGlvbjogJ+WunuaXtuebkeaOp+ezu+e7n+eKtuaAge+8jOaZuuiDveaVhemanOajgOa1i++8jOehruS/neacjeWKoeeos+Wumui/kOihjCcsXG4gICAgICB0YWdzOiBbJ+WunuaXtuebkeaOpycsICfmmbrog73mo4DmtYsnLCAn6Ieq5Yqo5oGi5aSNJ10sXG4gICAgICBjb2xvcjogJyM4YjVjZjYnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiA8QXBpT3V0bGluZWQgLz4sXG4gICAgICB0aXRsZTogJ0FQSSDpm4bmiJAnLFxuICAgICAgZGVzY3JpcHRpb246ICflrozmlbTnmoQgUkVTVGZ1bCBBUEnvvIzmlK/mjIHoh6rliqjljJbnrqHnkIblkoznrKzkuInmlrnns7vnu5/pm4bmiJAnLFxuICAgICAgdGFnczogWydSRVNUZnVsIEFQSScsICfoh6rliqjljJYnLCAn5piT6ZuG5oiQJ10sXG4gICAgICBjb2xvcjogJyNlZjQ0NDQnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiA8U2V0dGluZ091dGxpbmVkIC8+LFxuICAgICAgdGl0bGU6ICfngbXmtLvphY3nva4nLFxuICAgICAgZGVzY3JpcHRpb246ICfmlK/mjIHoh6rlrprkuYnphY3nva7vvIzmu6HotrPkuI3lkIzkuJrliqHlnLrmma/nmoTkuKrmgKfljJbpnIDmsYInLFxuICAgICAgdGFnczogWyfoh6rlrprkuYnphY3nva4nLCAn54G15rS76YOo572yJywgJ+WcuuaZr+mAgumFjSddLFxuICAgICAgY29sb3I6ICcjMDZiNmQ0J1xuICAgIH1cbiAgXTtcblxuICAvLyDkvb/nlKjmraXpqqRcbiAgY29uc3Qgc3RlcHMgPSBbXG4gICAge1xuICAgICAgdGl0bGU6ICfms6jlhozotKbmiLcnLFxuICAgICAgZGVzY3JpcHRpb246ICflv6vpgJ/ms6jlhozvvIzojrflj5bkuJPlsZ4gQVBJIOWvhumSpScsXG4gICAgICBpY29uOiA8Q2hlY2tPdXRsaW5lZCAvPlxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfpgInmi6nlpZfppJAnLFxuICAgICAgZGVzY3JpcHRpb246ICfmoLnmja7kuJrliqHpnIDmsYLpgInmi6nlkIjpgILnmoTmnI3liqHlpZfppJAnLFxuICAgICAgaWNvbjogPFNldHRpbmdPdXRsaW5lZCAvPlxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6ICfphY3nva7mjqXlhaUnLFxuICAgICAgZGVzY3JpcHRpb246ICfpgJrov4cgQVBJIOaIluaOp+WItumdouadv+mFjee9ruS7o+eQhuacjeWKoScsXG4gICAgICBpY29uOiA8QXBpT3V0bGluZWQgLz5cbiAgICB9LFxuICAgIHtcbiAgICAgIHRpdGxlOiAn5byA5aeL5L2/55SoJyxcbiAgICAgIGRlc2NyaXB0aW9uOiAn5Lqr5Y+X56iz5a6a6auY5pWI55qE5YWo55CD5Luj55CG5pyN5YqhJyxcbiAgICAgIGljb246IDxSb2NrZXRPdXRsaW5lZCAvPlxuICAgIH1cbiAgXTtcblxuICAvLyDlrqLmiLfmoYjkvotcbiAgY29uc3QgdGVzdGltb25pYWxzID0gW1xuICAgIHtcbiAgICAgIGNvbXBhbnk6ICfmn5DlpKflnovnlLXllYblubPlj7AnLFxuICAgICAgaW5kdXN0cnk6ICfnlLXlrZDllYbliqEnLFxuICAgICAgY29udGVudDogJ+S9v+eUqCBQcm94eUh1YiDlkI7vvIzmiJHku6znmoTlhajnkIPkuJrliqHmlbDmja7ph4fpm4bmlYjnjofmj5DljYfkuoYgMzAwJe+8jOacjeWKoeeos+WumuaAp+i+vuWIsOS6huS8geS4mue6p+agh+WHhuOAgicsXG4gICAgICBhdmF0YXI6ICdFJyxcbiAgICAgIGNvbG9yOiAnIzNiODJmNidcbiAgICB9LFxuICAgIHtcbiAgICAgIGNvbXBhbnk6ICfmn5Dph5Hono3np5HmioDlhazlj7gnLFxuICAgICAgaW5kdXN0cnk6ICfph5Hono3np5HmioAnLFxuICAgICAgY29udGVudDogJ1Byb3h5SHViIOeahOWuieWFqOaAp+WSjOeos+WumuaAp+WujOWFqOa7oei2s+aIkeS7rOeahOWQiOinhOimgeaxgu+8jOaYr+WAvOW+l+S/oei1lueahOS8geS4mue6p+acjeWKoeWVhuOAgicsXG4gICAgICBhdmF0YXI6ICdGJyxcbiAgICAgIGNvbG9yOiAnIzEwYjk4MSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGNvbXBhbnk6ICfmn5DmlbDmja7liIbmnpDlhazlj7gnLFxuICAgICAgaW5kdXN0cnk6ICfmlbDmja7liIbmnpAnLFxuICAgICAgY29udGVudDogJzI0Lzcg55qE5oqA5pyv5pSv5oyB5ZKMIDk5LjklIOeahOeos+WumuaAp+S/neivge+8jOiuqeaIkeS7rOeahOS4muWKoei/kOihjOabtOWKoOWuieW/g+OAgicsXG4gICAgICBhdmF0YXI6ICdEJyxcbiAgICAgIGNvbG9yOiAnI2Y1OWUwYidcbiAgICB9XG4gIF07XG5cbiAgcmV0dXJuIChcbiAgICA8TGF5b3V0IHN0eWxlPXt7IG1pbkhlaWdodDogJzEwMHZoJyB9fT5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8SGVhZGVyIHN0eWxlPXt7XG4gICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpJyxcbiAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDEwcHgpJyxcbiAgICAgICAgYm94U2hhZG93OiAnMCAxcHggM3B4IHJnYmEoMCwwLDAsMC4xKScsXG4gICAgICAgIHBhZGRpbmc6ICcwIDI0cHgnLFxuICAgICAgICBwb3NpdGlvbjogJ3N0aWNreScsXG4gICAgICAgIHRvcDogMCxcbiAgICAgICAgekluZGV4OiAxMDAwLFxuICAgICAgICBib3JkZXJCb3R0b206ICcxcHggc29saWQgcmdiYSgwLDAsMCwwLjA2KSdcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgICAgbWF4V2lkdGg6ICcxNDAwcHgnLFxuICAgICAgICAgIG1hcmdpbjogJzAgYXV0bycsXG4gICAgICAgICAgaGVpZ2h0OiAnNzJweCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBzdHlsZT17eyB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcxNnB4JyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIHdpZHRoOiAnNDhweCcsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDhweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzYjgyZjYsICMxZDRlZDgpJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgNHB4IDEycHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuMyknXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIDxTYWZldHlPdXRsaW5lZCBzdHlsZT17eyBmb250U2l6ZTogJzI0cHgnLCBjb2xvcjogJyNmZmYnIH19IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxUaXRsZSBsZXZlbD17M30gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzFlMjkzYicsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzI0cHgnLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzcwMCcsXG4gICAgICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMSdcbiAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgIFByb3h5SHViXG4gICAgICAgICAgICAgICAgPC9UaXRsZT5cbiAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxM3B4JyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzY0NzQ4YicsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgRW50ZXJwcmlzZSBQcm94eSBTb2x1dGlvbnNcbiAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgey8qIERlc2t0b3AgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdub25lJyB9fSBjbGFzc05hbWU9XCJkZXNrdG9wLW5hdlwiPlxuICAgICAgICAgICAgPFNwYWNlIHNpemU9XCJsYXJnZVwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzQ3NTU2OScsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDBweCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjM2I4MmY2JztcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnI2YxZjVmOSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjNDc1NTY5JztcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDkuqflk4HnibnmgKdcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNDc1NTY5JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc0MHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmNvbG9yID0gJyMzYjgyZjYnO1xuICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICcjZjFmNWY5JztcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmNvbG9yID0gJyM0NzU1NjknO1xuICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOino+WGs+aWueahiFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJyM0NzU1NjknLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNXB4JyxcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogJzQwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4J1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzNiODJmNic7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJyNmMWY1ZjknO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzQ3NTU2OSc7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5Lu35qC85pa55qGIXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzQ3NTU2OScsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDBweCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjM2I4MmY2JztcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnI2YxZjVmOSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjNDc1NTY5JztcbiAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDlvIDlj5HogIVcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxEaXZpZGVyIHR5cGU9XCJ2ZXJ0aWNhbFwiIHN0eWxlPXt7IGJvcmRlckNvbG9yOiAnI2UyZThmMCcsIGhlaWdodDogJzI0cHgnIH19IC8+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbG9naW5cIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNDc1NTY5JyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDBweCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmNvbG9yID0gJyMzYjgyZjYnO1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJyNmMWY1ZjknO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnIzQ3NTU2OSc7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDnmbvlvZVcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjM2I4MmY2LCAjMWQ0ZWQ4KScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDRweCcsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmdMZWZ0OiAnMjRweCcsXG4gICAgICAgICAgICAgICAgICAgIHBhZGRpbmdSaWdodDogJzI0cHgnLFxuICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxMnB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjMpJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZSdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5YWN6LS56K+V55SoXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTW9iaWxlIE5hdmlnYXRpb24gKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcxMnB4JyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICc4cHgnIH19IGNsYXNzTmFtZT1cIm1vYmlsZS1uYXZcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9sb2dpblwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cInRleHRcIiBzaXplPVwibWlkZGxlXCIgc3R5bGU9e3sgZm9udFdlaWdodDogJzUwMCcgfX0+XG4gICAgICAgICAgICAgICAgICDnmbvlvZVcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdHlwZT1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1pZGRsZVwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzNiODJmNicsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnIzNiODJmNicsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOivleeUqFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIGljb249ezxNZW51T3V0bGluZWQgLz59XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKHRydWUpfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdub25lJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE4cHgnLFxuICAgICAgICAgICAgICAgIHdpZHRoOiAnNDRweCcsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDRweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4J1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtb2JpbGUtbWVudS1idG5cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFJlc3BvbnNpdmUgQ1NTICovfVxuICAgICAgICA8c3R5bGUganN4PntgXG4gICAgICAgICAgQG1lZGlhIChtaW4td2lkdGg6IDEwMjRweCkge1xuICAgICAgICAgICAgLmRlc2t0b3AtbmF2IHsgZGlzcGxheTogYmxvY2sgIWltcG9ydGFudDsgfVxuICAgICAgICAgICAgLm1vYmlsZS1uYXYgeyBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7IH1cbiAgICAgICAgICAgIC5tb2JpbGUtbWVudS1idG4geyBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7IH1cbiAgICAgICAgICB9XG4gICAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDEwMjNweCkgYW5kIChtaW4td2lkdGg6IDY0MHB4KSB7XG4gICAgICAgICAgICAuZGVza3RvcC1uYXYgeyBkaXNwbGF5OiBub25lICFpbXBvcnRhbnQ7IH1cbiAgICAgICAgICAgIC5tb2JpbGUtbmF2IHsgZGlzcGxheTogZmxleCAhaW1wb3J0YW50OyB9XG4gICAgICAgICAgICAubW9iaWxlLW1lbnUtYnRuIHsgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OyB9XG4gICAgICAgICAgfVxuICAgICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA2MzlweCkge1xuICAgICAgICAgICAgLmRlc2t0b3AtbmF2IHsgZGlzcGxheTogbm9uZSAhaW1wb3J0YW50OyB9XG4gICAgICAgICAgICAubW9iaWxlLW5hdiB7IGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDsgfVxuICAgICAgICAgICAgLm1vYmlsZS1tZW51LWJ0biB7IGRpc3BsYXk6IGlubGluZS1mbGV4ICFpbXBvcnRhbnQ7IH1cbiAgICAgICAgICB9XG4gICAgICAgIGB9PC9zdHlsZT5cbiAgICAgIDwvSGVhZGVyPlxuXG4gICAgICB7LyogTW9iaWxlIERyYXdlciAqL31cbiAgICAgIDxEcmF3ZXJcbiAgICAgICAgdGl0bGU9e1xuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTZweCcgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHdpZHRoOiAnNDBweCcsXG4gICAgICAgICAgICAgIGhlaWdodDogJzQwcHgnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMHB4JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzYjgyZjYsICMxZDRlZDgpJyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFNhZmV0eU91dGxpbmVkIHN0eWxlPXt7IGZvbnRTaXplOiAnMjBweCcsIGNvbG9yOiAnI2ZmZicgfX0gLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJyMxZTI5M2InLCBmb250V2VpZ2h0OiAnNzAwJywgZm9udFNpemU6ICcxOHB4JyB9fT5cbiAgICAgICAgICAgICAgICBQcm94eUh1YlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBjb2xvcjogJyM2NDc0OGInLCBmb250U2l6ZTogJzEycHgnIH19PlxuICAgICAgICAgICAgICAgIEVudGVycHJpc2UgU29sdXRpb25zXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIH1cbiAgICAgICAgcGxhY2VtZW50PVwicmlnaHRcIlxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgIG9wZW49e21vYmlsZU1lbnVPcGVufVxuICAgICAgICB3aWR0aD17MzIwfVxuICAgICAgICBzdHlsZXM9e3tcbiAgICAgICAgICBib2R5OiB7IHBhZGRpbmc6ICcyNHB4JyB9LFxuICAgICAgICAgIGhlYWRlcjogeyBib3JkZXJCb3R0b206ICcxcHggc29saWQgI2YxZjVmOScsIHBhZGRpbmdCb3R0b206ICcxNnB4JyB9XG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgZ2FwOiAnMTJweCcgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5cbiAgICAgICAgICAgIDxUZXh0IHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcsIGNvbG9yOiAnIzY0NzQ4YicsIGZvbnRXZWlnaHQ6ICc1MDAnIH19PlxuICAgICAgICAgICAgICDlr7zoiKroj5zljZVcbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIGJsb2NrXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICB0ZXh0QWxpZ246ICdsZWZ0JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnNTJweCcsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICBjb2xvcjogJyM0NzU1NjknLFxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2ZsZXgtc3RhcnQnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHRyYW5zcGFyZW50J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICcjZjhmYWZjJztcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAnI2UyZThmMCc7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8R2xvYmFsT3V0bGluZWQgc3R5bGU9e3sgbWFyZ2luUmlnaHQ6ICcxMnB4JywgY29sb3I6ICcjM2I4MmY2JyB9fSAvPlxuICAgICAgICAgICAg5Lqn5ZOB54m55oCnXG4gICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICBibG9ja1xuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgdGV4dEFsaWduOiAnbGVmdCcsXG4gICAgICAgICAgICAgIGhlaWdodDogJzUycHgnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjNDc1NTY5JyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCB0cmFuc3BhcmVudCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnI2Y4ZmFmYyc7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJyNlMmU4ZjAnO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNldHRpbmdPdXRsaW5lZCBzdHlsZT17eyBtYXJnaW5SaWdodDogJzEycHgnLCBjb2xvcjogJyMxMGI5ODEnIH19IC8+XG4gICAgICAgICAgICDop6PlhrPmlrnmoYhcbiAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIGJsb2NrXG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICB0ZXh0QWxpZ246ICdsZWZ0JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnNTJweCcsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICBjb2xvcjogJyM0NzU1NjknLFxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2ZsZXgtc3RhcnQnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHRyYW5zcGFyZW50J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICcjZjhmYWZjJztcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAnI2UyZThmMCc7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8VHJvcGh5T3V0bGluZWQgc3R5bGU9e3sgbWFyZ2luUmlnaHQ6ICcxMnB4JywgY29sb3I6ICcjZjU5ZTBiJyB9fSAvPlxuICAgICAgICAgICAg5Lu35qC85pa55qGIXG4gICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICBibG9ja1xuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgdGV4dEFsaWduOiAnbGVmdCcsXG4gICAgICAgICAgICAgIGhlaWdodDogJzUycHgnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjNDc1NTY5JyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdmbGV4LXN0YXJ0JyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCB0cmFuc3BhcmVudCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnI2Y4ZmFmYyc7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJyNlMmU4ZjAnO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEFwaU91dGxpbmVkIHN0eWxlPXt7IG1hcmdpblJpZ2h0OiAnMTJweCcsIGNvbG9yOiAnIzhiNWNmNicgfX0gLz5cbiAgICAgICAgICAgIOW8gOWPkeiAhVxuICAgICAgICAgIDwvQnV0dG9uPlxuXG4gICAgICAgICAgPERpdmlkZXIgc3R5bGU9e3sgbWFyZ2luOiAnMjRweCAwJyB9fSAvPlxuXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5cbiAgICAgICAgICAgIDxUZXh0IHN0eWxlPXt7IGZvbnRTaXplOiAnMTRweCcsIGNvbG9yOiAnIzY0NzQ4YicsIGZvbnRXZWlnaHQ6ICc1MDAnIH19PlxuICAgICAgICAgICAgICDotKbmiLfmk43kvZxcbiAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvbG9naW5cIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBibG9ja1xuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGhlaWdodDogJzUycHgnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNDc1NTY5JyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2UyZThmMCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg55m75b2V6LSm5oi3XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgYmxvY2tcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBoZWlnaHQ6ICc1MnB4JyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjM2I4MmY2LCAjMWQ0ZWQ4KScsXG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luVG9wOiAnOHB4JyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxMnB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjMpJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDlhY3otLnor5XnlKhcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0RyYXdlcj5cblxuICAgICAgPENvbnRlbnQ+XG4gICAgICAgIHsvKiBIZXJvIFNlY3Rpb24gKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzBmMTcyYSAwJSwgIzFlMjkzYiA1MCUsICMzMzQxNTUgMTAwJSknLFxuICAgICAgICAgIHBhZGRpbmc6ICcxNDBweCAyNHB4IDEyMHB4JyxcbiAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJ1xuICAgICAgICB9fT5cbiAgICAgICAgICB7LyogQmFja2dyb3VuZCBFbGVtZW50cyAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgIHRvcDogMCxcbiAgICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgICByaWdodDogMCxcbiAgICAgICAgICAgIGJvdHRvbTogMCxcbiAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0Nzdmcgd2lkdGg9JzYwJyBoZWlnaHQ9JzYwJyB2aWV3Qm94PScwIDAgNjAgNjAnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyclM0UlM0NnIGZpbGw9J25vbmUnIGZpbGwtcnVsZT0nZXZlbm9kZCclM0UlM0NnIGZpbGw9JyUyM2ZmZmZmZicgZmlsbC1vcGFjaXR5PScwLjAyJyUzRSUzQ2NpcmNsZSBjeD0nMzAnIGN5PSczMCcgcj0nMS41Jy8lM0UlM0MvZyUzRSUzQy9nJTNFJTNDL3N2ZyUzRVwiKWAsXG4gICAgICAgICAgICBvcGFjaXR5OiAwLjZcbiAgICAgICAgICB9fSAvPlxuXG4gICAgICAgICAgey8qIEdyYWRpZW50IE9yYnMgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICB0b3A6ICcyMCUnLFxuICAgICAgICAgICAgbGVmdDogJzEwJScsXG4gICAgICAgICAgICB3aWR0aDogJzMwMHB4JyxcbiAgICAgICAgICAgIGhlaWdodDogJzMwMHB4JyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgcmdiYSg1OSwgMTMwLCAyNDYsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDcwJSknLFxuICAgICAgICAgICAgZmlsdGVyOiAnYmx1cig0MHB4KSdcbiAgICAgICAgICB9fSAvPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgdG9wOiAnNjAlJyxcbiAgICAgICAgICAgIHJpZ2h0OiAnMTAlJyxcbiAgICAgICAgICAgIHdpZHRoOiAnMjAwcHgnLFxuICAgICAgICAgICAgaGVpZ2h0OiAnMjAwcHgnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDE2LCAxODUsIDEyOSwgMC4xKSAwJSwgdHJhbnNwYXJlbnQgNzAlKScsXG4gICAgICAgICAgICBmaWx0ZXI6ICdibHVyKDMwcHgpJ1xuICAgICAgICAgIH19IC8+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAnMTIwMHB4JywgbWFyZ2luOiAnMCBhdXRvJywgcG9zaXRpb246ICdyZWxhdGl2ZScsIHpJbmRleDogMSB9fT5cbiAgICAgICAgICAgIHsvKiBUcnVzdCBJbmRpY2F0b3JzICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICc1NnB4JyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdpbmxpbmUtZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgZ2FwOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4wOCknLFxuICAgICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigyMHB4KScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHJnYmEoMjU1LDI1NSwyNTUsMC4xMiknLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzYwcHgnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4IDMycHgnLFxuICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzMycHgnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgZ2FwOiAnOHB4J1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxMGI5ODEnLFxuICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDAgMTJweCAjMTBiOTgxJyxcbiAgICAgICAgICAgICAgICAgICAgYW5pbWF0aW9uOiAncHVsc2UgMnMgaW5maW5pdGUnXG4gICAgICAgICAgICAgICAgICB9fSAvPlxuICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOSknLFxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNjAwJ1xuICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgIOacjeWKoSAxMCwwMDArIOS8geS4muWuouaIt1xuICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxEaXZpZGVyIHR5cGU9XCJ2ZXJ0aWNhbFwiIHN0eWxlPXt7IGJvcmRlckNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjIpJywgaGVpZ2h0OiAnMTZweCcgfX0gLz5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGdhcDogJzhweCdcbiAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgIDxUcm9waHlPdXRsaW5lZCBzdHlsZT17eyBjb2xvcjogJyNmNTllMGInLCBmb250U2l6ZTogJzE2cHgnIH19IC8+XG4gICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC45KScsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnXG4gICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAg6KGM5Lia6aKG5YWIXG4gICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBNYWluIEhlYWRsaW5lICovfVxuICAgICAgICAgICAgPFRpdGxlIGxldmVsPXsxfSBzdHlsZT17e1xuICAgICAgICAgICAgICBmb250U2l6ZTogJ2NsYW1wKDNyZW0sIDh2dywgNXJlbSknLFxuICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICczMnB4JyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjZmZmZmZmJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzgwMCcsXG4gICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjEnLFxuICAgICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiAnLTAuMDNlbScsXG4gICAgICAgICAgICAgIHRleHRTaGFkb3c6ICcwIDRweCAyMHB4IHJnYmEoMCwwLDAsMC4zKSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICDkvIHkuJrnuqflhajnkIPku6PnkIZcbiAgICAgICAgICAgICAgPGJyIC8+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzYjgyZjYsICMwNmI2ZDQpJyxcbiAgICAgICAgICAgICAgICBXZWJraXRCYWNrZ3JvdW5kQ2xpcDogJ3RleHQnLFxuICAgICAgICAgICAgICAgIFdlYmtpdFRleHRGaWxsQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENsaXA6ICd0ZXh0JyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnODAwJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDop6PlhrPmlrnmoYhcbiAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPC9UaXRsZT5cblxuICAgICAgICAgICAgey8qIFN1YnRpdGxlICovfVxuICAgICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17e1xuICAgICAgICAgICAgICBmb250U2l6ZTogJ2NsYW1wKDEuMnJlbSwgM3Z3LCAxLjVyZW0pJyxcbiAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOCknLFxuICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICc1NnB4JyxcbiAgICAgICAgICAgICAgbWF4V2lkdGg6ICc4MDBweCcsXG4gICAgICAgICAgICAgIG1hcmdpbjogJzAgYXV0byA1NnB4IGF1dG8nLFxuICAgICAgICAgICAgICBsaW5lSGVpZ2h0OiAnMS42JyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzQwMCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICDkuLrkvIHkuJrmj5DkvpvnqLPlrprjgIHlronlhajjgIHpq5jpgJ/nmoTlhajnkIPku6PnkIbnvZHnu5zmnI3liqFcbiAgICAgICAgICAgICAgPGJyIC8+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXt7IGNvbG9yOiAnIzYwYTVmYScsIGZvbnRXZWlnaHQ6ICc2MDAnIH19Pjk5LjklIFNMQSDkv53or4E8L1RleHQ+IOKAolxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17eyBjb2xvcjogJyMzNGQzOTknLCBmb250V2VpZ2h0OiAnNjAwJyB9fT41MCsg5Zu95a626IqC54K5PC9UZXh0PiDigKJcbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICcjZmJiZjI0JywgZm9udFdlaWdodDogJzYwMCcgfX0+MjQvNyDmioDmnK/mlK/mjIE8L1RleHQ+XG4gICAgICAgICAgICA8L1BhcmFncmFwaD5cblxuICAgICAgICAgICAgey8qIENUQSBTZWN0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICc4MHB4JyB9fT5cbiAgICAgICAgICAgICAgPFNwYWNlIHNpemU9XCJsYXJnZVwiIHdyYXA+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9yZWdpc3RlclwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICAgIGljb249ezxSb2NrZXRPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc2NHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE4cHgnLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc3MDAnLFxuICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjM2I4MmY2LCAjMWQ0ZWQ4KScsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgcGFkZGluZ0xlZnQ6ICc0MHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nUmlnaHQ6ICc0MHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDEycHggMzJweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC40KScsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6ICcyMDBweCdcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg56uL5Y2z5YWN6LS56K+V55SoXG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICAgIGljb249ezxQbGF5Q2lyY2xlT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc2NHB4JyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMDgpJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkIHJnYmEoMjU1LDI1NSwyNTUsMC4xNiknLFxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyNmZmZmZmYnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZHJvcEZpbHRlcjogJ2JsdXIoMjBweCknLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZ0xlZnQ6ICc0MHB4JyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZ1JpZ2h0OiAnNDBweCcsXG4gICAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiAnMTgwcHgnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOingueci+a8lOekulxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L1NwYWNlPlxuXG4gICAgICAgICAgICAgIHsvKiBRdWljayBTdGF0cyAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Ub3A6ICc0OHB4JyB9fT5cbiAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuNiknLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNHB4JyxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaycsXG4gICAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxNnB4J1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAg5bey5pyJ5pWw5Y2D5a625LyB5Lia6YCJ5oup5oiR5LusXG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDxTcGFjZSBzaXplPVwibGFyZ2VcIiB3cmFwPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17eyBjb2xvcjogJyNmZmZmZmYnLCBmb250U2l6ZTogJzI0cHgnLCBmb250V2VpZ2h0OiAnNzAwJywgZGlzcGxheTogJ2Jsb2NrJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAxMEsrXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuNiknLCBmb250U2l6ZTogJzEycHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIOS8geS4mueUqOaIt1xuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICcjZmZmZmZmJywgZm9udFNpemU6ICcyNHB4JywgZm9udFdlaWdodDogJzcwMCcsIGRpc3BsYXk6ICdibG9jaycgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgNTArXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuNiknLCBmb250U2l6ZTogJzEycHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIOWbveWutuiKgueCuVxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICcjZmZmZmZmJywgZm9udFNpemU6ICcyNHB4JywgZm9udFdlaWdodDogJzcwMCcsIGRpc3BsYXk6ICdibG9jaycgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgOTkuOSVcbiAgICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17eyBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC42KScsIGZvbnRTaXplOiAnMTJweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAg56iz5a6a5oCnXG4gICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvU3BhY2U+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBTdGF0cyBTZWN0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsMjU1LDI1NSwwLjA1KScsXG4gICAgICAgICAgICAgIGJhY2tkcm9wRmlsdGVyOiAnYmx1cigyMHB4KScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LDAuMSknLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICczMnB4JyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzQ4cHggMzJweCcsXG4gICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzAnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFJvdyBndXR0ZXI9e1szMiwgMzJdfT5cbiAgICAgICAgICAgICAgICB7c3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPENvbCB4cz17MTJ9IGxnPXs2fSBrZXk9e2luZGV4fT5cbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC4wOCknLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2Ryb3BGaWx0ZXI6ICdibHVyKDIwcHgpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCByZ2JhKDI1NSwyNTUsMjU1LDAuMTIpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI0cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzQwcHggMjRweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuNHMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKScsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdkZWZhdWx0JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoLTEycHgpIHNjYWxlKDEuMDIpJztcbiAgICAgICAgICAgICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3JnYmEoMjU1LDI1NSwyNTUsMC4xMiknO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJ3JnYmEoMjU1LDI1NSwyNTUsMC4yKSc7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoMCkgc2NhbGUoMSknO1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAncmdiYSgyNTUsMjU1LDI1NSwwLjA4KSc7XG4gICAgICAgICAgICAgICAgICAgICAgICBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAncmdiYSgyNTUsMjU1LDI1NSwwLjEyKSc7XG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIEdsb3cgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICB0b3A6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgbGVmdDogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGUoLTUwJSwgLTUwJSknLFxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMjBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcxMjBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogYHJhZGlhbC1ncmFkaWVudChjaXJjbGUsICR7c3RhdC5jb2xvcn0xNSAwJSwgdHJhbnNwYXJlbnQgNzAlKWAsXG4gICAgICAgICAgICAgICAgICAgICAgICBmaWx0ZXI6ICdibHVyKDIwcHgpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHpJbmRleDogMFxuICAgICAgICAgICAgICAgICAgICAgIH19IC8+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBvc2l0aW9uOiAncmVsYXRpdmUnLCB6SW5kZXg6IDEgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogSWNvbiBDb250YWluZXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzI0cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcidcbiAgICAgICAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc3MnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICc3MnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgbGluZWFyLWdyYWRpZW50KDEzNWRlZywgJHtzdGF0LmNvbG9yfTIwLCAke3N0YXQuY29sb3J9MzApYCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDJweCBzb2xpZCAke3N0YXQuY29sb3J9NDBgLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzogYDAgOHB4IDMycHggJHtzdGF0LmNvbG9yfTIwYFxuICAgICAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICczMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBzdGF0LmNvbG9yLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVyOiAnZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoMCwwLDAsMC4xKSknXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhdC5pY29ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFZhbHVlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxMnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyNmZmZmZmYnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMzZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzgwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluZUhlaWdodDogJzEnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0YXQudmFsdWV9e3N0YXQuc3VmZml4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIFRpdGxlICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjkpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0YXQudGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogRGVzY3JpcHRpb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC42KScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTNweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6ICcxLjQnXG4gICAgICAgICAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3N0YXQuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9Db2w+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvUm93PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBDVEEgQnV0dG9ucyAqL31cbiAgICAgICAgICAgIDxTcGFjZSBzaXplPVwibGFyZ2VcIiB3cmFwPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJwcmltYXJ5XCIgXG4gICAgICAgICAgICAgICAgICBzaXplPVwibGFyZ2VcIlxuICAgICAgICAgICAgICAgICAgaWNvbj17PFBsYXlDaXJjbGVPdXRsaW5lZCAvPn1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogJzQ4cHgnLCBwYWRkaW5nOiAnMCAzMnB4JywgZm9udFNpemU6ICcxNnB4JyB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOeri+WNs+W8gOWni+WFjei0ueivleeUqFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICBpY29uPXs8RXllT3V0bGluZWQgLz59XG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgaGVpZ2h0OiAnNDhweCcsIHBhZGRpbmc6ICcwIDMycHgnLCBmb250U2l6ZTogJzE2cHgnIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDop4LnnIvmvJTnpLpcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L1NwYWNlPlxuXG4gICAgICAgICAgICB7LyogVHJ1c3QgaW5kaWNhdG9ycyAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMzJweCcgfX0+XG4gICAgICAgICAgICAgIDxTcGFjZSBzaXplPVwibGFyZ2VcIiB3cmFwPlxuICAgICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZU91dGxpbmVkIHN0eWxlPXt7IGNvbG9yOiAnIzA1OTY2OScsIG1hcmdpblJpZ2h0OiAnOHB4JyB9fSAvPlxuICAgICAgICAgICAgICAgICAg5peg6ZyA5L+h55So5Y2hXG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDxUZXh0IHR5cGU9XCJzZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZU91dGxpbmVkIHN0eWxlPXt7IGNvbG9yOiAnIzA1OTY2OScsIG1hcmdpblJpZ2h0OiAnOHB4JyB9fSAvPlxuICAgICAgICAgICAgICAgICAgN+WkqeWFjei0ueivleeUqFxuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICA8VGV4dCB0eXBlPVwic2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGVPdXRsaW5lZCBzdHlsZT17eyBjb2xvcjogJyMwNTk2NjknLCBtYXJnaW5SaWdodDogJzhweCcgfX0gLz5cbiAgICAgICAgICAgICAgICAgIOmaj+aXtuWPlua2iFxuICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmVhdHVyZXMgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nOiAnODBweCAyNHB4JywgYmFja2dyb3VuZDogJyNmZmYnIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBtYXJnaW5Cb3R0b206ICc2NHB4JyB9fT5cbiAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXsyfSBzdHlsZT17eyBmb250U2l6ZTogJzIuNXJlbScsIG1hcmdpbkJvdHRvbTogJzE2cHgnIH19PlxuICAgICAgICAgICAgICAgIOS4uuS7gOS5iOmAieaLqeaIkeS7rO+8n1xuICAgICAgICAgICAgICA8L1RpdGxlPlxuICAgICAgICAgICAgICA8UGFyYWdyYXBoIHN0eWxlPXt7IFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4yNXJlbScsIFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnIzZiNzI4MCcsXG4gICAgICAgICAgICAgICAgbWF4V2lkdGg6ICc2MDBweCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAnMCBhdXRvJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDmiJHku6zmj5DkvpvkuJrnlYzpooblhYjnmoTku6PnkIbmnI3liqHvvIzliqnlipvmgqjnmoTkuJrliqHlnKjlhajnkIPojIPlm7TlhoXnqLPlrprov5DooYxcbiAgICAgICAgICAgICAgPC9QYXJhZ3JhcGg+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPFJvdyBndXR0ZXI9e1szMiwgMzJdfT5cbiAgICAgICAgICAgICAgPENvbCB4cz17MjR9IG1kPXsxMn0gbGc9ezh9PlxuICAgICAgICAgICAgICAgIDxDYXJkIFxuICAgICAgICAgICAgICAgICAgaG92ZXJhYmxlXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6ICcxMDAlJywgdGV4dEFsaWduOiAnY2VudGVyJyB9fVxuICAgICAgICAgICAgICAgICAgYm9keVN0eWxlPXt7IHBhZGRpbmc6ICczMnB4IDI0cHgnIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPEdsb2JhbE91dGxpbmVkIHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIGNvbG9yOiAnIzI1NjNlYicgfX0gLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXs0fSBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxMnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg5aSa5Y2P6K6u5pSv5oyBXG4gICAgICAgICAgICAgICAgICA8L1RpdGxlPlxuICAgICAgICAgICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17eyBjb2xvcjogJyM2YjcyODAnLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg5pSv5oyBSFRUUC9IVFRQU+WSjFNPQ0tTNeWNj+iuru+8jOa7oei2s+S4jeWQjOW6lOeUqOWcuuaZr+eahOmcgOaxgu+8jOWFvOWuueaAp+W8ulxuICAgICAgICAgICAgICAgICAgPC9QYXJhZ3JhcGg+XG4gICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICA8L0NvbD5cblxuICAgICAgICAgICAgICA8Q29sIHhzPXsyNH0gbWQ9ezEyfSBsZz17OH0+XG4gICAgICAgICAgICAgICAgPENhcmQgXG4gICAgICAgICAgICAgICAgICBob3ZlcmFibGVcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogJzEwMCUnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19XG4gICAgICAgICAgICAgICAgICBib2R5U3R5bGU9e3sgcGFkZGluZzogJzMycHggMjRweCcgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzE2cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICA8U2FmZXR5T3V0bGluZWQgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgY29sb3I6ICcjMDU5NjY5JyB9fSAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8VGl0bGUgbGV2ZWw9ezR9IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzEycHgnIH19PlxuICAgICAgICAgICAgICAgICAgICDlhajnkIPoioLngrnopobnm5ZcbiAgICAgICAgICAgICAgICAgIDwvVGl0bGU+XG4gICAgICAgICAgICAgICAgICA8UGFyYWdyYXBoIHN0eWxlPXt7IGNvbG9yOiAnIzZiNzI4MCcsIGxpbmVIZWlnaHQ6ICcxLjYnIH19PlxuICAgICAgICAgICAgICAgICAgICDopobnm5Y1MCvkuKrlm73lrrblkozlnLDljLrvvIzmj5DkvpvkvY7lu7bov5/jgIHpq5jpgJ/luqbnmoTnvZHnu5zorr/pl67kvZPpqoxcbiAgICAgICAgICAgICAgICAgIDwvUGFyYWdyYXBoPlxuICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgPC9Db2w+XG5cbiAgICAgICAgICAgICAgPENvbCB4cz17MjR9IG1kPXsxMn0gbGc9ezh9PlxuICAgICAgICAgICAgICAgIDxDYXJkIFxuICAgICAgICAgICAgICAgICAgaG92ZXJhYmxlXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6ICcxMDAlJywgdGV4dEFsaWduOiAnY2VudGVyJyB9fVxuICAgICAgICAgICAgICAgICAgYm9keVN0eWxlPXt7IHBhZGRpbmc6ICczMnB4IDI0cHgnIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgPFRodW5kZXJib2x0T3V0bGluZWQgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgY29sb3I6ICcjZDk3NzA2JyB9fSAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8VGl0bGUgbGV2ZWw9ezR9IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzEycHgnIH19PlxuICAgICAgICAgICAgICAgICAgICDmnoHpgJ/nqLPlrppcbiAgICAgICAgICAgICAgICAgIDwvVGl0bGU+XG4gICAgICAgICAgICAgICAgICA8UGFyYWdyYXBoIHN0eWxlPXt7IGNvbG9yOiAnIzZiNzI4MCcsIGxpbmVIZWlnaHQ6ICcxLjYnIH19PlxuICAgICAgICAgICAgICAgICAgICA5OS45Jeeos+WumuaAp+S/neivge+8jOS8mOi0qOeahOe9kee7nOWfuuehgOiuvuaWve+8jOehruS/neacjeWKoemrmOmAn+eos+Wumui/kOihjFxuICAgICAgICAgICAgICAgICAgPC9QYXJhZ3JhcGg+XG4gICAgICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgICA8L0NvbD5cblxuICAgICAgICAgICAgICA8Q29sIHhzPXsyNH0gbWQ9ezEyfSBsZz17OH0+XG4gICAgICAgICAgICAgICAgPENhcmQgXG4gICAgICAgICAgICAgICAgICBob3ZlcmFibGVcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7IGhlaWdodDogJzEwMCUnLCB0ZXh0QWxpZ246ICdjZW50ZXInIH19XG4gICAgICAgICAgICAgICAgICBib2R5U3R5bGU9e3sgcGFkZGluZzogJzMycHggMjRweCcgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpbkJvdHRvbTogJzE2cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICA8TG9ja091dGxpbmVkIHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIGNvbG9yOiAnIzdjM2FlZCcgfX0gLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXs0fSBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxMnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg5LyB5Lia57qn5a6J5YWoXG4gICAgICAgICAgICAgICAgICA8L1RpdGxlPlxuICAgICAgICAgICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17eyBjb2xvcjogJyM2YjcyODAnLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg6YeH55So5Yab55So57qn5Yqg5a+G5oqA5pyv77yM5L+d5oqk5oKo55qE5pWw5o2u5Lyg6L6T5a6J5YWo5ZKM6ZqQ56eB5LiN6KKr5rOE6ZyyXG4gICAgICAgICAgICAgICAgICA8L1BhcmFncmFwaD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvQ29sPlxuXG4gICAgICAgICAgICAgIDxDb2wgeHM9ezI0fSBtZD17MTJ9IGxnPXs4fT5cbiAgICAgICAgICAgICAgICA8Q2FyZCBcbiAgICAgICAgICAgICAgICAgIGhvdmVyYWJsZVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgaGVpZ2h0OiAnMTAwJScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX1cbiAgICAgICAgICAgICAgICAgIGJvZHlTdHlsZT17eyBwYWRkaW5nOiAnMzJweCAyNHB4JyB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxCYXJDaGFydE91dGxpbmVkIHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIGNvbG9yOiAnI2RjMjYyNicgfX0gLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXs0fSBzdHlsZT17eyBtYXJnaW5Cb3R0b206ICcxMnB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg5a6e5pe255uR5o6nXG4gICAgICAgICAgICAgICAgICA8L1RpdGxlPlxuICAgICAgICAgICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17eyBjb2xvcjogJyM2YjcyODAnLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgICAgICAgICAg5o+Q5L6b6K+m57uG55qE5L2/55So57uf6K6h5ZKM5a6e5pe255uR5o6n6Z2i5p2/77yM5biu5Yqp5oKo5LyY5YyW5Luj55CG5L2/55So5pWI546HXG4gICAgICAgICAgICAgICAgICA8L1BhcmFncmFwaD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvQ29sPlxuXG4gICAgICAgICAgICAgIDxDb2wgeHM9ezI0fSBtZD17MTJ9IGxnPXs4fT5cbiAgICAgICAgICAgICAgICA8Q2FyZCBcbiAgICAgICAgICAgICAgICAgIGhvdmVyYWJsZVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgaGVpZ2h0OiAnMTAwJScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX1cbiAgICAgICAgICAgICAgICAgIGJvZHlTdHlsZT17eyBwYWRkaW5nOiAnMzJweCAyNHB4JyB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMTZweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxBcGlPdXRsaW5lZCBzdHlsZT17eyBmb250U2l6ZTogJzJyZW0nLCBjb2xvcjogJyMwODkxYjInIH19IC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxUaXRsZSBsZXZlbD17NH0gc3R5bGU9e3sgbWFyZ2luQm90dG9tOiAnMTJweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIEFQSembhuaIkFxuICAgICAgICAgICAgICAgICAgPC9UaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxQYXJhZ3JhcGggc3R5bGU9e3sgY29sb3I6ICcjNmI3MjgwJywgbGluZUhlaWdodDogJzEuNicgfX0+XG4gICAgICAgICAgICAgICAgICAgIOWujOaVtOeahFJFU1RmdWwgQVBJ5o6l5Y+j77yM5pSv5oyB6Ieq5Yqo5YyW566h55CG5ZKM56ys5LiJ5pa557O757uf5peg57yd6ZuG5oiQXG4gICAgICAgICAgICAgICAgICA8L1BhcmFncmFwaD5cbiAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgIDwvQ29sPlxuICAgICAgICAgICAgPC9Sb3c+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDVEEgU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzI1NjNlYiAwJSwgIzdjM2FlZCAxMDAlKScsXG4gICAgICAgICAgcGFkZGluZzogJzgwcHggMjRweCcsXG4gICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1heFdpZHRoOiAnODAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgICAgICAgPFRpdGxlIGxldmVsPXsyfSBzdHlsZT17eyBjb2xvcjogJ3doaXRlJywgZm9udFNpemU6ICcyLjVyZW0nLCBtYXJnaW5Cb3R0b206ICcxNnB4JyB9fT5cbiAgICAgICAgICAgICAg5YeG5aSH5byA5aeL5LqG5ZCX77yfXG4gICAgICAgICAgICA8L1RpdGxlPlxuICAgICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17eyBcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjI1cmVtJyxcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMzJweCcsXG4gICAgICAgICAgICAgIG9wYWNpdHk6IDAuOSxcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICDliqDlhaUxMOS4hyvnlKjmiLfnmoTooYzliJfvvIzkvZPpqozkuJPkuJrnmoTku6PnkIbmnI3liqHlubPlj7BcbiAgICAgICAgICAgIDwvUGFyYWdyYXBoPlxuICAgICAgICAgICAgPFNwYWNlIHNpemU9XCJsYXJnZVwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDhweCcsIFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMCAzMnB4JywgXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzI1NjNlYicsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOeri+WNs+WFjei0ueazqOWGjFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgICAgICAgc2l6ZT1cImxhcmdlXCJcbiAgICAgICAgICAgICAgICBnaG9zdFxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnNDhweCcsIFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAgMzJweCcsIFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNnB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg6IGU57O76ZSA5ZSu5Zui6ZifXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9TcGFjZT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0NvbnRlbnQ+XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICA8Rm9vdGVyIHN0eWxlPXt7IFxuICAgICAgICBiYWNrZ3JvdW5kOiAnIzAwMTUyOScsIFxuICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgcGFkZGluZzogJzQ4cHggMjRweCAxNnB4IDI0cHgnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBtYXhXaWR0aDogJzEyMDBweCcsIG1hcmdpbjogJzAgYXV0bycsIHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgZ2FwOiAnMTZweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICczMnB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPEF2YXRhciBcbiAgICAgICAgICAgICAgc2l6ZT17NDh9IFxuICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMjU2M2ViJyB9fVxuICAgICAgICAgICAgICBpY29uPXs8U2FmZXR5T3V0bGluZWQgLz59XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPFRpdGxlIGxldmVsPXszfSBzdHlsZT17eyBjb2xvcjogJyMyNTYzZWInLCBtYXJnaW46IDAgfX0+XG4gICAgICAgICAgICAgICAgUHJveHlIdWJcbiAgICAgICAgICAgICAgPC9UaXRsZT5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3sgY29sb3I6ICcjOGM4YzhjJywgZm9udFNpemU6ICcxNHB4JyB9fT5cbiAgICAgICAgICAgICAgICDkvIHkuJrnuqfku6PnkIbmnI3liqHlubPlj7BcbiAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPFBhcmFncmFwaCBzdHlsZT17eyBjb2xvcjogJyM4YzhjOGMnLCBtYXJnaW5Cb3R0b206ICczMnB4JyB9fT5cbiAgICAgICAgICAgIOS4uuWFqOeQg+eUqOaIt+aPkOS+m+eos+WumuOAgemrmOmAn+OAgeWuieWFqOeahOS7o+eQhuacjeWKoeino+WGs+aWueahiFxuICAgICAgICAgIDwvUGFyYWdyYXBoPlxuICAgICAgICAgIFxuICAgICAgICAgIDxUZXh0IHN0eWxlPXt7IGNvbG9yOiAnIzhjOGM4YycsIGZvbnRTaXplOiAnMTRweCcgfX0+XG4gICAgICAgICAgICDCqSAyMDI0IFByb3h5SHViLiDkv53nlZnmiYDmnInmnYPliKkuXG4gICAgICAgICAgPC9UZXh0PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRm9vdGVyPlxuICAgIDwvTGF5b3V0PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsIkJ1dHRvbiIsIkNhcmQiLCJSb3ciLCJDb2wiLCJUeXBvZ3JhcGh5IiwiU3BhY2UiLCJMYXlvdXQiLCJBdmF0YXIiLCJEcmF3ZXIiLCJEaXZpZGVyIiwiU2FmZXR5T3V0bGluZWQiLCJHbG9iYWxPdXRsaW5lZCIsIlRodW5kZXJib2x0T3V0bGluZWQiLCJMb2NrT3V0bGluZWQiLCJCYXJDaGFydE91dGxpbmVkIiwiQXBpT3V0bGluZWQiLCJQbGF5Q2lyY2xlT3V0bGluZWQiLCJFeWVPdXRsaW5lZCIsIkNoZWNrQ2lyY2xlT3V0bGluZWQiLCJNZW51T3V0bGluZWQiLCJSb2NrZXRPdXRsaW5lZCIsIkNsb3VkT3V0bGluZWQiLCJEYXNoYm9hcmRPdXRsaW5lZCIsIlRlYW1PdXRsaW5lZCIsIlRyb3BoeU91dGxpbmVkIiwiQ2hlY2tPdXRsaW5lZCIsIlNldHRpbmdPdXRsaW5lZCIsIkhlYWRlciIsIkNvbnRlbnQiLCJGb290ZXIiLCJUaXRsZSIsIlBhcmFncmFwaCIsIlRleHQiLCJIb21lIiwibW9iaWxlTWVudU9wZW4iLCJzZXRNb2JpbGVNZW51T3BlbiIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInN0YXRzIiwidGl0bGUiLCJ2YWx1ZSIsInN1ZmZpeCIsImljb24iLCJjb2xvciIsImRlc2NyaXB0aW9uIiwiZmVhdHVyZXMiLCJ0YWdzIiwic3RlcHMiLCJ0ZXN0aW1vbmlhbHMiLCJjb21wYW55IiwiaW5kdXN0cnkiLCJjb250ZW50IiwiYXZhdGFyIiwic3R5bGUiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kIiwiYmFja2Ryb3BGaWx0ZXIiLCJib3hTaGFkb3ciLCJwYWRkaW5nIiwicG9zaXRpb24iLCJ0b3AiLCJ6SW5kZXgiLCJib3JkZXJCb3R0b20iLCJkaXYiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwibWF4V2lkdGgiLCJtYXJnaW4iLCJoZWlnaHQiLCJocmVmIiwidGV4dERlY29yYXRpb24iLCJnYXAiLCJ3aWR0aCIsImJvcmRlclJhZGl1cyIsImZvbnRTaXplIiwibGV2ZWwiLCJmb250V2VpZ2h0IiwibGluZUhlaWdodCIsInNpemUiLCJ0eXBlIiwib25Nb3VzZUVudGVyIiwiZSIsInRhcmdldCIsIm9uTW91c2VMZWF2ZSIsImJvcmRlckNvbG9yIiwicGFkZGluZ0xlZnQiLCJwYWRkaW5nUmlnaHQiLCJib3JkZXIiLCJvbkNsaWNrIiwiY2xhc3NOYW1lIiwicGxhY2VtZW50Iiwib25DbG9zZSIsIm9wZW4iLCJzdHlsZXMiLCJib2R5IiwiaGVhZGVyIiwicGFkZGluZ0JvdHRvbSIsImZsZXhEaXJlY3Rpb24iLCJtYXJnaW5Cb3R0b20iLCJibG9jayIsInRleHRBbGlnbiIsIm1hcmdpblJpZ2h0IiwibWFyZ2luVG9wIiwib3ZlcmZsb3ciLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJiYWNrZ3JvdW5kSW1hZ2UiLCJvcGFjaXR5IiwiZmlsdGVyIiwiYW5pbWF0aW9uIiwibGV0dGVyU3BhY2luZyIsInRleHRTaGFkb3ciLCJiciIsIldlYmtpdEJhY2tncm91bmRDbGlwIiwiV2Via2l0VGV4dEZpbGxDb2xvciIsImJhY2tncm91bmRDbGlwIiwid3JhcCIsIm1pbldpZHRoIiwiZ3V0dGVyIiwibWFwIiwic3RhdCIsImluZGV4IiwieHMiLCJsZyIsInRyYW5zaXRpb24iLCJjdXJzb3IiLCJjdXJyZW50VGFyZ2V0IiwidHJhbnNmb3JtIiwic3BhbiIsIm1kIiwiaG92ZXJhYmxlIiwiYm9keVN0eWxlIiwiZ2hvc3QiLCJiYWNrZ3JvdW5kQ29sb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ant-design/nextjs-registry */ \"(ssr)/./node_modules/@ant-design/nextjs-registry/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ConfigProvider!=!antd */ \"(ssr)/./node_modules/antd/es/config-provider/index.js\");\n/* harmony import */ var antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/locale/zh_CN */ \"(ssr)/./node_modules/antd/lib/locale/zh_CN.js\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ant_design_nextjs_registry__WEBPACK_IMPORTED_MODULE_1__.AntdRegistry, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ConfigProvider_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            locale: antd_locale_zh_CN__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            theme: {\n                token: {\n                    // 主色调\n                    colorPrimary: '#2563eb',\n                    colorSuccess: '#059669',\n                    colorWarning: '#d97706',\n                    colorError: '#dc2626',\n                    colorInfo: '#0891b2',\n                    // 字体\n                    fontFamily: 'system-ui, -apple-system, sans-serif',\n                    fontSize: 14,\n                    // 圆角\n                    borderRadius: 6,\n                    // 间距\n                    padding: 16,\n                    margin: 16,\n                    // 阴影\n                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',\n                    boxShadowSecondary: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                    // 边框\n                    lineWidth: 1,\n                    lineType: 'solid',\n                    colorBorder: '#e5e7eb',\n                    // 背景色\n                    colorBgContainer: '#ffffff',\n                    colorBgLayout: '#f9fafb',\n                    colorBgElevated: '#ffffff'\n                },\n                components: {\n                    // 按钮组件定制\n                    Button: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingContentHorizontal: 16\n                    },\n                    // 输入框组件定制\n                    Input: {\n                        borderRadius: 6,\n                        controlHeight: 40,\n                        paddingInline: 12\n                    },\n                    // 卡片组件定制\n                    Card: {\n                        borderRadius: 8,\n                        paddingLG: 24\n                    },\n                    // 表格组件定制\n                    Table: {\n                        borderRadius: 8,\n                        cellPaddingBlock: 12,\n                        cellPaddingInline: 16\n                    },\n                    // 菜单组件定制\n                    Menu: {\n                        borderRadius: 6,\n                        itemHeight: 40,\n                        itemPaddingInline: 16\n                    }\n                }\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/antd","vendor-chunks/@ant-design","vendor-chunks/@rc-component","vendor-chunks/rc-field-form","vendor-chunks/rc-menu","vendor-chunks/rc-tabs","vendor-chunks/rc-util","vendor-chunks/resize-observer-polyfill","vendor-chunks/rc-motion","vendor-chunks/rc-pagination","vendor-chunks/@babel","vendor-chunks/rc-textarea","vendor-chunks/rc-input","vendor-chunks/rc-overflow","vendor-chunks/stylis","vendor-chunks/rc-collapse","vendor-chunks/rc-resize-observer","vendor-chunks/rc-dropdown","vendor-chunks/rc-tooltip","vendor-chunks/copy-to-clipboard","vendor-chunks/@emotion","vendor-chunks/rc-picker","vendor-chunks/@swc","vendor-chunks/classnames","vendor-chunks/toggle-selection","vendor-chunks/rc-drawer","vendor-chunks/styled-jsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C%E4%BB%A3%E7%A0%81%5CProxy%5Cvscode_proxy%5Cproxy-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();