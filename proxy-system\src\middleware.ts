import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 需要认证的路径
const protectedPaths = ['/dashboard'];

// 认证页面路径
const authPaths = ['/login', '/register'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查是否是受保护的路径
  const isProtectedPath = protectedPaths.some(path => 
    pathname.startsWith(path)
  );
  
  // 检查是否是认证页面
  const isAuthPath = authPaths.some(path => 
    pathname.startsWith(path)
  );

  // 从cookie或header中获取token
  const token = request.cookies.get('token')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '');

  if (isProtectedPath && !token) {
    // 如果访问受保护的路径但没有token，重定向到登录页
    return NextResponse.redirect(new URL('/login', request.url));
  }

  if (isAuthPath && token) {
    // 如果已经登录但访问认证页面，重定向到仪表板
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
