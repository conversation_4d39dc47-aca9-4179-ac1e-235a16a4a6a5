"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _PicLeftOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/PicLeftOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var PicLeftOutlined = function PicLeftOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _PicLeftOutlined.default
  }));
};

/**![pic-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1MiA3OTJINzJjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoODgwYzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0wLTYzMkg3MmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg4ODBjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6TTYwOCA2NjBjOC44IDAgMTYtNy4yIDE2LTE2VjM4MGMwLTguOC03LjItMTYtMTYtMTZIOTZjLTguOCAwLTE2IDcuMi0xNiAxNnYyNjRjMCA4LjggNy4yIDE2IDE2IDE2aDUxMnpNMTUyIDQzNmg0MDB2MTUySDE1MlY0MzZ6bTU1MiAyMTBjMCA0LjQgMy42IDggOCA4aDIyNGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOEg3MTJjLTQuNCAwLTggMy42LTggOHY1NnptOC0yMDRoMjI0YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04SDcxMmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PicLeftOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PicLeftOutlined';
}
var _default = exports.default = RefIcon;