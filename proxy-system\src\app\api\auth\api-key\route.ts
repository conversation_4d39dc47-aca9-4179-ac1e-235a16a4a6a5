import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getUserFromToken, generateApiKey } from '@/lib/auth';

// 获取API密钥
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const user = await getUserFromToken(authHeader);

    if (!user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      apiKey: user.apiKey,
    });

  } catch (error) {
    console.error('Get API key error:', error);
    return NextResponse.json(
      { error: '获取API密钥失败' },
      { status: 500 }
    );
  }
}

// 重新生成API密钥
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const user = await getUserFromToken(authHeader);

    if (!user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const newApiKey = generateApiKey();

    const updatedUser = await db.user.update({
      where: { id: user.id },
      data: { apiKey: newApiKey },
      select: {
        id: true,
        email: true,
        username: true,
        apiKey: true,
        balance: true,
        currency: true,
      },
    });

    return NextResponse.json({
      message: 'API密钥已重新生成',
      apiKey: updatedUser.apiKey,
    });

  } catch (error) {
    console.error('Regenerate API key error:', error);
    return NextResponse.json(
      { error: '重新生成API密钥失败' },
      { status: 500 }
    );
  }
}
