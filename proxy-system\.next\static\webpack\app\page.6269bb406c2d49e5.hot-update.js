"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StarFilled.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 52\n            }, this),\n            color: '#1890ff'\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 56\n            }, this),\n            color: '#52c41a'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 53\n            }, this),\n            color: '#faad14'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 43\n            }, this),\n            color: '#722ed1'\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#1890ff'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',\n            highlight: 'HTTP/HTTPS & SOCKS5'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#52c41a'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            title: '全球节点覆盖',\n            description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',\n            highlight: '50+ 国家地区'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#faad14'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            title: '极速稳定',\n            description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',\n            highlight: '99.9% 稳定性'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#722ed1'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',\n            highlight: '军用级加密'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#eb2f96'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this),\n            title: '实时监控',\n            description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',\n            highlight: '实时监控面板'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#13c2c2'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, this),\n            title: 'API集成',\n            description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',\n            highlight: 'RESTful API'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 16px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1200px',\n                            margin: '0 auto',\n                            height: '64px'\n                        },\n                        className: \"jsx-24d3f5d8be7854ab\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '12px',\n                                        minWidth: 0\n                                    },\n                                    className: \"jsx-24d3f5d8be7854ab\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 40,\n                                            style: {\n                                                backgroundColor: '#2563eb',\n                                                flexShrink: 0\n                                            },\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: 0\n                                            },\n                                            className: \"jsx-24d3f5d8be7854ab\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 4,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#2563eb',\n                                                        fontSize: '18px',\n                                                        lineHeight: '1.2',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    style: {\n                                                        fontSize: '11px',\n                                                        lineHeight: '1',\n                                                        display: 'block',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"企业级代理服务平台\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"帮助文档\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"vertical\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"注册\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"24d3f5d8be7854ab\",\n                        children: \"@media(min-width:768px){.desktop-nav.jsx-24d3f5d8be7854ab{display:block!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:none!important}}@media(max-width:767px){.desktop-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}@media(max-width:480px){.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 32,\n                            style: {\n                                backgroundColor: '#2563eb'\n                            },\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 21\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: '#2563eb',\n                                fontWeight: 'bold'\n                            },\n                            children: \"ProxyHub\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 280,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDE80 产品特性\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDCB0 价格方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDCDA 帮助文档\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '48px'\n                                },\n                                children: \"\\uD83D\\uDD10 登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '48px'\n                                },\n                                children: \"\\uD83D\\uDCDD 免费注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            padding: '100px 24px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.3\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Ribbon, {\n                                        text: \"\\uD83D\\uDE80 企业首选\",\n                                        color: \"#52c41a\",\n                                        style: {\n                                            marginBottom: '24px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-block',\n                                                marginBottom: '32px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        style: {\n                                                            color: '#faad14'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.9)',\n                                                            fontSize: '14px'\n                                                        },\n                                                        children: \"已服务 10万+ 企业用户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        style: {\n                                                            color: '#faad14'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                                            marginBottom: '24px',\n                                            color: '#fff',\n                                            textShadow: '0 2px 4px rgba(0,0,0,0.3)',\n                                            lineHeight: '1.2'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDF10 全球代理服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 24\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #ffd700, #ffed4e)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text'\n                                                },\n                                                children: \"企业级解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                                            color: 'rgba(255,255,255,0.9)',\n                                            marginBottom: '48px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 48px auto',\n                                            lineHeight: '1.6',\n                                            textShadow: '0 1px 2px rgba(0,0,0,0.2)'\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDD25 提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 52\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"⚡ 99.9%稳定性保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83C\\uDF0D 50+国家覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83D\\uDEE1️ 企业级安全\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        style: {\n                                            marginBottom: '64px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        fontWeight: 'bold',\n                                                        background: 'linear-gradient(45deg, #1890ff, #36cfc9)',\n                                                        border: 'none',\n                                                        boxShadow: '0 4px 15px rgba(24, 144, 255, 0.4)'\n                                                    },\n                                                    children: \"立即免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        background: 'rgba(255,255,255,0.1)',\n                                                        border: '2px solid rgba(255,255,255,0.3)',\n                                                        color: '#fff',\n                                                        backdropFilter: 'blur(10px)'\n                                                    },\n                                                    children: \"观看演示\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        gutter: [\n                                            24,\n                                            24\n                                        ],\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    style: {\n                                                        textAlign: 'center',\n                                                        background: 'rgba(255,255,255,0.95)',\n                                                        backdropFilter: 'blur(10px)',\n                                                        border: '1px solid rgba(255,255,255,0.2)',\n                                                        borderRadius: '16px',\n                                                        boxShadow: '0 8px 32px rgba(0,0,0,0.1)',\n                                                        transition: 'all 0.3s ease',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    hoverable: true,\n                                                    bodyStyle: {\n                                                        padding: '24px 16px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '12px'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    fontSize: '2rem',\n                                                                    color: stat.color,\n                                                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                                                },\n                                                                children: stat.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                style: {\n                                                                    color: '#666',\n                                                                    fontSize: '14px',\n                                                                    fontWeight: '500'\n                                                                },\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            value: stat.value,\n                                                            suffix: stat.suffix,\n                                                            valueStyle: {\n                                                                color: stat.color,\n                                                                fontWeight: 'bold',\n                                                                fontSize: '24px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});