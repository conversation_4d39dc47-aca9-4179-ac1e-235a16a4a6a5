"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloudOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('1');\n    // 核心统计数据\n    const stats = [\n        {\n            title: '全球节点',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            color: '#3b82f6',\n            description: '覆盖全球主要国家和地区'\n        },\n        {\n            title: '企业用户',\n            value: 10000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            color: '#10b981',\n            description: '服务全球企业客户'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, this),\n            color: '#f59e0b',\n            description: 'SLA 服务等级保证'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, this),\n            color: '#8b5cf6',\n            description: '全天候专业技术支持'\n        }\n    ];\n    // 核心功能特性\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',\n            tags: [\n                'HTTP/HTTPS',\n                'SOCKS5',\n                '高兼容性'\n            ],\n            color: '#3b82f6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, this),\n            title: '全球节点网络',\n            description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',\n            tags: [\n                '50+ 国家',\n                '低延迟',\n                '高速度'\n            ],\n            color: '#10b981'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，多重安全防护，保障数据传输安全',\n            tags: [\n                '军用级加密',\n                '多重防护',\n                '隐私保护'\n            ],\n            color: '#f59e0b'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, this),\n            title: '智能监控',\n            description: '实时监控系统状态，智能故障检测，确保服务稳定运行',\n            tags: [\n                '实时监控',\n                '智能检测',\n                '自动恢复'\n            ],\n            color: '#8b5cf6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 13\n            }, this),\n            title: 'API 集成',\n            description: '完整的 RESTful API，支持自动化管理和第三方系统集成',\n            tags: [\n                'RESTful API',\n                '自动化',\n                '易集成'\n            ],\n            color: '#ef4444'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, this),\n            title: '灵活配置',\n            description: '支持自定义配置，满足不同业务场景的个性化需求',\n            tags: [\n                '自定义配置',\n                '灵活部署',\n                '场景适配'\n            ],\n            color: '#06b6d4'\n        }\n    ];\n    // 使用步骤\n    const steps = [\n        {\n            title: '注册账户',\n            description: '快速注册，获取专属 API 密钥',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '选择套餐',\n            description: '根据业务需求选择合适的服务套餐',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '配置接入',\n            description: '通过 API 或控制面板配置代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '开始使用',\n            description: '享受稳定高效的全球代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: '某大型电商平台',\n            industry: '电子商务',\n            content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',\n            avatar: 'E',\n            color: '#3b82f6'\n        },\n        {\n            company: '某金融科技公司',\n            industry: '金融科技',\n            content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',\n            avatar: 'F',\n            color: '#10b981'\n        },\n        {\n            company: '某数据分析公司',\n            industry: '数据分析',\n            content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',\n            avatar: 'D',\n            color: '#f59e0b'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                    padding: '0 24px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000,\n                    borderBottom: '1px solid rgba(0,0,0,0.06)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1400px',\n                            margin: '0 auto',\n                            height: '72px'\n                        },\n                        className: \"jsx-2af30dff10d881d1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '16px'\n                                    },\n                                    className: \"jsx-2af30dff10d881d1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '48px',\n                                                height: '48px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                            },\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                style: {\n                                                    fontSize: '24px',\n                                                    color: '#fff'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 3,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#1e293b',\n                                                        fontSize: '24px',\n                                                        fontWeight: '700',\n                                                        lineHeight: '1'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '13px',\n                                                        color: '#64748b',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Enterprise Proxy Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-2af30dff10d881d1\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"开发者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e2e8f0',\n                                                height: '24px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#475569',\n                                                    fontSize: '15px',\n                                                    height: '40px',\n                                                    borderRadius: '8px'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.target.style.color = '#3b82f6';\n                                                    e.target.style.background = '#f1f5f9';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.target.style.color = '#475569';\n                                                    e.target.style.background = 'transparent';\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                    borderColor: 'transparent',\n                                                    borderRadius: '10px',\n                                                    height: '44px',\n                                                    paddingLeft: '24px',\n                                                    paddingRight: '24px',\n                                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                                                    border: 'none'\n                                                },\n                                                children: \"免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                className: \"jsx-2af30dff10d881d1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-2af30dff10d881d1\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '8px',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none',\n                                            fontSize: '18px',\n                                            width: '44px',\n                                            height: '44px',\n                                            borderRadius: '8px'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"2af30dff10d881d1\",\n                        children: \"@media(min-width:1024px){.desktop-nav.jsx-2af30dff10d881d1{display:block!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:1023px)and (min-width:640px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:639px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '10px',\n                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                style: {\n                                    fontSize: '20px',\n                                    color: '#fff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#1e293b',\n                                        fontWeight: '700',\n                                        fontSize: '18px'\n                                    },\n                                    children: \"ProxyHub\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#64748b',\n                                        fontSize: '12px'\n                                    },\n                                    children: \"Enterprise Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 320,\n                styles: {\n                    body: {\n                        padding: '24px'\n                    },\n                    header: {\n                        borderBottom: '1px solid #f1f5f9',\n                        paddingBottom: '16px'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"导航菜单\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#3b82f6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                \"产品特性\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#10b981'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                \"解决方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#f59e0b'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                \"价格方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#8b5cf6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                \"开发者\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            style: {\n                                margin: '24px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"账户操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#475569',\n                                    borderRadius: '12px',\n                                    border: '1px solid #e2e8f0'\n                                },\n                                children: \"登录账户\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                    borderColor: 'transparent',\n                                    borderRadius: '12px',\n                                    marginTop: '8px',\n                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                },\n                                children: \"免费试用\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',\n                            padding: '140px 24px 120px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.6\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '20%',\n                                    left: '10%',\n                                    width: '300px',\n                                    height: '300px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(40px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '60%',\n                                    right: '10%',\n                                    width: '200px',\n                                    height: '200px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(30px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '56px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '16px',\n                                                background: 'rgba(255,255,255,0.08)',\n                                                backdropFilter: 'blur(20px)',\n                                                border: '1px solid rgba(255,255,255,0.12)',\n                                                borderRadius: '60px',\n                                                padding: '12px 32px',\n                                                marginBottom: '32px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '10px',\n                                                                height: '10px',\n                                                                borderRadius: '50%',\n                                                                background: '#10b981',\n                                                                boxShadow: '0 0 12px #10b981',\n                                                                animation: 'pulse 2s infinite'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"服务 10,000+ 企业客户\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"vertical\",\n                                                    style: {\n                                                        borderColor: 'rgba(255,255,255,0.2)',\n                                                        height: '16px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            style: {\n                                                                color: '#f59e0b',\n                                                                fontSize: '16px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"行业领先\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(3rem, 8vw, 5rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '800',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.03em',\n                                            textShadow: '0 4px 20px rgba(0,0,0,0.3)'\n                                        },\n                                        children: [\n                                            \"企业级全球代理\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #3b82f6, #06b6d4)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text',\n                                                    fontWeight: '800'\n                                                },\n                                                children: \"解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.2rem, 3vw, 1.5rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '56px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 56px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"为企业提供稳定、安全、高速的全球代理网络服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% SLA 保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#34d399',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家节点\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"24/7 技术支持\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                wrap: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/register\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            type: \"primary\",\n                                                            size: \"large\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            style: {\n                                                                height: '64px',\n                                                                fontSize: '18px',\n                                                                fontWeight: '700',\n                                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                                borderColor: 'transparent',\n                                                                borderRadius: '16px',\n                                                                paddingLeft: '40px',\n                                                                paddingRight: '40px',\n                                                                boxShadow: '0 12px 32px rgba(59, 130, 246, 0.4)',\n                                                                border: 'none',\n                                                                minWidth: '200px'\n                                                            },\n                                                            children: \"立即免费试用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: \"large\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 25\n                                                        }, void 0),\n                                                        style: {\n                                                            height: '64px',\n                                                            fontSize: '18px',\n                                                            fontWeight: '600',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            border: '2px solid rgba(255,255,255,0.16)',\n                                                            color: '#ffffff',\n                                                            backdropFilter: 'blur(20px)',\n                                                            borderRadius: '16px',\n                                                            paddingLeft: '40px',\n                                                            paddingRight: '40px',\n                                                            minWidth: '180px'\n                                                        },\n                                                        children: \"观看演示\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '48px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.6)',\n                                                            fontSize: '14px',\n                                                            display: 'block',\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: \"已有数千家企业选择我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: \"large\",\n                                                        wrap: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"10K+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"企业用户\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"50+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"国家节点\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"99.9%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"稳定性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: 'rgba(255,255,255,0.05)',\n                                            backdropFilter: 'blur(20px)',\n                                            border: '1px solid rgba(255,255,255,0.1)',\n                                            borderRadius: '32px',\n                                            padding: '48px 32px',\n                                            marginBottom: '0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            gutter: [\n                                                32,\n                                                32\n                                            ],\n                                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    xs: 12,\n                                                    lg: 6,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            textAlign: 'center',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            backdropFilter: 'blur(20px)',\n                                                            border: '1px solid rgba(255,255,255,0.12)',\n                                                            borderRadius: '24px',\n                                                            padding: '40px 24px',\n                                                            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                            cursor: 'default',\n                                                            position: 'relative',\n                                                            overflow: 'hidden'\n                                                        },\n                                                        onMouseEnter: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.12)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)';\n                                                        },\n                                                        onMouseLeave: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.08)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.12)';\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'absolute',\n                                                                    top: '50%',\n                                                                    left: '50%',\n                                                                    transform: 'translate(-50%, -50%)',\n                                                                    width: '120px',\n                                                                    height: '120px',\n                                                                    borderRadius: '50%',\n                                                                    background: \"radial-gradient(circle, \".concat(stat.color, \"15 0%, transparent 70%)\"),\n                                                                    filter: 'blur(20px)',\n                                                                    zIndex: 0\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'relative',\n                                                                    zIndex: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '24px',\n                                                                            display: 'flex',\n                                                                            justifyContent: 'center',\n                                                                            alignItems: 'center'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                width: '72px',\n                                                                                height: '72px',\n                                                                                borderRadius: '20px',\n                                                                                background: \"linear-gradient(135deg, \".concat(stat.color, \"20, \").concat(stat.color, \"30)\"),\n                                                                                display: 'flex',\n                                                                                alignItems: 'center',\n                                                                                justifyContent: 'center',\n                                                                                border: \"2px solid \".concat(stat.color, \"40\"),\n                                                                                boxShadow: \"0 8px 32px \".concat(stat.color, \"20\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    fontSize: '32px',\n                                                                                    color: stat.color,\n                                                                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                                                                },\n                                                                                children: stat.icon\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 912,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 901,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '12px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: '#ffffff',\n                                                                                fontSize: '36px',\n                                                                                fontWeight: '800',\n                                                                                lineHeight: '1',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: [\n                                                                                stat.value,\n                                                                                stat.suffix\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: 'rgba(255,255,255,0.9)',\n                                                                                fontSize: '16px',\n                                                                                fontWeight: '600',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: stat.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 936,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '13px',\n                                                                            lineHeight: '1.4'\n                                                                        },\n                                                                        children: stat.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1022,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 1186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"R+rp0qcabHV79vV49OYD5uovxls=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});