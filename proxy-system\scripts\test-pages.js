#!/usr/bin/env node

/**
 * 页面测试脚本
 * 测试所有页面是否能正常访问和渲染
 */

const http = require('http');
const { URL } = require('url');

const BASE_URL = 'http://localhost:3000';
const PAGES = [
  { path: '/', name: '首页' },
  { path: '/login', name: '登录页面' },
  { path: '/register', name: '注册页面' },
  { path: '/dashboard', name: '仪表板' }
];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function testPage(path, name) {
  return new Promise((resolve) => {
    const url = new URL(path, BASE_URL);
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const success = res.statusCode === 200;
        const hasAntd = data.includes('antd') || data.includes('ant-design');
        const hasReact = data.includes('__NEXT_DATA__');
        
        resolve({
          path,
          name,
          statusCode: res.statusCode,
          success,
          hasAntd,
          hasReact,
          size: data.length
        });
      });
    });
    
    req.on('error', (error) => {
      resolve({
        path,
        name,
        statusCode: 0,
        success: false,
        hasAntd: false,
        hasReact: false,
        error: error.message,
        size: 0
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        path,
        name,
        statusCode: 0,
        success: false,
        hasAntd: false,
        hasReact: false,
        error: 'Timeout',
        size: 0
      });
    });
  });
}

async function runTests() {
  log('🚀 开始测试 ProxyHub 页面...', 'blue');
  log('=' .repeat(50), 'blue');
  
  const results = [];
  
  for (const page of PAGES) {
    log(`\n📄 测试 ${page.name} (${page.path})...`, 'yellow');
    
    const result = await testPage(page.path, page.name);
    results.push(result);
    
    if (result.success) {
      log(`✅ ${result.name}: 状态码 ${result.statusCode}`, 'green');
      log(`   📦 页面大小: ${(result.size / 1024).toFixed(2)} KB`, 'green');
      
      if (result.hasAntd) {
        log(`   🎨 Ant Design: 已集成`, 'green');
      } else {
        log(`   ⚠️  Ant Design: 未检测到`, 'yellow');
      }
      
      if (result.hasReact) {
        log(`   ⚛️  React/Next.js: 正常`, 'green');
      }
    } else {
      log(`❌ ${result.name}: 失败`, 'red');
      if (result.error) {
        log(`   错误: ${result.error}`, 'red');
      } else {
        log(`   状态码: ${result.statusCode}`, 'red');
      }
    }
  }
  
  // 生成测试报告
  log('\n' + '=' .repeat(50), 'blue');
  log('📊 测试报告', 'bold');
  log('=' .repeat(50), 'blue');
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  const antdCount = results.filter(r => r.hasAntd).length;
  
  log(`\n📈 总体统计:`, 'blue');
  log(`   总页面数: ${totalCount}`, 'blue');
  log(`   成功页面: ${successCount}`, successCount === totalCount ? 'green' : 'yellow');
  log(`   失败页面: ${totalCount - successCount}`, totalCount - successCount === 0 ? 'green' : 'red');
  log(`   Ant Design 集成: ${antdCount}/${totalCount}`, antdCount === totalCount ? 'green' : 'yellow');
  
  if (successCount === totalCount) {
    log('\n🎉 所有页面测试通过！', 'green');
    log('✨ ProxyHub 已成功集成 Ant Design 企业级 UI', 'green');
  } else {
    log('\n⚠️  部分页面测试失败，请检查开发服务器状态', 'yellow');
  }
  
  log('\n🔗 访问链接:', 'blue');
  results.forEach(result => {
    if (result.success) {
      log(`   ${result.name}: ${BASE_URL}${result.path}`, 'blue');
    }
  });
  
  log('\n💡 提示:', 'yellow');
  log('   1. 确保开发服务器正在运行 (npm run dev)', 'yellow');
  log('   2. 确保端口 3000 未被占用', 'yellow');
  log('   3. 检查 Ant Design 依赖是否正确安装', 'yellow');
  
  return successCount === totalCount;
}

// 检查开发服务器是否运行
function checkServer() {
  return new Promise((resolve) => {
    const req = http.get(BASE_URL, (res) => {
      resolve(true);
    });
    
    req.on('error', () => {
      resolve(false);
    });
    
    req.setTimeout(2000, () => {
      req.destroy();
      resolve(false);
    });
  });
}

async function main() {
  log('🔍 检查开发服务器状态...', 'blue');
  
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    log('❌ 开发服务器未运行！', 'red');
    log('请先启动开发服务器:', 'yellow');
    log('   npm run dev', 'yellow');
    log('   或', 'yellow');
    log('   yarn dev', 'yellow');
    process.exit(1);
  }
  
  log('✅ 开发服务器正在运行', 'green');
  
  const success = await runTests();
  process.exit(success ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    log(`❌ 测试过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { runTests, testPage };
