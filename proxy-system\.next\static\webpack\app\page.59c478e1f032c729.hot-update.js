"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RocketOutlined.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/RocketOutlined.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar RocketOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M864 736c0-111.6-65.4-208-160-252.9V317.3c0-15.1-5.3-29.7-15.1-41.2L536.5 95.4C530.1 87.8 521 84 512 84s-18.1 3.8-24.5 11.4L335.1 276.1a63.97 63.97 0 00-15.1 41.2v165.8C225.4 528 160 624.4 160 736h156.5c-2.3 7.2-3.5 15-3.5 23.8 0 22.1 7.6 43.7 21.4 60.8a97.2 97.2 0 0043.1 30.6c23.1 54 75.6 88.8 134.5 88.8 29.1 0 57.3-8.6 81.4-24.8 23.6-15.8 41.9-37.9 53-64a97 97 0 0043.1-30.5 97.52 97.52 0 0021.4-60.8c0-8.4-1.1-16.4-3.1-23.8H864zM762.3 621.4c9.4 14.6 17 30.3 22.5 46.6H700V558.7a211.6 211.6 0 0162.3 62.7zM388 483.1V318.8l124-147 124 147V668H388V483.1zM239.2 668c5.5-16.3 13.1-32 22.5-46.6 16.3-25.2 37.5-46.5 62.3-62.7V668h-84.8zm388.9 116.2c-5.2 3-11.2 4.2-17.1 3.4l-19.5-2.4-2.8 19.4c-5.4 37.9-38.4 66.5-76.7 66.5-38.3 0-71.3-28.6-76.7-66.5l-2.8-19.5-19.5 2.5a27.7 27.7 0 01-17.1-3.5c-8.7-5-14.1-14.3-14.1-24.4 0-10.6 5.9-19.4 14.6-23.8h231.3c8.8 4.5 14.6 13.3 14.6 23.8-.1 10.2-5.5 19.6-14.2 24.5zM464 400a48 48 0 1096 0 48 48 0 10-96 0z\" } }] }, \"name\": \"rocket\", \"theme\": \"outlined\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RocketOutlined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RocketOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/StarFilled.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@ant-design/icons-svg/es/asn/StarFilled.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// This icon file is generated automatically.\nvar StarFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z\" } }] }, \"name\": \"star\", \"theme\": \"filled\" };\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StarFilled);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AYW50LWRlc2lnbi9pY29ucy1zdmcvZXMvYXNuL1N0YXJGaWxsZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsbUJBQW1CLFVBQVUseUJBQXlCLGtEQUFrRCxpQkFBaUIsMEJBQTBCLGdYQUFnWCxHQUFHO0FBQ3RnQixpRUFBZSxVQUFVLEVBQUMiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcbm9kZV9tb2R1bGVzXFxAYW50LWRlc2lnblxcaWNvbnMtc3ZnXFxlc1xcYXNuXFxTdGFyRmlsbGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFRoaXMgaWNvbiBmaWxlIGlzIGdlbmVyYXRlZCBhdXRvbWF0aWNhbGx5LlxudmFyIFN0YXJGaWxsZWQgPSB7IFwiaWNvblwiOiB7IFwidGFnXCI6IFwic3ZnXCIsIFwiYXR0cnNcIjogeyBcInZpZXdCb3hcIjogXCI2NCA2NCA4OTYgODk2XCIsIFwiZm9jdXNhYmxlXCI6IFwiZmFsc2VcIiB9LCBcImNoaWxkcmVuXCI6IFt7IFwidGFnXCI6IFwicGF0aFwiLCBcImF0dHJzXCI6IHsgXCJkXCI6IFwiTTkwOC4xIDM1My4xbC0yNTMuOS0zNi45TDU0MC43IDg2LjFjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzY5LjggMzE2LjJsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMS00My40IDI1Mi45YTMxLjk1IDMxLjk1IDAgMDA0Ni40IDMzLjdMNTEyIDc1NGwyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6XCIgfSB9XSB9LCBcIm5hbWVcIjogXCJzdGFyXCIsIFwidGhlbWVcIjogXCJmaWxsZWRcIiB9O1xuZXhwb3J0IGRlZmF1bHQgU3RhckZpbGxlZDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/StarFilled.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/RocketOutlined.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_RocketOutlined__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/RocketOutlined */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/RocketOutlined.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nconst RocketOutlined = (props, ref)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _extends({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_RocketOutlined__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }));\n_c = RocketOutlined;\n/**![rocket](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCA3MzZjMC0xMTEuNi02NS40LTIwOC0xNjAtMjUyLjlWMzE3LjNjMC0xNS4xLTUuMy0yOS43LTE1LjEtNDEuMkw1MzYuNSA5NS40QzUzMC4xIDg3LjggNTIxIDg0IDUxMiA4NHMtMTguMSAzLjgtMjQuNSAxMS40TDMzNS4xIDI3Ni4xYTYzLjk3IDYzLjk3IDAgMDAtMTUuMSA0MS4ydjE2NS44QzIyNS40IDUyOCAxNjAgNjI0LjQgMTYwIDczNmgxNTYuNWMtMi4zIDcuMi0zLjUgMTUtMy41IDIzLjggMCAyMi4xIDcuNiA0My43IDIxLjQgNjAuOGE5Ny4yIDk3LjIgMCAwMDQzLjEgMzAuNmMyMy4xIDU0IDc1LjYgODguOCAxMzQuNSA4OC44IDI5LjEgMCA1Ny4zLTguNiA4MS40LTI0LjggMjMuNi0xNS44IDQxLjktMzcuOSA1My02NGE5NyA5NyAwIDAwNDMuMS0zMC41IDk3LjUyIDk3LjUyIDAgMDAyMS40LTYwLjhjMC04LjQtMS4xLTE2LjQtMy4xLTIzLjhIODY0ek03NjIuMyA2MjEuNGM5LjQgMTQuNiAxNyAzMC4zIDIyLjUgNDYuNkg3MDBWNTU4LjdhMjExLjYgMjExLjYgMCAwMTYyLjMgNjIuN3pNMzg4IDQ4My4xVjMxOC44bDEyNC0xNDcgMTI0IDE0N1Y2NjhIMzg4VjQ4My4xek0yMzkuMiA2NjhjNS41LTE2LjMgMTMuMS0zMiAyMi41LTQ2LjYgMTYuMy0yNS4yIDM3LjUtNDYuNSA2Mi4zLTYyLjdWNjY4aC04NC44em0zODguOSAxMTYuMmMtNS4yIDMtMTEuMiA0LjItMTcuMSAzLjRsLTE5LjUtMi40LTIuOCAxOS40Yy01LjQgMzcuOS0zOC40IDY2LjUtNzYuNyA2Ni41LTM4LjMgMC03MS4zLTI4LjYtNzYuNy02Ni41bC0yLjgtMTkuNS0xOS41IDIuNWEyNy43IDI3LjcgMCAwMS0xNy4xLTMuNWMtOC43LTUtMTQuMS0xNC4zLTE0LjEtMjQuNCAwLTEwLjYgNS45LTE5LjQgMTQuNi0yMy44aDIzMS4zYzguOCA0LjUgMTQuNiAxMy4zIDE0LjYgMjMuOC0uMSAxMC4yLTUuNSAxOS42LTE0LjIgMjQuNXpNNDY0IDQwMGE0OCA0OCAwIDEwOTYgMCA0OCA0OCAwIDEwLTk2IDB6IiAvPjwvc3ZnPg==) */ const RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(RocketOutlined);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'RocketOutlined';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"RocketOutlined\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StarFilled.js":
/*!***************************************************************!*\
  !*** ./node_modules/@ant-design/icons/es/icons/StarFilled.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ant_design_icons_svg_es_asn_StarFilled__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ant-design/icons-svg/es/asn/StarFilled */ \"(app-pages-browser)/./node_modules/@ant-design/icons-svg/es/asn/StarFilled.js\");\n/* harmony import */ var _components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/AntdIcon */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/components/AntdIcon.js\");\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\n\n\nconst StarFilled = (props, ref)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_components_AntdIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"], _extends({}, props, {\n        ref: ref,\n        icon: _ant_design_icons_svg_es_asn_StarFilled__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    }));\n_c = StarFilled;\n/**![star](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwOC4xIDM1My4xbC0yNTMuOS0zNi45TDU0MC43IDg2LjFjLTMuMS02LjMtOC4yLTExLjQtMTQuNS0xNC41LTE1LjgtNy44LTM1LTEuMy00Mi45IDE0LjVMMzY5LjggMzE2LjJsLTI1My45IDM2LjljLTcgMS0xMy40IDQuMy0xOC4zIDkuM2EzMi4wNSAzMi4wNSAwIDAwLjYgNDUuM2wxODMuNyAxNzkuMS00My40IDI1Mi45YTMxLjk1IDMxLjk1IDAgMDA0Ni40IDMzLjdMNTEyIDc1NGwyMjcuMSAxMTkuNGM2LjIgMy4zIDEzLjQgNC40IDIwLjMgMy4yIDE3LjQtMyAyOS4xLTE5LjUgMjYuMS0zNi45bC00My40LTI1Mi45IDE4My43LTE3OS4xYzUtNC45IDguMy0xMS4zIDkuMy0xOC4zIDIuNy0xNy41LTkuNS0zMy43LTI3LTM2LjN6IiAvPjwvc3ZnPg==) */ const RefIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(StarFilled);\n_c1 = RefIcon;\nif (true) {\n    RefIcon.displayName = 'StarFilled';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RefIcon);\nvar _c, _c1;\n$RefreshReg$(_c, \"StarFilled\");\n$RefreshReg$(_c1, \"RefIcon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StarFilled.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/Ribbon.js":
/*!**********************************************!*\
  !*** ./node_modules/antd/es/badge/Ribbon.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _util_colors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_util/colors */ \"(app-pages-browser)/./node_modules/antd/es/_util/colors.js\");\n/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config-provider */ \"(app-pages-browser)/./node_modules/antd/es/config-provider/context.js\");\n/* harmony import */ var _style_ribbon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./style/ribbon */ \"(app-pages-browser)/./node_modules/antd/es/badge/style/ribbon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\n\n\n\nconst Ribbon = (props)=>{\n    _s();\n    const { className, prefixCls: customizePrefixCls, style, color, children, text, placement = 'end', rootClassName } = props;\n    const { getPrefixCls, direction } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_2__.ConfigContext);\n    const prefixCls = getPrefixCls('ribbon', customizePrefixCls);\n    const wrapperCls = \"\".concat(prefixCls, \"-wrapper\");\n    const [wrapCSSVar, hashId, cssVarCls] = (0,_style_ribbon__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(prefixCls, wrapperCls);\n    const colorInPreset = (0,_util_colors__WEBPACK_IMPORTED_MODULE_4__.isPresetColor)(color, false);\n    const ribbonCls = classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, \"\".concat(prefixCls, \"-placement-\").concat(placement), {\n        [\"\".concat(prefixCls, \"-rtl\")]: direction === 'rtl',\n        [\"\".concat(prefixCls, \"-color-\").concat(color)]: colorInPreset\n    }, className);\n    const colorStyle = {};\n    const cornerColorStyle = {};\n    if (color && !colorInPreset) {\n        colorStyle.background = color;\n        cornerColorStyle.color = color;\n    }\n    return wrapCSSVar(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(wrapperCls, rootClassName, hashId, cssVarCls)\n    }, children, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(ribbonCls, hashId),\n        style: Object.assign(Object.assign({}, colorStyle), style)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-text\")\n    }, text), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"\".concat(prefixCls, \"-corner\"),\n        style: cornerColorStyle\n    }))));\n};\n_s(Ribbon, \"R58vX1NnXe/wfkEX/w3Nbd6L/g4=\", false, function() {\n    return [\n        _style_ribbon__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = Ribbon;\nif (true) {\n    Ribbon.displayName = 'Ribbon';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Ribbon);\nvar _c;\n$RefreshReg$(_c, \"Ribbon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/Ribbon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/ScrollNumber.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/badge/ScrollNumber.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_util/reactNode */ \"(app-pages-browser)/./node_modules/antd/es/_util/reactNode.js\");\n/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config-provider */ \"(app-pages-browser)/./node_modules/antd/es/config-provider/context.js\");\n/* harmony import */ var _SingleNumber__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SingleNumber */ \"(app-pages-browser)/./node_modules/antd/es/badge/SingleNumber.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\nconst ScrollNumber = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s((props, ref)=>{\n    _s();\n    const { prefixCls: customizePrefixCls, count, className, motionClassName, style, title, show, component: Component = 'sup', children } = props, restProps = __rest(props, [\n        \"prefixCls\",\n        \"count\",\n        \"className\",\n        \"motionClassName\",\n        \"style\",\n        \"title\",\n        \"show\",\n        \"component\",\n        \"children\"\n    ]);\n    const { getPrefixCls } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_2__.ConfigContext);\n    const prefixCls = getPrefixCls('scroll-number', customizePrefixCls);\n    // ============================ Render ============================\n    const newProps = Object.assign(Object.assign({}, restProps), {\n        'data-show': show,\n        style,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, className, motionClassName),\n        title: title\n    });\n    // Only integer need motion\n    let numberNodes = count;\n    if (count && Number(count) % 1 === 0) {\n        const numberList = String(count).split('');\n        numberNodes = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"bdi\", null, numberList.map((num, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_SingleNumber__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                prefixCls: prefixCls,\n                count: Number(count),\n                value: num,\n                // eslint-disable-next-line react/no-array-index-key\n                key: numberList.length - i\n            })));\n    }\n    // allow specify the border\n    // mock border-color by box-shadow for compatible with old usage:\n    // <Badge count={4} style={{ backgroundColor: '#fff', color: '#999', borderColor: '#d9d9d9' }} />\n    if (style === null || style === void 0 ? void 0 : style.borderColor) {\n        newProps.style = Object.assign(Object.assign({}, style), {\n            boxShadow: \"0 0 0 1px \".concat(style.borderColor, \" inset\")\n        });\n    }\n    if (children) {\n        return (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_4__.cloneElement)(children, (oriProps)=>({\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-custom-component\"), oriProps === null || oriProps === void 0 ? void 0 : oriProps.className, motionClassName)\n            }));\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, Object.assign({}, newProps, {\n        ref: ref\n    }), numberNodes);\n}, \"p0+esITaFKQ8uLDWWK2RcvnP7tM=\")), \"p0+esITaFKQ8uLDWWK2RcvnP7tM=\");\n_c1 = ScrollNumber;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ScrollNumber);\nvar _c, _c1;\n$RefreshReg$(_c, \"ScrollNumber$React.forwardRef\");\n$RefreshReg$(_c1, \"ScrollNumber\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/ScrollNumber.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/SingleNumber.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/badge/SingleNumber.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nconst UnitNumber = (props)=>{\n    const { prefixCls, value, current, offset = 0 } = props;\n    let style;\n    if (offset) {\n        style = {\n            position: 'absolute',\n            top: \"\".concat(offset, \"00%\"),\n            left: 0\n        };\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        style: style,\n        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-only-unit\"), {\n            current\n        })\n    }, value);\n};\n_c = UnitNumber;\nfunction getOffset(start, end, unit) {\n    let index = start;\n    let offset = 0;\n    while((index + 10) % 10 !== end){\n        index += unit;\n        offset += unit;\n    }\n    return offset;\n}\nconst SingleNumber = (props)=>{\n    _s();\n    const { prefixCls, count: originCount, value: originValue } = props;\n    const value = Number(originValue);\n    const count = Math.abs(originCount);\n    const [prevValue, setPrevValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(value);\n    const [prevCount, setPrevCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(count);\n    // ============================= Events =============================\n    const onTransitionEnd = ()=>{\n        setPrevValue(value);\n        setPrevCount(count);\n    };\n    // Fallback if transition events are not supported\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SingleNumber.useEffect\": ()=>{\n            const timer = setTimeout(onTransitionEnd, 1000);\n            return ({\n                \"SingleNumber.useEffect\": ()=>clearTimeout(timer)\n            })[\"SingleNumber.useEffect\"];\n        }\n    }[\"SingleNumber.useEffect\"], [\n        value\n    ]);\n    // ============================= Render =============================\n    // Render unit list\n    let unitNodes;\n    let offsetStyle;\n    if (prevValue === value || Number.isNaN(value) || Number.isNaN(prevValue)) {\n        // Nothing to change\n        unitNodes = [\n            /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UnitNumber, Object.assign({}, props, {\n                key: value,\n                current: true\n            }))\n        ];\n        offsetStyle = {\n            transition: 'none'\n        };\n    } else {\n        unitNodes = [];\n        // Fill basic number units\n        const end = value + 10;\n        const unitNumberList = [];\n        for(let index = value; index <= end; index += 1){\n            unitNumberList.push(index);\n        }\n        const unit = prevCount < count ? 1 : -1;\n        // Fill with number unit nodes\n        const prevIndex = unitNumberList.findIndex((n)=>n % 10 === prevValue);\n        // Cut list\n        const cutUnitNumberList = unit < 0 ? unitNumberList.slice(0, prevIndex + 1) : unitNumberList.slice(prevIndex);\n        unitNodes = cutUnitNumberList.map((n, index)=>{\n            const singleUnit = n % 10;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(UnitNumber, Object.assign({}, props, {\n                key: n,\n                value: singleUnit,\n                offset: unit < 0 ? index - prevIndex : index,\n                current: index === prevIndex\n            }));\n        });\n        // Calculate container offset value\n        offsetStyle = {\n            transform: \"translateY(\".concat(-getOffset(prevValue, value, unit), \"00%)\")\n        };\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-only\"),\n        style: offsetStyle,\n        onTransitionEnd: onTransitionEnd\n    }, unitNodes);\n};\n_s(SingleNumber, \"v+dVZFaQKFB3w0T2LEk16Zr+8Os=\");\n_c1 = SingleNumber;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SingleNumber);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnitNumber\");\n$RefreshReg$(_c1, \"SingleNumber\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/SingleNumber.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/index.js":
/*!*********************************************!*\
  !*** ./node_modules/antd/es/badge/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-motion */ \"(app-pages-browser)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var _util_colors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../_util/colors */ \"(app-pages-browser)/./node_modules/antd/es/_util/colors.js\");\n/* harmony import */ var _util_reactNode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../_util/reactNode */ \"(app-pages-browser)/./node_modules/antd/es/_util/reactNode.js\");\n/* harmony import */ var _config_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../config-provider */ \"(app-pages-browser)/./node_modules/antd/es/config-provider/context.js\");\n/* harmony import */ var _Ribbon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Ribbon */ \"(app-pages-browser)/./node_modules/antd/es/badge/Ribbon.js\");\n/* harmony import */ var _ScrollNumber__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ScrollNumber */ \"(app-pages-browser)/./node_modules/antd/es/badge/ScrollNumber.js\");\n/* harmony import */ var _style__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./style */ \"(app-pages-browser)/./node_modules/antd/es/badge/style/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar __rest = undefined && undefined.__rest || function(s, e) {\n    var t = {};\n    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){\n        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n    }\n    return t;\n};\n\n\n\n\n\n\n\n\n\n\nconst InternalBadge = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c = _s((props, ref)=>{\n    _s();\n    var _a, _b, _c, _d, _e;\n    const { prefixCls: customizePrefixCls, scrollNumberPrefixCls: customizeScrollNumberPrefixCls, children, status, text, color, count = null, overflowCount = 99, dot = false, size = 'default', title, offset, style, className, rootClassName, classNames, styles, showZero = false } = props, restProps = __rest(props, [\n        \"prefixCls\",\n        \"scrollNumberPrefixCls\",\n        \"children\",\n        \"status\",\n        \"text\",\n        \"color\",\n        \"count\",\n        \"overflowCount\",\n        \"dot\",\n        \"size\",\n        \"title\",\n        \"offset\",\n        \"style\",\n        \"className\",\n        \"rootClassName\",\n        \"classNames\",\n        \"styles\",\n        \"showZero\"\n    ]);\n    const { getPrefixCls, direction, badge } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(_config_provider__WEBPACK_IMPORTED_MODULE_3__.ConfigContext);\n    const prefixCls = getPrefixCls('badge', customizePrefixCls);\n    const [wrapCSSVar, hashId, cssVarCls] = (0,_style__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(prefixCls);\n    // ================================ Misc ================================\n    const numberedDisplayCount = count > overflowCount ? \"\".concat(overflowCount, \"+\") : count;\n    const isZero = numberedDisplayCount === '0' || numberedDisplayCount === 0;\n    const ignoreCount = count === null || isZero && !showZero;\n    const hasStatus = (status !== null && status !== undefined || color !== null && color !== undefined) && ignoreCount;\n    const hasStatusValue = status !== null && status !== undefined || !isZero;\n    const showAsDot = dot && !isZero;\n    const mergedCount = showAsDot ? '' : numberedDisplayCount;\n    const isHidden = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"InternalBadge.useMemo[isHidden]\": ()=>{\n            const isEmpty = mergedCount === null || mergedCount === undefined || mergedCount === '';\n            return (isEmpty || isZero && !showZero) && !showAsDot;\n        }\n    }[\"InternalBadge.useMemo[isHidden]\"], [\n        mergedCount,\n        isZero,\n        showZero,\n        showAsDot\n    ]);\n    // Count should be cache in case hidden change it\n    const countRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(count);\n    if (!isHidden) {\n        countRef.current = count;\n    }\n    const livingCount = countRef.current;\n    // We need cache count since remove motion should not change count display\n    const displayCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(mergedCount);\n    if (!isHidden) {\n        displayCountRef.current = mergedCount;\n    }\n    const displayCount = displayCountRef.current;\n    // We will cache the dot status to avoid shaking on leaved motion\n    const isDotRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(showAsDot);\n    if (!isHidden) {\n        isDotRef.current = showAsDot;\n    }\n    // =============================== Styles ===============================\n    const mergedStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"InternalBadge.useMemo[mergedStyle]\": ()=>{\n            if (!offset) {\n                return Object.assign(Object.assign({}, badge === null || badge === void 0 ? void 0 : badge.style), style);\n            }\n            const offsetStyle = {\n                marginTop: offset[1]\n            };\n            if (direction === 'rtl') {\n                offsetStyle.left = parseInt(offset[0], 10);\n            } else {\n                offsetStyle.right = -parseInt(offset[0], 10);\n            }\n            return Object.assign(Object.assign(Object.assign({}, offsetStyle), badge === null || badge === void 0 ? void 0 : badge.style), style);\n        }\n    }[\"InternalBadge.useMemo[mergedStyle]\"], [\n        direction,\n        offset,\n        style,\n        badge === null || badge === void 0 ? void 0 : badge.style\n    ]);\n    // =============================== Render ===============================\n    // >>> Title\n    const titleNode = title !== null && title !== void 0 ? title : typeof livingCount === 'string' || typeof livingCount === 'number' ? livingCount : undefined;\n    // >>> Status Text\n    const statusTextNode = isHidden || !text ? null : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n        className: \"\".concat(prefixCls, \"-status-text\")\n    }, text);\n    // >>> Display Component\n    const displayNode = !livingCount || typeof livingCount !== 'object' ? undefined : (0,_util_reactNode__WEBPACK_IMPORTED_MODULE_5__.cloneElement)(livingCount, (oriProps)=>({\n            style: Object.assign(Object.assign({}, mergedStyle), oriProps.style)\n        }));\n    // InternalColor\n    const isInternalColor = (0,_util_colors__WEBPACK_IMPORTED_MODULE_6__.isPresetColor)(color, false);\n    // Shared styles\n    const statusCls = classnames__WEBPACK_IMPORTED_MODULE_1___default()(classNames === null || classNames === void 0 ? void 0 : classNames.indicator, (_a = badge === null || badge === void 0 ? void 0 : badge.classNames) === null || _a === void 0 ? void 0 : _a.indicator, {\n        [\"\".concat(prefixCls, \"-status-dot\")]: hasStatus,\n        [\"\".concat(prefixCls, \"-status-\").concat(status)]: !!status,\n        [\"\".concat(prefixCls, \"-color-\").concat(color)]: isInternalColor\n    });\n    const statusStyle = {};\n    if (color && !isInternalColor) {\n        statusStyle.color = color;\n        statusStyle.background = color;\n    }\n    const badgeClassName = classnames__WEBPACK_IMPORTED_MODULE_1___default()(prefixCls, {\n        [\"\".concat(prefixCls, \"-status\")]: hasStatus,\n        [\"\".concat(prefixCls, \"-not-a-wrapper\")]: !children,\n        [\"\".concat(prefixCls, \"-rtl\")]: direction === 'rtl'\n    }, className, rootClassName, badge === null || badge === void 0 ? void 0 : badge.className, (_b = badge === null || badge === void 0 ? void 0 : badge.classNames) === null || _b === void 0 ? void 0 : _b.root, classNames === null || classNames === void 0 ? void 0 : classNames.root, hashId, cssVarCls);\n    // <Badge status=\"success\" />\n    if (!children && hasStatus && (text || hasStatusValue || !ignoreCount)) {\n        const statusTextColor = mergedStyle.color;\n        return wrapCSSVar(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", Object.assign({}, restProps, {\n            className: badgeClassName,\n            style: Object.assign(Object.assign(Object.assign({}, styles === null || styles === void 0 ? void 0 : styles.root), (_c = badge === null || badge === void 0 ? void 0 : badge.styles) === null || _c === void 0 ? void 0 : _c.root), mergedStyle)\n        }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n            className: statusCls,\n            style: Object.assign(Object.assign(Object.assign({}, styles === null || styles === void 0 ? void 0 : styles.indicator), (_d = badge === null || badge === void 0 ? void 0 : badge.styles) === null || _d === void 0 ? void 0 : _d.indicator), statusStyle)\n        }), text && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", {\n            style: {\n                color: statusTextColor\n            },\n            className: \"\".concat(prefixCls, \"-status-text\")\n        }, text)));\n    }\n    return wrapCSSVar(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"span\", Object.assign({\n        ref: ref\n    }, restProps, {\n        className: badgeClassName,\n        style: Object.assign(Object.assign({}, (_e = badge === null || badge === void 0 ? void 0 : badge.styles) === null || _e === void 0 ? void 0 : _e.root), styles === null || styles === void 0 ? void 0 : styles.root)\n    }), children, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        visible: !isHidden,\n        motionName: \"\".concat(prefixCls, \"-zoom\"),\n        motionAppear: false,\n        motionDeadline: 1000\n    }, (param)=>{\n        let { className: motionClassName } = param;\n        var _a, _b;\n        const scrollNumberPrefixCls = getPrefixCls('scroll-number', customizeScrollNumberPrefixCls);\n        const isDot = isDotRef.current;\n        const scrollNumberCls = classnames__WEBPACK_IMPORTED_MODULE_1___default()(classNames === null || classNames === void 0 ? void 0 : classNames.indicator, (_a = badge === null || badge === void 0 ? void 0 : badge.classNames) === null || _a === void 0 ? void 0 : _a.indicator, {\n            [\"\".concat(prefixCls, \"-dot\")]: isDot,\n            [\"\".concat(prefixCls, \"-count\")]: !isDot,\n            [\"\".concat(prefixCls, \"-count-sm\")]: size === 'small',\n            [\"\".concat(prefixCls, \"-multiple-words\")]: !isDot && displayCount && displayCount.toString().length > 1,\n            [\"\".concat(prefixCls, \"-status-\").concat(status)]: !!status,\n            [\"\".concat(prefixCls, \"-color-\").concat(color)]: isInternalColor\n        });\n        let scrollNumberStyle = Object.assign(Object.assign(Object.assign({}, styles === null || styles === void 0 ? void 0 : styles.indicator), (_b = badge === null || badge === void 0 ? void 0 : badge.styles) === null || _b === void 0 ? void 0 : _b.indicator), mergedStyle);\n        if (color && !isInternalColor) {\n            scrollNumberStyle = scrollNumberStyle || {};\n            scrollNumberStyle.background = color;\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_ScrollNumber__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            prefixCls: scrollNumberPrefixCls,\n            show: !isHidden,\n            motionClassName: motionClassName,\n            className: scrollNumberCls,\n            count: displayCount,\n            title: titleNode,\n            style: scrollNumberStyle,\n            key: \"scrollNumber\"\n        }, displayNode);\n    }), statusTextNode));\n}, \"p/lfsFFBGXjNisSpdajsrBALf64=\", false, function() {\n    return [\n        _style__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n})), \"p/lfsFFBGXjNisSpdajsrBALf64=\", false, function() {\n    return [\n        _style__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c1 = InternalBadge;\nconst Badge = InternalBadge;\nBadge.Ribbon = _Ribbon__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\nif (true) {\n    Badge.displayName = 'Badge';\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);\nvar _c, _c1;\n$RefreshReg$(_c, \"InternalBadge$React.forwardRef\");\n$RefreshReg$(_c1, \"InternalBadge\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/style/index.js":
/*!***************************************************!*\
  !*** ./node_modules/antd/es/badge/style/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prepareComponentToken: () => (/* binding */ prepareComponentToken),\n/* harmony export */   prepareToken: () => (/* binding */ prepareToken)\n/* harmony export */ });\n/* harmony import */ var _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ant-design/cssinjs */ \"(app-pages-browser)/./node_modules/@ant-design/cssinjs/es/index.js\");\n/* harmony import */ var _style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../style */ \"(app-pages-browser)/./node_modules/antd/es/style/index.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/antd/es/theme/util/genPresetColor.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/@ant-design/cssinjs-utils/es/index.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/antd/es/theme/util/genStyleUtils.js\");\n\n\n\nconst antStatusProcessing = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antStatusProcessing', {\n    '0%': {\n        transform: 'scale(0.8)',\n        opacity: 0.5\n    },\n    '100%': {\n        transform: 'scale(2.4)',\n        opacity: 0\n    }\n});\nconst antZoomBadgeIn = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antZoomBadgeIn', {\n    '0%': {\n        transform: 'scale(0) translate(50%, -50%)',\n        opacity: 0\n    },\n    '100%': {\n        transform: 'scale(1) translate(50%, -50%)'\n    }\n});\nconst antZoomBadgeOut = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antZoomBadgeOut', {\n    '0%': {\n        transform: 'scale(1) translate(50%, -50%)'\n    },\n    '100%': {\n        transform: 'scale(0) translate(50%, -50%)',\n        opacity: 0\n    }\n});\nconst antNoWrapperZoomBadgeIn = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antNoWrapperZoomBadgeIn', {\n    '0%': {\n        transform: 'scale(0)',\n        opacity: 0\n    },\n    '100%': {\n        transform: 'scale(1)'\n    }\n});\nconst antNoWrapperZoomBadgeOut = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antNoWrapperZoomBadgeOut', {\n    '0%': {\n        transform: 'scale(1)'\n    },\n    '100%': {\n        transform: 'scale(0)',\n        opacity: 0\n    }\n});\nconst antBadgeLoadingCircle = new _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.Keyframes('antBadgeLoadingCircle', {\n    '0%': {\n        transformOrigin: '50%'\n    },\n    '100%': {\n        transform: 'translate(50%, -50%) rotate(360deg)',\n        transformOrigin: '50%'\n    }\n});\nconst genSharedBadgeStyle = (token)=>{\n    const { componentCls, iconCls, antCls, badgeShadowSize, textFontSize, textFontSizeSM, statusSize, dotSize, textFontWeight, indicatorHeight, indicatorHeightSM, marginXS, calc } = token;\n    const numberPrefixCls = \"\".concat(antCls, \"-scroll-number\");\n    const colorPreset = (0,_theme_internal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(token, (colorKey, param)=>{\n        let { darkColor } = param;\n        return {\n            [\"&\".concat(componentCls, \" \").concat(componentCls, \"-color-\").concat(colorKey)]: {\n                background: darkColor,\n                [\"&:not(\".concat(componentCls, \"-count)\")]: {\n                    color: darkColor\n                },\n                'a:hover &': {\n                    background: darkColor\n                }\n            }\n        };\n    });\n    return {\n        [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, (0,_style__WEBPACK_IMPORTED_MODULE_2__.resetComponent)(token)), {\n            position: 'relative',\n            display: 'inline-block',\n            width: 'fit-content',\n            lineHeight: 1,\n            [\"\".concat(componentCls, \"-count\")]: {\n                display: 'inline-flex',\n                justifyContent: 'center',\n                zIndex: token.indicatorZIndex,\n                minWidth: indicatorHeight,\n                height: indicatorHeight,\n                color: token.badgeTextColor,\n                fontWeight: textFontWeight,\n                fontSize: textFontSize,\n                lineHeight: (0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(indicatorHeight),\n                whiteSpace: 'nowrap',\n                textAlign: 'center',\n                background: token.badgeColor,\n                borderRadius: calc(indicatorHeight).div(2).equal(),\n                boxShadow: \"0 0 0 \".concat((0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(badgeShadowSize), \" \").concat(token.badgeShadowColor),\n                transition: \"background \".concat(token.motionDurationMid),\n                a: {\n                    color: token.badgeTextColor\n                },\n                'a:hover': {\n                    color: token.badgeTextColor\n                },\n                'a:hover &': {\n                    background: token.badgeColorHover\n                }\n            },\n            [\"\".concat(componentCls, \"-count-sm\")]: {\n                minWidth: indicatorHeightSM,\n                height: indicatorHeightSM,\n                fontSize: textFontSizeSM,\n                lineHeight: (0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(indicatorHeightSM),\n                borderRadius: calc(indicatorHeightSM).div(2).equal()\n            },\n            [\"\".concat(componentCls, \"-multiple-words\")]: {\n                padding: \"0 \".concat((0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(token.paddingXS)),\n                bdi: {\n                    unicodeBidi: 'plaintext'\n                }\n            },\n            [\"\".concat(componentCls, \"-dot\")]: {\n                zIndex: token.indicatorZIndex,\n                width: dotSize,\n                minWidth: dotSize,\n                height: dotSize,\n                background: token.badgeColor,\n                borderRadius: '100%',\n                boxShadow: \"0 0 0 \".concat((0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(badgeShadowSize), \" \").concat(token.badgeShadowColor)\n            },\n            [\"\".concat(componentCls, \"-count, \").concat(componentCls, \"-dot, \").concat(numberPrefixCls, \"-custom-component\")]: {\n                position: 'absolute',\n                top: 0,\n                insetInlineEnd: 0,\n                transform: 'translate(50%, -50%)',\n                transformOrigin: '100% 0%',\n                [\"&\".concat(iconCls, \"-spin\")]: {\n                    animationName: antBadgeLoadingCircle,\n                    animationDuration: '1s',\n                    animationIterationCount: 'infinite',\n                    animationTimingFunction: 'linear'\n                }\n            },\n            [\"&\".concat(componentCls, \"-status\")]: {\n                lineHeight: 'inherit',\n                verticalAlign: 'baseline',\n                [\"\".concat(componentCls, \"-status-dot\")]: {\n                    position: 'relative',\n                    top: -1,\n                    // Magic number, but seems better experience\n                    display: 'inline-block',\n                    width: statusSize,\n                    height: statusSize,\n                    verticalAlign: 'middle',\n                    borderRadius: '50%'\n                },\n                [\"\".concat(componentCls, \"-status-success\")]: {\n                    backgroundColor: token.colorSuccess\n                },\n                [\"\".concat(componentCls, \"-status-processing\")]: {\n                    overflow: 'visible',\n                    color: token.colorInfo,\n                    backgroundColor: token.colorInfo,\n                    borderColor: 'currentcolor',\n                    '&::after': {\n                        position: 'absolute',\n                        top: 0,\n                        insetInlineStart: 0,\n                        width: '100%',\n                        height: '100%',\n                        borderWidth: badgeShadowSize,\n                        borderStyle: 'solid',\n                        borderColor: 'inherit',\n                        borderRadius: '50%',\n                        animationName: antStatusProcessing,\n                        animationDuration: token.badgeProcessingDuration,\n                        animationIterationCount: 'infinite',\n                        animationTimingFunction: 'ease-in-out',\n                        content: '\"\"'\n                    }\n                },\n                [\"\".concat(componentCls, \"-status-default\")]: {\n                    backgroundColor: token.colorTextPlaceholder\n                },\n                [\"\".concat(componentCls, \"-status-error\")]: {\n                    backgroundColor: token.colorError\n                },\n                [\"\".concat(componentCls, \"-status-warning\")]: {\n                    backgroundColor: token.colorWarning\n                },\n                [\"\".concat(componentCls, \"-status-text\")]: {\n                    marginInlineStart: marginXS,\n                    color: token.colorText,\n                    fontSize: token.fontSize\n                }\n            }\n        }), colorPreset), {\n            [\"\".concat(componentCls, \"-zoom-appear, \").concat(componentCls, \"-zoom-enter\")]: {\n                animationName: antZoomBadgeIn,\n                animationDuration: token.motionDurationSlow,\n                animationTimingFunction: token.motionEaseOutBack,\n                animationFillMode: 'both'\n            },\n            [\"\".concat(componentCls, \"-zoom-leave\")]: {\n                animationName: antZoomBadgeOut,\n                animationDuration: token.motionDurationSlow,\n                animationTimingFunction: token.motionEaseOutBack,\n                animationFillMode: 'both'\n            },\n            [\"&\".concat(componentCls, \"-not-a-wrapper\")]: {\n                [\"\".concat(componentCls, \"-zoom-appear, \").concat(componentCls, \"-zoom-enter\")]: {\n                    animationName: antNoWrapperZoomBadgeIn,\n                    animationDuration: token.motionDurationSlow,\n                    animationTimingFunction: token.motionEaseOutBack\n                },\n                [\"\".concat(componentCls, \"-zoom-leave\")]: {\n                    animationName: antNoWrapperZoomBadgeOut,\n                    animationDuration: token.motionDurationSlow,\n                    animationTimingFunction: token.motionEaseOutBack\n                },\n                [\"&:not(\".concat(componentCls, \"-status)\")]: {\n                    verticalAlign: 'middle'\n                },\n                [\"\".concat(numberPrefixCls, \"-custom-component, \").concat(componentCls, \"-count\")]: {\n                    transform: 'none'\n                },\n                [\"\".concat(numberPrefixCls, \"-custom-component, \").concat(numberPrefixCls)]: {\n                    position: 'relative',\n                    top: 'auto',\n                    display: 'block',\n                    transformOrigin: '50% 50%'\n                }\n            },\n            [numberPrefixCls]: {\n                overflow: 'hidden',\n                transition: \"all \".concat(token.motionDurationMid, \" \").concat(token.motionEaseOutBack),\n                [\"\".concat(numberPrefixCls, \"-only\")]: {\n                    position: 'relative',\n                    display: 'inline-block',\n                    height: indicatorHeight,\n                    transition: \"all \".concat(token.motionDurationSlow, \" \").concat(token.motionEaseOutBack),\n                    WebkitTransformStyle: 'preserve-3d',\n                    WebkitBackfaceVisibility: 'hidden',\n                    [\"> p\".concat(numberPrefixCls, \"-only-unit\")]: {\n                        height: indicatorHeight,\n                        margin: 0,\n                        WebkitTransformStyle: 'preserve-3d',\n                        WebkitBackfaceVisibility: 'hidden'\n                    }\n                },\n                [\"\".concat(numberPrefixCls, \"-symbol\")]: {\n                    verticalAlign: 'top'\n                }\n            },\n            // ====================== RTL =======================\n            '&-rtl': {\n                direction: 'rtl',\n                [\"\".concat(componentCls, \"-count, \").concat(componentCls, \"-dot, \").concat(numberPrefixCls, \"-custom-component\")]: {\n                    transform: 'translate(-50%, -50%)'\n                }\n            }\n        })\n    };\n};\n// ============================== Export ==============================\nconst prepareToken = (token)=>{\n    const { fontHeight, lineWidth, marginXS, colorBorderBg } = token;\n    const badgeFontHeight = fontHeight;\n    const badgeShadowSize = lineWidth;\n    const badgeTextColor = token.colorTextLightSolid;\n    const badgeColor = token.colorError;\n    const badgeColorHover = token.colorErrorHover;\n    const badgeToken = (0,_theme_internal__WEBPACK_IMPORTED_MODULE_3__.mergeToken)(token, {\n        badgeFontHeight,\n        badgeShadowSize,\n        badgeTextColor,\n        badgeColor,\n        badgeColorHover,\n        badgeShadowColor: colorBorderBg,\n        badgeProcessingDuration: '1.2s',\n        badgeRibbonOffset: marginXS,\n        // Follow token just by Design. Not related with token\n        badgeRibbonCornerTransform: 'scaleY(0.75)',\n        badgeRibbonCornerFilter: \"brightness(75%)\"\n    });\n    return badgeToken;\n};\nconst prepareComponentToken = (token)=>{\n    const { fontSize, lineHeight, fontSizeSM, lineWidth } = token;\n    return {\n        indicatorZIndex: 'auto',\n        indicatorHeight: Math.round(fontSize * lineHeight) - 2 * lineWidth,\n        indicatorHeightSM: fontSize,\n        dotSize: fontSizeSM / 2,\n        textFontSize: fontSizeSM,\n        textFontSizeSM: fontSizeSM,\n        textFontWeight: 'normal',\n        statusSize: fontSizeSM / 2\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_theme_internal__WEBPACK_IMPORTED_MODULE_4__.genStyleHooks)('Badge', (token)=>{\n    const badgeToken = prepareToken(token);\n    return genSharedBadgeStyle(badgeToken);\n}, prepareComponentToken));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/style/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/antd/es/badge/style/ribbon.js":
/*!****************************************************!*\
  !*** ./node_modules/antd/es/badge/style/ribbon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ant-design/cssinjs */ \"(app-pages-browser)/./node_modules/@ant-design/cssinjs/es/index.js\");\n/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! . */ \"(app-pages-browser)/./node_modules/antd/es/badge/style/index.js\");\n/* harmony import */ var _style__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../style */ \"(app-pages-browser)/./node_modules/antd/es/style/index.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/antd/es/theme/util/genPresetColor.js\");\n/* harmony import */ var _theme_internal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../theme/internal */ \"(app-pages-browser)/./node_modules/antd/es/theme/util/genStyleUtils.js\");\n\n\n\n\n// ============================== Ribbon ==============================\nconst genRibbonStyle = (token)=>{\n    const { antCls, badgeFontHeight, marginXS, badgeRibbonOffset, calc } = token;\n    const ribbonPrefixCls = \"\".concat(antCls, \"-ribbon\");\n    const ribbonWrapperPrefixCls = \"\".concat(antCls, \"-ribbon-wrapper\");\n    const statusRibbonPreset = (0,_theme_internal__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(token, (colorKey, param)=>{\n        let { darkColor } = param;\n        return {\n            [\"&\".concat(ribbonPrefixCls, \"-color-\").concat(colorKey)]: {\n                background: darkColor,\n                color: darkColor\n            }\n        };\n    });\n    return {\n        [ribbonWrapperPrefixCls]: {\n            position: 'relative'\n        },\n        [ribbonPrefixCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, (0,_style__WEBPACK_IMPORTED_MODULE_2__.resetComponent)(token)), {\n            position: 'absolute',\n            top: marginXS,\n            padding: \"0 \".concat((0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(token.paddingXS)),\n            color: token.colorPrimary,\n            lineHeight: (0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(badgeFontHeight),\n            whiteSpace: 'nowrap',\n            backgroundColor: token.colorPrimary,\n            borderRadius: token.borderRadiusSM,\n            [\"\".concat(ribbonPrefixCls, \"-text\")]: {\n                color: token.badgeTextColor\n            },\n            [\"\".concat(ribbonPrefixCls, \"-corner\")]: {\n                position: 'absolute',\n                top: '100%',\n                width: badgeRibbonOffset,\n                height: badgeRibbonOffset,\n                color: 'currentcolor',\n                border: \"\".concat((0,_ant_design_cssinjs__WEBPACK_IMPORTED_MODULE_0__.unit)(calc(badgeRibbonOffset).div(2).equal()), \" solid\"),\n                transform: token.badgeRibbonCornerTransform,\n                transformOrigin: 'top',\n                filter: token.badgeRibbonCornerFilter\n            }\n        }), statusRibbonPreset), {\n            [\"&\".concat(ribbonPrefixCls, \"-placement-end\")]: {\n                insetInlineEnd: calc(badgeRibbonOffset).mul(-1).equal(),\n                borderEndEndRadius: 0,\n                [\"\".concat(ribbonPrefixCls, \"-corner\")]: {\n                    insetInlineEnd: 0,\n                    borderInlineEndColor: 'transparent',\n                    borderBlockEndColor: 'transparent'\n                }\n            },\n            [\"&\".concat(ribbonPrefixCls, \"-placement-start\")]: {\n                insetInlineStart: calc(badgeRibbonOffset).mul(-1).equal(),\n                borderEndStartRadius: 0,\n                [\"\".concat(ribbonPrefixCls, \"-corner\")]: {\n                    insetInlineStart: 0,\n                    borderBlockEndColor: 'transparent',\n                    borderInlineStartColor: 'transparent'\n                }\n            },\n            // ====================== RTL =======================\n            '&-rtl': {\n                direction: 'rtl'\n            }\n        })\n    };\n};\n// ============================== Export ==============================\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,_theme_internal__WEBPACK_IMPORTED_MODULE_3__.genStyleHooks)([\n    'Badge',\n    'Ribbon'\n], (token)=>{\n    const badgeToken = (0,___WEBPACK_IMPORTED_MODULE_4__.prepareToken)(token);\n    return genRibbonStyle(badgeToken);\n}, ___WEBPACK_IMPORTED_MODULE_4__.prepareComponentToken));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/antd/es/badge/style/ribbon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/badge/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Badge,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SecurityScanOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/StarFilled.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SecurityScanOutlined,StarFilled,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 52\n            }, this),\n            color: '#1890ff'\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 44,\n                columnNumber: 56\n            }, this),\n            color: '#52c41a'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 53\n            }, this),\n            color: '#faad14'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 43\n            }, this),\n            color: '#722ed1'\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#1890ff'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',\n            highlight: 'HTTP/HTTPS & SOCKS5'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#52c41a'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 13\n            }, this),\n            title: '全球节点覆盖',\n            description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',\n            highlight: '50+ 国家地区'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#faad14'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            title: '极速稳定',\n            description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',\n            highlight: '99.9% 稳定性'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#722ed1'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',\n            highlight: '军用级加密'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#eb2f96'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 13\n            }, this),\n            title: '实时监控',\n            description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',\n            highlight: '实时监控面板'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                style: {\n                    fontSize: '2.5rem',\n                    color: '#13c2c2'\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 13\n            }, this),\n            title: 'API集成',\n            description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',\n            highlight: 'RESTful API'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 16px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1200px',\n                            margin: '0 auto',\n                            height: '64px'\n                        },\n                        className: \"jsx-24d3f5d8be7854ab\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '12px',\n                                        minWidth: 0\n                                    },\n                                    className: \"jsx-24d3f5d8be7854ab\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            size: 40,\n                                            style: {\n                                                backgroundColor: '#2563eb',\n                                                flexShrink: 0\n                                            },\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: 0\n                                            },\n                                            className: \"jsx-24d3f5d8be7854ab\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 4,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#2563eb',\n                                                        fontSize: '18px',\n                                                        lineHeight: '1.2',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    style: {\n                                                        fontSize: '11px',\n                                                        lineHeight: '1',\n                                                        display: 'block',\n                                                        whiteSpace: 'nowrap'\n                                                    },\n                                                    children: \"企业级代理服务平台\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500'\n                                            },\n                                            children: \"帮助文档\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"vertical\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '8px'\n                                },\n                                className: \"jsx-24d3f5d8be7854ab\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-24d3f5d8be7854ab\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"small\",\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"small\",\n                                                    children: \"注册\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"24d3f5d8be7854ab\",\n                        children: \"@media(min-width:768px){.desktop-nav.jsx-24d3f5d8be7854ab{display:block!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:none!important}}@media(max-width:767px){.desktop-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-nav.jsx-24d3f5d8be7854ab{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}@media(max-width:480px){.mobile-nav.jsx-24d3f5d8be7854ab{display:none!important}.mobile-menu-btn.jsx-24d3f5d8be7854ab{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            size: 32,\n                            style: {\n                                backgroundColor: '#2563eb'\n                            },\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 21\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: '#2563eb',\n                                fontWeight: 'bold'\n                            },\n                            children: \"ProxyHub\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 280,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDE80 产品特性\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDCB0 价格方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px'\n                            },\n                            children: \"\\uD83D\\uDCDA 帮助文档\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '48px'\n                                },\n                                children: \"\\uD83D\\uDD10 登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '48px'\n                                },\n                                children: \"\\uD83D\\uDCDD 免费注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            padding: '100px 24px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.3\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Ribbon, {\n                                        text: \"\\uD83D\\uDE80 企业首选\",\n                                        color: \"#52c41a\",\n                                        style: {\n                                            marginBottom: '24px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-block',\n                                                marginBottom: '32px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        style: {\n                                                            color: '#faad14'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.9)',\n                                                            fontSize: '14px'\n                                                        },\n                                                        children: \"已服务 10万+ 企业用户\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        style: {\n                                                            color: '#faad14'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(2rem, 5vw, 3.5rem)',\n                                            marginBottom: '24px',\n                                            color: '#fff',\n                                            textShadow: '0 2px 4px rgba(0,0,0,0.3)',\n                                            lineHeight: '1.2'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDF10 全球代理服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 24\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #ffd700, #ffed4e)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text'\n                                                },\n                                                children: \"企业级解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',\n                                            color: 'rgba(255,255,255,0.9)',\n                                            marginBottom: '48px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 48px auto',\n                                            lineHeight: '1.6',\n                                            textShadow: '0 1px 2px rgba(0,0,0,0.2)'\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDD25 提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 52\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"⚡ 99.9%稳定性保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83C\\uDF0D 50+国家覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                strong: true,\n                                                style: {\n                                                    color: '#ffd700'\n                                                },\n                                                children: \"\\uD83D\\uDEE1️ 企业级安全\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        style: {\n                                            marginBottom: '64px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        fontWeight: 'bold',\n                                                        background: 'linear-gradient(45deg, #1890ff, #36cfc9)',\n                                                        border: 'none',\n                                                        boxShadow: '0 4px 15px rgba(24, 144, 255, 0.4)'\n                                                    },\n                                                    children: \"立即免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        background: 'rgba(255,255,255,0.1)',\n                                                        border: '2px solid rgba(255,255,255,0.3)',\n                                                        color: '#fff',\n                                                        backdropFilter: 'blur(10px)'\n                                                    },\n                                                    children: \"观看演示\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        gutter: [\n                                            32,\n                                            16\n                                        ],\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: \"small\",\n                                                    style: {\n                                                        textAlign: 'center'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        title: stat.title,\n                                                        value: stat.value,\n                                                        suffix: stat.suffix,\n                                                        prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: '1.2rem',\n                                                                color: '#2563eb'\n                                                            },\n                                                            children: stat.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 31\n                                                        }, void 0),\n                                                        valueStyle: {\n                                                            color: '#2563eb',\n                                                            fontWeight: 'bold'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 544,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Badge_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SecurityScanOutlined_StarFilled_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 615,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});