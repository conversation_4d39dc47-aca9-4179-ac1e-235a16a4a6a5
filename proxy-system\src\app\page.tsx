'use client';

import { useState } from 'react';
import Link from "next/link";
import {
  <PERSON><PERSON>,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Statistic,
  Layout,
  Avatar,
  Drawer,
  Badge,
  Divider,
  Flex,
  Steps,
  Timeline,
  Tabs,
  List,
  Tag
} from 'antd';
import {
  SafetyOutlined,
  GlobalOutlined,
  <PERSON>boltOutlined,
  LockOutlined,
  BarChartOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  MenuOutlined,
  StarFilled,
  RocketOutlined,
  SecurityScanOutlined,
  CloudOutlined,
  DashboardOutlined,
  TeamOutlined,
  TrophyOutlined,
  ArrowRightOutlined,
  CheckOutlined,
  SettingOutlined,
  MonitorOutlined,
  DatabaseOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('1');

  // 核心统计数据
  const stats = [
    {
      title: '全球节点',
      value: 50,
      suffix: '+',
      icon: <GlobalOutlined />,
      color: '#3b82f6',
      description: '覆盖全球主要国家和地区'
    },
    {
      title: '企业用户',
      value: 10000,
      suffix: '+',
      icon: <TeamOutlined />,
      color: '#10b981',
      description: '服务全球企业客户'
    },
    {
      title: '稳定性',
      value: 99.9,
      suffix: '%',
      icon: <SafetyOutlined />,
      color: '#f59e0b',
      description: 'SLA 服务等级保证'
    },
    {
      title: '技术支持',
      value: '24/7',
      icon: <SafetyOutlined />,
      color: '#8b5cf6',
      description: '全天候专业技术支持'
    }
  ];

  // 核心功能特性
  const features = [
    {
      icon: <CloudOutlined />,
      title: '多协议支持',
      description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',
      tags: ['HTTP/HTTPS', 'SOCKS5', '高兼容性'],
      color: '#3b82f6'
    },
    {
      icon: <GlobalOutlined />,
      title: '全球节点网络',
      description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',
      tags: ['50+ 国家', '低延迟', '高速度'],
      color: '#10b981'
    },
    {
      icon: <LockOutlined />,
      title: '企业级安全',
      description: '采用军用级加密技术，多重安全防护，保障数据传输安全',
      tags: ['军用级加密', '多重防护', '隐私保护'],
      color: '#f59e0b'
    },
    {
      icon: <DashboardOutlined />,
      title: '智能监控',
      description: '实时监控系统状态，智能故障检测，确保服务稳定运行',
      tags: ['实时监控', '智能检测', '自动恢复'],
      color: '#8b5cf6'
    },
    {
      icon: <ApiOutlined />,
      title: 'API 集成',
      description: '完整的 RESTful API，支持自动化管理和第三方系统集成',
      tags: ['RESTful API', '自动化', '易集成'],
      color: '#ef4444'
    },
    {
      icon: <SettingOutlined />,
      title: '灵活配置',
      description: '支持自定义配置，满足不同业务场景的个性化需求',
      tags: ['自定义配置', '灵活部署', '场景适配'],
      color: '#06b6d4'
    }
  ];

  // 使用步骤
  const steps = [
    {
      title: '注册账户',
      description: '快速注册，获取专属 API 密钥',
      icon: <CheckOutlined />
    },
    {
      title: '选择套餐',
      description: '根据业务需求选择合适的服务套餐',
      icon: <SettingOutlined />
    },
    {
      title: '配置接入',
      description: '通过 API 或控制面板配置代理服务',
      icon: <ApiOutlined />
    },
    {
      title: '开始使用',
      description: '享受稳定高效的全球代理服务',
      icon: <RocketOutlined />
    }
  ];

  // 客户案例
  const testimonials = [
    {
      company: '某大型电商平台',
      industry: '电子商务',
      content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',
      avatar: 'E',
      color: '#3b82f6'
    },
    {
      company: '某金融科技公司',
      industry: '金融科技',
      content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',
      avatar: 'F',
      color: '#10b981'
    },
    {
      company: '某数据分析公司',
      industry: '数据分析',
      content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',
      avatar: 'D',
      color: '#f59e0b'
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        padding: '0 24px',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        borderBottom: '1px solid rgba(0,0,0,0.06)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          maxWidth: '1400px',
          margin: '0 auto',
          height: '72px'
        }}>
          {/* Logo */}
          <Link href="/" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{
                width: '48px',
                height: '48px',
                borderRadius: '12px',
                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'
              }}>
                <SafetyOutlined style={{ fontSize: '24px', color: '#fff' }} />
              </div>
              <div>
                <Title level={3} style={{
                  margin: 0,
                  color: '#1e293b',
                  fontSize: '24px',
                  fontWeight: '700',
                  lineHeight: '1'
                }}>
                  ProxyHub
                </Title>
                <Text style={{
                  fontSize: '13px',
                  color: '#64748b',
                  fontWeight: '500'
                }}>
                  Enterprise Proxy Solutions
                </Text>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div style={{ display: 'none' }} className="desktop-nav">
            <Space size="large">
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#475569',
                  fontSize: '15px',
                  height: '40px',
                  borderRadius: '8px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = '#3b82f6';
                  e.target.style.background = '#f1f5f9';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = '#475569';
                  e.target.style.background = 'transparent';
                }}
              >
                产品特性
              </Button>
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#475569',
                  fontSize: '15px',
                  height: '40px',
                  borderRadius: '8px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = '#3b82f6';
                  e.target.style.background = '#f1f5f9';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = '#475569';
                  e.target.style.background = 'transparent';
                }}
              >
                解决方案
              </Button>
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#475569',
                  fontSize: '15px',
                  height: '40px',
                  borderRadius: '8px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = '#3b82f6';
                  e.target.style.background = '#f1f5f9';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = '#475569';
                  e.target.style.background = 'transparent';
                }}
              >
                价格方案
              </Button>
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#475569',
                  fontSize: '15px',
                  height: '40px',
                  borderRadius: '8px'
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = '#3b82f6';
                  e.target.style.background = '#f1f5f9';
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = '#475569';
                  e.target.style.background = 'transparent';
                }}
              >
                开发者
              </Button>
              <Divider type="vertical" style={{ borderColor: '#e2e8f0', height: '24px' }} />
              <Link href="/login">
                <Button
                  type="text"
                  style={{
                    fontWeight: '500',
                    color: '#475569',
                    fontSize: '15px',
                    height: '40px',
                    borderRadius: '8px'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = '#3b82f6';
                    e.target.style.background = '#f1f5f9';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = '#475569';
                    e.target.style.background = 'transparent';
                  }}
                >
                  登录
                </Button>
              </Link>
              <Link href="/register">
                <Button
                  type="primary"
                  style={{
                    fontWeight: '600',
                    fontSize: '15px',
                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                    borderColor: 'transparent',
                    borderRadius: '10px',
                    height: '44px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                    border: 'none'
                  }}
                >
                  免费试用
                </Button>
              </Link>
            </Space>
          </div>

          {/* Mobile Navigation */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{ display: 'flex', gap: '8px' }} className="mobile-nav">
              <Link href="/login">
                <Button type="text" size="middle" style={{ fontWeight: '500' }}>
                  登录
                </Button>
              </Link>
              <Link href="/register">
                <Button
                  type="primary"
                  size="middle"
                  style={{
                    background: '#3b82f6',
                    borderColor: '#3b82f6',
                    borderRadius: '8px',
                    fontWeight: '600'
                  }}
                >
                  试用
                </Button>
              </Link>
            </div>

            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuOpen(true)}
              style={{
                display: 'none',
                fontSize: '18px',
                width: '44px',
                height: '44px',
                borderRadius: '8px'
              }}
              className="mobile-menu-btn"
            />
          </div>
        </div>

        {/* Responsive CSS */}
        <style jsx>{`
          @media (min-width: 1024px) {
            .desktop-nav { display: block !important; }
            .mobile-nav { display: none !important; }
            .mobile-menu-btn { display: none !important; }
          }
          @media (max-width: 1023px) and (min-width: 640px) {
            .desktop-nav { display: none !important; }
            .mobile-nav { display: flex !important; }
            .mobile-menu-btn { display: none !important; }
          }
          @media (max-width: 639px) {
            .desktop-nav { display: none !important; }
            .mobile-nav { display: none !important; }
            .mobile-menu-btn { display: inline-flex !important; }
          }
        `}</style>
      </Header>

      {/* Mobile Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '10px',
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <SafetyOutlined style={{ fontSize: '20px', color: '#fff' }} />
            </div>
            <div>
              <div style={{ color: '#1e293b', fontWeight: '700', fontSize: '18px' }}>
                ProxyHub
              </div>
              <div style={{ color: '#64748b', fontSize: '12px' }}>
                Enterprise Solutions
              </div>
            </div>
          </div>
        }
        placement="right"
        onClose={() => setMobileMenuOpen(false)}
        open={mobileMenuOpen}
        width={320}
        styles={{
          body: { padding: '24px' },
          header: { borderBottom: '1px solid #f1f5f9', paddingBottom: '16px' }
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
          <div style={{ marginBottom: '16px' }}>
            <Text style={{ fontSize: '14px', color: '#64748b', fontWeight: '500' }}>
              导航菜单
            </Text>
          </div>

          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '52px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#475569',
              justifyContent: 'flex-start',
              borderRadius: '12px',
              border: '1px solid transparent'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = '#f8fafc';
              e.target.style.borderColor = '#e2e8f0';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
              e.target.style.borderColor = 'transparent';
            }}
          >
            <GlobalOutlined style={{ marginRight: '12px', color: '#3b82f6' }} />
            产品特性
          </Button>

          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '52px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#475569',
              justifyContent: 'flex-start',
              borderRadius: '12px',
              border: '1px solid transparent'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = '#f8fafc';
              e.target.style.borderColor = '#e2e8f0';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
              e.target.style.borderColor = 'transparent';
            }}
          >
            <SettingOutlined style={{ marginRight: '12px', color: '#10b981' }} />
            解决方案
          </Button>

          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '52px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#475569',
              justifyContent: 'flex-start',
              borderRadius: '12px',
              border: '1px solid transparent'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = '#f8fafc';
              e.target.style.borderColor = '#e2e8f0';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
              e.target.style.borderColor = 'transparent';
            }}
          >
            <TrophyOutlined style={{ marginRight: '12px', color: '#f59e0b' }} />
            价格方案
          </Button>

          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '52px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#475569',
              justifyContent: 'flex-start',
              borderRadius: '12px',
              border: '1px solid transparent'
            }}
            onMouseEnter={(e) => {
              e.target.style.background = '#f8fafc';
              e.target.style.borderColor = '#e2e8f0';
            }}
            onMouseLeave={(e) => {
              e.target.style.background = 'transparent';
              e.target.style.borderColor = 'transparent';
            }}
          >
            <ApiOutlined style={{ marginRight: '12px', color: '#8b5cf6' }} />
            开发者
          </Button>

          <Divider style={{ margin: '24px 0' }} />

          <div style={{ marginBottom: '16px' }}>
            <Text style={{ fontSize: '14px', color: '#64748b', fontWeight: '500' }}>
              账户操作
            </Text>
          </div>

          <Link href="/login">
            <Button
              type="text"
              block
              style={{
                height: '52px',
                fontSize: '16px',
                fontWeight: '500',
                color: '#475569',
                borderRadius: '12px',
                border: '1px solid #e2e8f0'
              }}
            >
              登录账户
            </Button>
          </Link>

          <Link href="/register">
            <Button
              type="primary"
              block
              style={{
                height: '52px',
                fontSize: '16px',
                fontWeight: '600',
                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                borderColor: 'transparent',
                borderRadius: '12px',
                marginTop: '8px',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'
              }}
            >
              免费试用
            </Button>
          </Link>
        </div>
      </Drawer>

      <Content>
        {/* Hero Section */}
        <div style={{
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
          padding: '140px 24px 120px',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background Elements */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.6
          }} />

          {/* Gradient Orbs */}
          <div style={{
            position: 'absolute',
            top: '20%',
            left: '10%',
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',
            filter: 'blur(40px)'
          }} />
          <div style={{
            position: 'absolute',
            top: '60%',
            right: '10%',
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)',
            filter: 'blur(30px)'
          }} />

          <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 1 }}>
            {/* Trust Indicators */}
            <div style={{ marginBottom: '56px' }}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '16px',
                background: 'rgba(255,255,255,0.08)',
                backdropFilter: 'blur(20px)',
                border: '1px solid rgba(255,255,255,0.12)',
                borderRadius: '60px',
                padding: '12px 32px',
                marginBottom: '32px'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    background: '#10b981',
                    boxShadow: '0 0 12px #10b981',
                    animation: 'pulse 2s infinite'
                  }} />
                  <Text style={{
                    color: 'rgba(255,255,255,0.9)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}>
                    服务 10,000+ 企业客户
                  </Text>
                </div>
                <Divider type="vertical" style={{ borderColor: 'rgba(255,255,255,0.2)', height: '16px' }} />
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  <TrophyOutlined style={{ color: '#f59e0b', fontSize: '16px' }} />
                  <Text style={{
                    color: 'rgba(255,255,255,0.9)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}>
                    行业领先
                  </Text>
                </div>
              </div>
            </div>

            {/* Main Headline */}
            <Title level={1} style={{
              fontSize: 'clamp(3rem, 8vw, 5rem)',
              marginBottom: '32px',
              color: '#ffffff',
              fontWeight: '800',
              lineHeight: '1.1',
              letterSpacing: '-0.03em',
              textShadow: '0 4px 20px rgba(0,0,0,0.3)'
            }}>
              企业级全球代理
              <br />
              <Text style={{
                background: 'linear-gradient(135deg, #3b82f6, #06b6d4)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                fontWeight: '800'
              }}>
                解决方案
              </Text>
            </Title>

            {/* Subtitle */}
            <Paragraph style={{
              fontSize: 'clamp(1.2rem, 3vw, 1.5rem)',
              color: 'rgba(255,255,255,0.8)',
              marginBottom: '56px',
              maxWidth: '800px',
              margin: '0 auto 56px auto',
              lineHeight: '1.6',
              fontWeight: '400'
            }}>
              为企业提供稳定、安全、高速的全球代理网络服务
              <br />
              <Text style={{ color: '#60a5fa', fontWeight: '600' }}>99.9% SLA 保证</Text> •
              <Text style={{ color: '#34d399', fontWeight: '600' }}>50+ 国家节点</Text> •
              <Text style={{ color: '#fbbf24', fontWeight: '600' }}>24/7 技术支持</Text>
            </Paragraph>

            {/* CTA Section */}
            <div style={{ marginBottom: '80px' }}>
              <Space size="large" wrap>
                <Link href="/register">
                  <Button
                    type="primary"
                    size="large"
                    icon={<RocketOutlined />}
                    style={{
                      height: '64px',
                      fontSize: '18px',
                      fontWeight: '700',
                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                      borderColor: 'transparent',
                      borderRadius: '16px',
                      paddingLeft: '40px',
                      paddingRight: '40px',
                      boxShadow: '0 12px 32px rgba(59, 130, 246, 0.4)',
                      border: 'none',
                      minWidth: '200px'
                    }}
                  >
                    立即免费试用
                  </Button>
                </Link>
                <Button
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{
                    height: '64px',
                    fontSize: '18px',
                    fontWeight: '600',
                    background: 'rgba(255,255,255,0.08)',
                    border: '2px solid rgba(255,255,255,0.16)',
                    color: '#ffffff',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '16px',
                    paddingLeft: '40px',
                    paddingRight: '40px',
                    minWidth: '180px'
                  }}
                >
                  观看演示
                </Button>
              </Space>

              {/* Quick Stats */}
              <div style={{ marginTop: '48px' }}>
                <Text style={{
                  color: 'rgba(255,255,255,0.6)',
                  fontSize: '14px',
                  display: 'block',
                  marginBottom: '16px'
                }}>
                  已有数千家企业选择我们
                </Text>
                <Space size="large" wrap>
                  <div style={{ textAlign: 'center' }}>
                    <Text style={{ color: '#ffffff', fontSize: '24px', fontWeight: '700', display: 'block' }}>
                      10K+
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                      企业用户
                    </Text>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <Text style={{ color: '#ffffff', fontSize: '24px', fontWeight: '700', display: 'block' }}>
                      50+
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                      国家节点
                    </Text>
                  </div>
                  <div style={{ textAlign: 'center' }}>
                    <Text style={{ color: '#ffffff', fontSize: '24px', fontWeight: '700', display: 'block' }}>
                      99.9%
                    </Text>
                    <Text style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                      稳定性
                    </Text>
                  </div>
                </Space>
              </div>
            </div>

            {/* Enhanced Stats Section */}
            <div style={{
              background: 'rgba(255,255,255,0.05)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255,255,255,0.1)',
              borderRadius: '32px',
              padding: '48px 32px',
              marginBottom: '0'
            }}>
              <Row gutter={[32, 32]}>
                {stats.map((stat, index) => (
                  <Col xs={12} lg={6} key={index}>
                    <div
                      style={{
                        textAlign: 'center',
                        background: 'rgba(255,255,255,0.08)',
                        backdropFilter: 'blur(20px)',
                        border: '1px solid rgba(255,255,255,0.12)',
                        borderRadius: '24px',
                        padding: '40px 24px',
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        cursor: 'default',
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.12)';
                        e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0) scale(1)';
                        e.currentTarget.style.background = 'rgba(255,255,255,0.08)';
                        e.currentTarget.style.borderColor = 'rgba(255,255,255,0.12)';
                      }}
                    >
                      {/* Background Glow */}
                      <div style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        width: '120px',
                        height: '120px',
                        borderRadius: '50%',
                        background: `radial-gradient(circle, ${stat.color}15 0%, transparent 70%)`,
                        filter: 'blur(20px)',
                        zIndex: 0
                      }} />

                      <div style={{ position: 'relative', zIndex: 1 }}>
                        {/* Icon Container */}
                        <div style={{
                          marginBottom: '24px',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}>
                          <div style={{
                            width: '72px',
                            height: '72px',
                            borderRadius: '20px',
                            background: `linear-gradient(135deg, ${stat.color}20, ${stat.color}30)`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            border: `2px solid ${stat.color}40`,
                            boxShadow: `0 8px 32px ${stat.color}20`
                          }}>
                            <span style={{
                              fontSize: '32px',
                              color: stat.color,
                              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                            }}>
                              {stat.icon}
                            </span>
                          </div>
                        </div>

                        {/* Value */}
                        <div style={{ marginBottom: '12px' }}>
                          <Text style={{
                            color: '#ffffff',
                            fontSize: '36px',
                            fontWeight: '800',
                            lineHeight: '1',
                            display: 'block'
                          }}>
                            {stat.value}{stat.suffix}
                          </Text>
                        </div>

                        {/* Title */}
                        <div style={{ marginBottom: '8px' }}>
                          <Text style={{
                            color: 'rgba(255,255,255,0.9)',
                            fontSize: '16px',
                            fontWeight: '600',
                            display: 'block'
                          }}>
                            {stat.title}
                          </Text>
                        </div>

                        {/* Description */}
                        <Text style={{
                          color: 'rgba(255,255,255,0.6)',
                          fontSize: '13px',
                          lineHeight: '1.4'
                        }}>
                          {stat.description}
                        </Text>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>

            {/* CTA Buttons */}
            <Space size="large" wrap>
              <Link href="/register">
                <Button 
                  type="primary" 
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
                >
                  立即开始免费试用
                </Button>
              </Link>
              <Button 
                size="large"
                icon={<EyeOutlined />}
                style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
              >
                观看演示
              </Button>
            </Space>

            {/* Trust indicators */}
            <div style={{ marginTop: '32px' }}>
              <Space size="large" wrap>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  无需信用卡
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  7天免费试用
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  随时取消
                </Text>
              </Space>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div style={{ padding: '80px 24px', background: '#fff' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '64px' }}>
              <Title level={2} style={{ fontSize: '2.5rem', marginBottom: '16px' }}>
                为什么选择我们？
              </Title>
              <Paragraph style={{ 
                fontSize: '1.25rem', 
                color: '#6b7280',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行
              </Paragraph>
            </div>

            <Row gutter={[32, 32]}>
              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <GlobalOutlined style={{ fontSize: '2rem', color: '#2563eb' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    多协议支持
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <SafetyOutlined style={{ fontSize: '2rem', color: '#059669' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    全球节点覆盖
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ThunderboltOutlined style={{ fontSize: '2rem', color: '#d97706' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    极速稳定
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <LockOutlined style={{ fontSize: '2rem', color: '#7c3aed' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    企业级安全
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    采用军用级加密技术，保护您的数据传输安全和隐私不被泄露
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <BarChartOutlined style={{ fontSize: '2rem', color: '#dc2626' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    实时监控
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    提供详细的使用统计和实时监控面板，帮助您优化代理使用效率
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ApiOutlined style={{ fontSize: '2rem', color: '#0891b2' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    API集成
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    完整的RESTful API接口，支持自动化管理和第三方系统无缝集成
                  </Paragraph>
                </Card>
              </Col>
            </Row>
          </div>
        </div>

        {/* CTA Section */}
        <div style={{ 
          background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
          padding: '80px 24px',
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <Title level={2} style={{ color: 'white', fontSize: '2.5rem', marginBottom: '16px' }}>
              准备开始了吗？
            </Title>
            <Paragraph style={{ 
              fontSize: '1.25rem',
              marginBottom: '32px',
              opacity: 0.9,
              color: 'white'
            }}>
              加入10万+用户的行列，体验专业的代理服务平台
            </Paragraph>
            <Space size="large">
              <Link href="/register">
                <Button 
                  size="large"
                  style={{ 
                    height: '48px', 
                    padding: '0 32px', 
                    fontSize: '16px',
                    background: 'white',
                    color: '#2563eb',
                    border: 'none'
                  }}
                >
                  立即免费注册
                </Button>
              </Link>
              <Button 
                size="large"
                ghost
                style={{ 
                  height: '48px', 
                  padding: '0 32px', 
                  fontSize: '16px',
                  borderColor: 'white',
                  color: 'white'
                }}
              >
                联系销售团队
              </Button>
            </Space>
          </div>
        </div>
      </Content>

      {/* Footer */}
      <Footer style={{ 
        background: '#001529', 
        color: 'white',
        padding: '48px 24px 16px 24px'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', textAlign: 'center' }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
            marginBottom: '32px'
          }}>
            <Avatar 
              size={48} 
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <div>
              <Title level={3} style={{ color: '#2563eb', margin: 0 }}>
                ProxyHub
              </Title>
              <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
                企业级代理服务平台
              </Text>
            </div>
          </div>
          
          <Paragraph style={{ color: '#8c8c8c', marginBottom: '32px' }}>
            为全球用户提供稳定、高速、安全的代理服务解决方案
          </Paragraph>
          
          <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
            © 2024 ProxyHub. 保留所有权利.
          </Text>
        </div>
      </Footer>
    </Layout>
  );
}
