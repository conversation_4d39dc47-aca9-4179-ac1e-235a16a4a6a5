'use client';

import { useState } from 'react';
import Link from "next/link";
import {
  <PERSON><PERSON>,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Statistic,
  Layout,
  Avatar,
  Drawer,
  Badge,
  Divider,
  Flex
} from 'antd';
import {
  SafetyOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  LockOutlined,
  <PERSON><PERSON>hartOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  MenuOutlined,
  StarFilled,
  RocketOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const stats = [
    { title: '覆盖国家', value: 50, suffix: '+', icon: <GlobalOutlined />, color: '#1890ff' },
    { title: '活跃用户', value: 100000, suffix: '+', icon: <SafetyOutlined />, color: '#52c41a' },
    { title: '稳定性', value: 99.9, suffix: '%', icon: <ThunderboltOutlined />, color: '#faad14' },
    { title: '技术支持', value: '24/7', icon: <ApiOutlined />, color: '#722ed1' }
  ];

  const features = [
    {
      icon: <GlobalOutlined style={{ fontSize: '2.5rem', color: '#1890ff' }} />,
      title: '多协议支持',
      description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',
      highlight: 'HTTP/HTTPS & SOCKS5'
    },
    {
      icon: <SecurityScanOutlined style={{ fontSize: '2.5rem', color: '#52c41a' }} />,
      title: '全球节点覆盖',
      description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',
      highlight: '50+ 国家地区'
    },
    {
      icon: <ThunderboltOutlined style={{ fontSize: '2.5rem', color: '#faad14' }} />,
      title: '极速稳定',
      description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',
      highlight: '99.9% 稳定性'
    },
    {
      icon: <LockOutlined style={{ fontSize: '2.5rem', color: '#722ed1' }} />,
      title: '企业级安全',
      description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',
      highlight: '军用级加密'
    },
    {
      icon: <BarChartOutlined style={{ fontSize: '2.5rem', color: '#eb2f96' }} />,
      title: '实时监控',
      description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',
      highlight: '实时监控面板'
    },
    {
      icon: <ApiOutlined style={{ fontSize: '2.5rem', color: '#13c2c2' }} />,
      title: 'API集成',
      description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',
      highlight: 'RESTful API'
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        padding: '0 16px',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          maxWidth: '1200px',
          margin: '0 auto',
          height: '64px'
        }}>
          {/* Logo */}
          <Link href="/" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', minWidth: 0 }}>
              <Avatar
                size={40}
                style={{ backgroundColor: '#2563eb', flexShrink: 0 }}
                icon={<SafetyOutlined />}
              />
              <div style={{ minWidth: 0 }}>
                <Title level={4} style={{
                  margin: 0,
                  color: '#2563eb',
                  fontSize: '18px',
                  lineHeight: '1.2',
                  whiteSpace: 'nowrap'
                }}>
                  ProxyHub
                </Title>
                <Text type="secondary" style={{
                  fontSize: '11px',
                  lineHeight: '1',
                  display: 'block',
                  whiteSpace: 'nowrap'
                }}>
                  企业级代理服务平台
                </Text>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div style={{ display: 'none' }} className="desktop-nav">
            <Space size="large">
              <Button type="text" style={{ fontWeight: '500' }}>产品特性</Button>
              <Button type="text" style={{ fontWeight: '500' }}>价格方案</Button>
              <Button type="text" style={{ fontWeight: '500' }}>帮助文档</Button>
              <Divider type="vertical" />
              <Link href="/login">
                <Button type="text" style={{ fontWeight: '500' }}>登录</Button>
              </Link>
              <Link href="/register">
                <Button type="primary" style={{ fontWeight: '500' }}>免费注册</Button>
              </Link>
            </Space>
          </div>

          {/* Mobile Navigation */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {/* Mobile Buttons */}
            <div style={{ display: 'flex', gap: '8px' }} className="mobile-nav">
              <Link href="/login">
                <Button type="text" size="small">登录</Button>
              </Link>
              <Link href="/register">
                <Button type="primary" size="small">注册</Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuOpen(true)}
              style={{ display: 'none' }}
              className="mobile-menu-btn"
            />
          </div>
        </div>

        {/* Add responsive CSS */}
        <style jsx>{`
          @media (min-width: 768px) {
            .desktop-nav {
              display: block !important;
            }
            .mobile-nav {
              display: none !important;
            }
            .mobile-menu-btn {
              display: none !important;
            }
          }
          @media (max-width: 767px) {
            .desktop-nav {
              display: none !important;
            }
            .mobile-nav {
              display: flex !important;
            }
            .mobile-menu-btn {
              display: inline-flex !important;
            }
          }
          @media (max-width: 480px) {
            .mobile-nav {
              display: none !important;
            }
            .mobile-menu-btn {
              display: inline-flex !important;
            }
          }
        `}</style>
      </Header>

      {/* Mobile Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Avatar
              size={32}
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <span style={{ color: '#2563eb', fontWeight: 'bold' }}>ProxyHub</span>
          </div>
        }
        placement="right"
        onClose={() => setMobileMenuOpen(false)}
        open={mobileMenuOpen}
        width={280}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <Button type="text" block style={{ textAlign: 'left', height: '48px' }}>
            🚀 产品特性
          </Button>
          <Button type="text" block style={{ textAlign: 'left', height: '48px' }}>
            💰 价格方案
          </Button>
          <Button type="text" block style={{ textAlign: 'left', height: '48px' }}>
            📚 帮助文档
          </Button>
          <Divider />
          <Link href="/login">
            <Button type="text" block style={{ height: '48px' }}>
              🔐 登录
            </Button>
          </Link>
          <Link href="/register">
            <Button type="primary" block style={{ height: '48px' }}>
              📝 免费注册
            </Button>
          </Link>
        </div>
      </Drawer>

      <Content>
        {/* Hero Section */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '100px 24px',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background Pattern */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.3
          }} />

          <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 1 }}>
            {/* Badge */}
            <Badge.Ribbon text="🚀 企业首选" color="#52c41a" style={{ marginBottom: '24px' }}>
              <div style={{ display: 'inline-block', marginBottom: '32px' }}>
                <Space>
                  <StarFilled style={{ color: '#faad14' }} />
                  <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: '14px' }}>
                    已服务 10万+ 企业用户
                  </Text>
                  <StarFilled style={{ color: '#faad14' }} />
                </Space>
              </div>
            </Badge.Ribbon>

            <Title level={1} style={{
              fontSize: 'clamp(2rem, 5vw, 3.5rem)',
              marginBottom: '24px',
              color: '#fff',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              lineHeight: '1.2'
            }}>
              🌐 全球代理服务<br />
              <Text style={{
                background: 'linear-gradient(45deg, #ffd700, #ffed4e)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}>
                企业级解决方案
              </Text>
            </Title>

            <Paragraph style={{
              fontSize: 'clamp(1rem, 2.5vw, 1.25rem)',
              color: 'rgba(255,255,255,0.9)',
              marginBottom: '48px',
              maxWidth: '800px',
              margin: '0 auto 48px auto',
              lineHeight: '1.6',
              textShadow: '0 1px 2px rgba(0,0,0,0.2)'
            }}>
              🔥 提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议<br />
              <Text strong style={{ color: '#ffd700' }}>⚡ 99.9%稳定性保证</Text> •
              <Text strong style={{ color: '#ffd700' }}>🌍 50+国家覆盖</Text> •
              <Text strong style={{ color: '#ffd700' }}>🛡️ 企业级安全</Text>
            </Paragraph>

            {/* CTA Buttons */}
            <Space size="large" style={{ marginBottom: '64px' }}>
              <Link href="/register">
                <Button
                  type="primary"
                  size="large"
                  icon={<RocketOutlined />}
                  style={{
                    height: '56px',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    background: 'linear-gradient(45deg, #1890ff, #36cfc9)',
                    border: 'none',
                    boxShadow: '0 4px 15px rgba(24, 144, 255, 0.4)'
                  }}
                >
                  立即免费试用
                </Button>
              </Link>
              <Link href="/login">
                <Button
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{
                    height: '56px',
                    fontSize: '16px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '2px solid rgba(255,255,255,0.3)',
                    color: '#fff',
                    backdropFilter: 'blur(10px)'
                  }}
                >
                  观看演示
                </Button>
              </Link>
            </Space>

            {/* Stats */}
            <Row gutter={[24, 24]} style={{ marginBottom: '48px' }}>
              {stats.map((stat, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Card
                    style={{
                      textAlign: 'center',
                      background: 'rgba(255,255,255,0.95)',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '16px',
                      boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    hoverable
                    bodyStyle={{ padding: '24px 16px' }}
                  >
                    <div style={{ marginBottom: '12px' }}>
                      <span style={{
                        fontSize: '2rem',
                        color: stat.color,
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                      }}>
                        {stat.icon}
                      </span>
                    </div>
                    <Statistic
                      title={
                        <Text style={{
                          color: '#666',
                          fontSize: '14px',
                          fontWeight: '500'
                        }}>
                          {stat.title}
                        </Text>
                      }
                      value={stat.value}
                      suffix={stat.suffix}
                      valueStyle={{
                        color: stat.color,
                        fontWeight: 'bold',
                        fontSize: '24px'
                      }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>

            {/* CTA Buttons */}
            <Space size="large" wrap>
              <Link href="/register">
                <Button 
                  type="primary" 
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
                >
                  立即开始免费试用
                </Button>
              </Link>
              <Button 
                size="large"
                icon={<EyeOutlined />}
                style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
              >
                观看演示
              </Button>
            </Space>

            {/* Trust indicators */}
            <div style={{ marginTop: '32px' }}>
              <Space size="large" wrap>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  无需信用卡
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  7天免费试用
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  随时取消
                </Text>
              </Space>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div style={{ padding: '80px 24px', background: '#fff' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '64px' }}>
              <Title level={2} style={{ fontSize: '2.5rem', marginBottom: '16px' }}>
                为什么选择我们？
              </Title>
              <Paragraph style={{ 
                fontSize: '1.25rem', 
                color: '#6b7280',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行
              </Paragraph>
            </div>

            <Row gutter={[32, 32]}>
              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <GlobalOutlined style={{ fontSize: '2rem', color: '#2563eb' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    多协议支持
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <SafetyOutlined style={{ fontSize: '2rem', color: '#059669' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    全球节点覆盖
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ThunderboltOutlined style={{ fontSize: '2rem', color: '#d97706' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    极速稳定
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <LockOutlined style={{ fontSize: '2rem', color: '#7c3aed' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    企业级安全
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    采用军用级加密技术，保护您的数据传输安全和隐私不被泄露
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <BarChartOutlined style={{ fontSize: '2rem', color: '#dc2626' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    实时监控
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    提供详细的使用统计和实时监控面板，帮助您优化代理使用效率
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ApiOutlined style={{ fontSize: '2rem', color: '#0891b2' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    API集成
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    完整的RESTful API接口，支持自动化管理和第三方系统无缝集成
                  </Paragraph>
                </Card>
              </Col>
            </Row>
          </div>
        </div>

        {/* CTA Section */}
        <div style={{ 
          background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
          padding: '80px 24px',
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <Title level={2} style={{ color: 'white', fontSize: '2.5rem', marginBottom: '16px' }}>
              准备开始了吗？
            </Title>
            <Paragraph style={{ 
              fontSize: '1.25rem',
              marginBottom: '32px',
              opacity: 0.9,
              color: 'white'
            }}>
              加入10万+用户的行列，体验专业的代理服务平台
            </Paragraph>
            <Space size="large">
              <Link href="/register">
                <Button 
                  size="large"
                  style={{ 
                    height: '48px', 
                    padding: '0 32px', 
                    fontSize: '16px',
                    background: 'white',
                    color: '#2563eb',
                    border: 'none'
                  }}
                >
                  立即免费注册
                </Button>
              </Link>
              <Button 
                size="large"
                ghost
                style={{ 
                  height: '48px', 
                  padding: '0 32px', 
                  fontSize: '16px',
                  borderColor: 'white',
                  color: 'white'
                }}
              >
                联系销售团队
              </Button>
            </Space>
          </div>
        </div>
      </Content>

      {/* Footer */}
      <Footer style={{ 
        background: '#001529', 
        color: 'white',
        padding: '48px 24px 16px 24px'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', textAlign: 'center' }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
            marginBottom: '32px'
          }}>
            <Avatar 
              size={48} 
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <div>
              <Title level={3} style={{ color: '#2563eb', margin: 0 }}>
                ProxyHub
              </Title>
              <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
                企业级代理服务平台
              </Text>
            </div>
          </div>
          
          <Paragraph style={{ color: '#8c8c8c', marginBottom: '32px' }}>
            为全球用户提供稳定、高速、安全的代理服务解决方案
          </Paragraph>
          
          <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
            © 2024 ProxyHub. 保留所有权利.
          </Text>
        </div>
      </Footer>
    </Layout>
  );
}
