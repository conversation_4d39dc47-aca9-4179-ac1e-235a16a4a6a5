'use client';

import { useState } from 'react';
import Link from "next/link";
import {
  <PERSON><PERSON>,
  Card,
  Row,
  Col,
  Typography,
  Space,
  Statistic,
  Layout,
  Avatar,
  Drawer,
  Badge,
  Divider,
  Flex
} from 'antd';
import {
  SafetyOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  LockOutlined,
  <PERSON><PERSON>hartOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  MenuOutlined,
  StarFilled,
  RocketOutlined,
  SecurityScanOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const stats = [
    { title: '覆盖国家', value: 50, suffix: '+', icon: <GlobalOutlined />, color: '#1890ff' },
    { title: '活跃用户', value: 100000, suffix: '+', icon: <SafetyOutlined />, color: '#52c41a' },
    { title: '稳定性', value: 99.9, suffix: '%', icon: <ThunderboltOutlined />, color: '#faad14' },
    { title: '技术支持', value: '24/7', icon: <ApiOutlined />, color: '#722ed1' }
  ];

  const features = [
    {
      icon: <GlobalOutlined style={{ fontSize: '2.5rem', color: '#1890ff' }} />,
      title: '多协议支持',
      description: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强',
      highlight: 'HTTP/HTTPS & SOCKS5'
    },
    {
      icon: <SecurityScanOutlined style={{ fontSize: '2.5rem', color: '#52c41a' }} />,
      title: '全球节点覆盖',
      description: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验',
      highlight: '50+ 国家地区'
    },
    {
      icon: <ThunderboltOutlined style={{ fontSize: '2.5rem', color: '#faad14' }} />,
      title: '极速稳定',
      description: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行',
      highlight: '99.9% 稳定性'
    },
    {
      icon: <LockOutlined style={{ fontSize: '2.5rem', color: '#722ed1' }} />,
      title: '企业级安全',
      description: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露',
      highlight: '军用级加密'
    },
    {
      icon: <BarChartOutlined style={{ fontSize: '2.5rem', color: '#eb2f96' }} />,
      title: '实时监控',
      description: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率',
      highlight: '实时监控面板'
    },
    {
      icon: <ApiOutlined style={{ fontSize: '2.5rem', color: '#13c2c2' }} />,
      title: 'API集成',
      description: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成',
      highlight: 'RESTful API'
    }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Header */}
      <Header style={{
        background: '#fff',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        padding: '0 16px',
        position: 'sticky',
        top: 0,
        zIndex: 1000
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          maxWidth: '1200px',
          margin: '0 auto',
          height: '64px'
        }}>
          {/* Logo */}
          <Link href="/" style={{ textDecoration: 'none' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', minWidth: 0 }}>
              <Avatar
                size={40}
                style={{ backgroundColor: '#2563eb', flexShrink: 0 }}
                icon={<SafetyOutlined />}
              />
              <div style={{ minWidth: 0 }}>
                <Title level={4} style={{
                  margin: 0,
                  color: '#2563eb',
                  fontSize: '18px',
                  lineHeight: '1.2',
                  whiteSpace: 'nowrap'
                }}>
                  ProxyHub
                </Title>
                <Text type="secondary" style={{
                  fontSize: '11px',
                  lineHeight: '1',
                  display: 'block',
                  whiteSpace: 'nowrap'
                }}>
                  企业级代理服务平台
                </Text>
              </div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div style={{ display: 'none' }} className="desktop-nav">
            <Space size="large">
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#374151',
                  fontSize: '15px'
                }}
                onMouseEnter={(e) => e.target.style.color = '#2563eb'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                产品特性
              </Button>
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#374151',
                  fontSize: '15px'
                }}
                onMouseEnter={(e) => e.target.style.color = '#2563eb'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                价格方案
              </Button>
              <Button
                type="text"
                style={{
                  fontWeight: '500',
                  color: '#374151',
                  fontSize: '15px'
                }}
                onMouseEnter={(e) => e.target.style.color = '#2563eb'}
                onMouseLeave={(e) => e.target.style.color = '#374151'}
              >
                帮助文档
              </Button>
              <Divider type="vertical" style={{ borderColor: '#e5e7eb' }} />
              <Link href="/login">
                <Button
                  type="text"
                  style={{
                    fontWeight: '500',
                    color: '#374151',
                    fontSize: '15px'
                  }}
                  onMouseEnter={(e) => e.target.style.color = '#2563eb'}
                  onMouseLeave={(e) => e.target.style.color = '#374151'}
                >
                  登录
                </Button>
              </Link>
              <Link href="/register">
                <Button
                  type="primary"
                  style={{
                    fontWeight: '600',
                    fontSize: '15px',
                    background: '#2563eb',
                    borderColor: '#2563eb',
                    borderRadius: '8px',
                    height: '40px',
                    paddingLeft: '20px',
                    paddingRight: '20px'
                  }}
                >
                  免费注册
                </Button>
              </Link>
            </Space>
          </div>

          {/* Mobile Navigation */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {/* Mobile Buttons */}
            <div style={{ display: 'flex', gap: '8px' }} className="mobile-nav">
              <Link href="/login">
                <Button type="text" size="small">登录</Button>
              </Link>
              <Link href="/register">
                <Button type="primary" size="small">注册</Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setMobileMenuOpen(true)}
              style={{ display: 'none' }}
              className="mobile-menu-btn"
            />
          </div>
        </div>

        {/* Add responsive CSS */}
        <style jsx>{`
          @media (min-width: 768px) {
            .desktop-nav {
              display: block !important;
            }
            .mobile-nav {
              display: none !important;
            }
            .mobile-menu-btn {
              display: none !important;
            }
          }
          @media (max-width: 767px) {
            .desktop-nav {
              display: none !important;
            }
            .mobile-nav {
              display: flex !important;
            }
            .mobile-menu-btn {
              display: inline-flex !important;
            }
          }
          @media (max-width: 480px) {
            .mobile-nav {
              display: none !important;
            }
            .mobile-menu-btn {
              display: inline-flex !important;
            }
          }
        `}</style>
      </Header>

      {/* Mobile Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Avatar
              size={32}
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <span style={{ color: '#2563eb', fontWeight: 'bold' }}>ProxyHub</span>
          </div>
        }
        placement="right"
        onClose={() => setMobileMenuOpen(false)}
        open={mobileMenuOpen}
        width={280}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '48px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#374151',
              justifyContent: 'flex-start'
            }}
          >
            产品特性
          </Button>
          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '48px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#374151',
              justifyContent: 'flex-start'
            }}
          >
            价格方案
          </Button>
          <Button
            type="text"
            block
            style={{
              textAlign: 'left',
              height: '48px',
              fontSize: '16px',
              fontWeight: '500',
              color: '#374151',
              justifyContent: 'flex-start'
            }}
          >
            帮助文档
          </Button>
          <Divider style={{ margin: '16px 0' }} />
          <Link href="/login">
            <Button
              type="text"
              block
              style={{
                height: '48px',
                fontSize: '16px',
                fontWeight: '500',
                color: '#374151'
              }}
            >
              登录
            </Button>
          </Link>
          <Link href="/register">
            <Button
              type="primary"
              block
              style={{
                height: '48px',
                fontSize: '16px',
                fontWeight: '600',
                background: '#2563eb',
                borderColor: '#2563eb'
              }}
            >
              免费注册
            </Button>
          </Link>
        </div>
      </Drawer>

      <Content>
        {/* Hero Section */}
        <div style={{
          background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',
          padding: '120px 24px',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Background Pattern */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            opacity: 0.4
          }} />

          <div style={{ maxWidth: '1200px', margin: '0 auto', position: 'relative', zIndex: 1 }}>
            {/* Trust Badge */}
            <div style={{ marginBottom: '48px' }}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '12px',
                background: 'rgba(255,255,255,0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '50px',
                padding: '8px 24px',
                marginBottom: '24px'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  background: '#10b981',
                  boxShadow: '0 0 8px #10b981'
                }} />
                <Text style={{
                  color: 'rgba(255,255,255,0.9)',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  已服务 100,000+ 企业用户
                </Text>
              </div>
            </div>

            <Title level={1} style={{
              fontSize: 'clamp(2.5rem, 6vw, 4rem)',
              marginBottom: '32px',
              color: '#ffffff',
              fontWeight: '700',
              lineHeight: '1.1',
              letterSpacing: '-0.02em'
            }}>
              全球代理服务<br />
              <Text style={{
                color: '#3b82f6',
                fontWeight: '700'
              }}>
                企业级解决方案
              </Text>
            </Title>

            <Paragraph style={{
              fontSize: 'clamp(1.1rem, 2.5vw, 1.3rem)',
              color: 'rgba(255,255,255,0.8)',
              marginBottom: '48px',
              maxWidth: '700px',
              margin: '0 auto 48px auto',
              lineHeight: '1.6',
              fontWeight: '400'
            }}>
              提供高质量的全球代理网络，支持 HTTP/HTTPS 和 SOCKS5 协议
              <br />
              <Text style={{ color: '#60a5fa', fontWeight: '600' }}>99.9% 稳定性保证</Text> •
              <Text style={{ color: '#60a5fa', fontWeight: '600' }}>50+ 国家覆盖</Text> •
              <Text style={{ color: '#60a5fa', fontWeight: '600' }}>企业级安全</Text>
            </Paragraph>

            {/* CTA Buttons */}
            <Space size="large" style={{ marginBottom: '80px' }}>
              <Link href="/register">
                <Button
                  type="primary"
                  size="large"
                  style={{
                    height: '56px',
                    fontSize: '16px',
                    fontWeight: '600',
                    background: '#3b82f6',
                    borderColor: '#3b82f6',
                    borderRadius: '12px',
                    paddingLeft: '32px',
                    paddingRight: '32px',
                    boxShadow: '0 8px 25px rgba(59, 130, 246, 0.3)',
                    border: 'none'
                  }}
                >
                  立即免费试用
                </Button>
              </Link>
              <Button
                size="large"
                style={{
                  height: '56px',
                  fontSize: '16px',
                  fontWeight: '500',
                  background: 'rgba(255,255,255,0.1)',
                  border: '2px solid rgba(255,255,255,0.2)',
                  color: '#ffffff',
                  backdropFilter: 'blur(10px)',
                  borderRadius: '12px',
                  paddingLeft: '32px',
                  paddingRight: '32px'
                }}
              >
                了解更多
              </Button>
            </Space>

            {/* Stats */}
            <Row gutter={[32, 32]} style={{ marginBottom: '0' }}>
              {stats.map((stat, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Card
                    style={{
                      textAlign: 'center',
                      background: 'rgba(255,255,255,0.98)',
                      backdropFilter: 'blur(20px)',
                      border: '1px solid rgba(255,255,255,0.3)',
                      borderRadius: '20px',
                      boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      cursor: 'default',
                      overflow: 'hidden'
                    }}
                    bodyStyle={{ padding: '32px 24px' }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-8px)';
                      e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
                    }}
                  >
                    <div style={{
                      marginBottom: '20px',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}>
                      <div style={{
                        width: '60px',
                        height: '60px',
                        borderRadius: '16px',
                        background: `linear-gradient(135deg, ${stat.color}15, ${stat.color}25)`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: `2px solid ${stat.color}20`
                      }}>
                        <span style={{
                          fontSize: '24px',
                          color: stat.color
                        }}>
                          {stat.icon}
                        </span>
                      </div>
                    </div>
                    <Statistic
                      title={
                        <Text style={{
                          color: '#64748b',
                          fontSize: '14px',
                          fontWeight: '500',
                          textTransform: 'uppercase',
                          letterSpacing: '0.5px'
                        }}>
                          {stat.title}
                        </Text>
                      }
                      value={stat.value}
                      suffix={stat.suffix}
                      valueStyle={{
                        color: '#1e293b',
                        fontWeight: '700',
                        fontSize: '28px',
                        lineHeight: '1.2'
                      }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>

            {/* CTA Buttons */}
            <Space size="large" wrap>
              <Link href="/register">
                <Button 
                  type="primary" 
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
                >
                  立即开始免费试用
                </Button>
              </Link>
              <Button 
                size="large"
                icon={<EyeOutlined />}
                style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
              >
                观看演示
              </Button>
            </Space>

            {/* Trust indicators */}
            <div style={{ marginTop: '32px' }}>
              <Space size="large" wrap>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  无需信用卡
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  7天免费试用
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  随时取消
                </Text>
              </Space>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div style={{ padding: '80px 24px', background: '#fff' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '64px' }}>
              <Title level={2} style={{ fontSize: '2.5rem', marginBottom: '16px' }}>
                为什么选择我们？
              </Title>
              <Paragraph style={{ 
                fontSize: '1.25rem', 
                color: '#6b7280',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行
              </Paragraph>
            </div>

            <Row gutter={[32, 32]}>
              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <GlobalOutlined style={{ fontSize: '2rem', color: '#2563eb' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    多协议支持
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <SafetyOutlined style={{ fontSize: '2rem', color: '#059669' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    全球节点覆盖
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ThunderboltOutlined style={{ fontSize: '2rem', color: '#d97706' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    极速稳定
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <LockOutlined style={{ fontSize: '2rem', color: '#7c3aed' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    企业级安全
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    采用军用级加密技术，保护您的数据传输安全和隐私不被泄露
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <BarChartOutlined style={{ fontSize: '2rem', color: '#dc2626' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    实时监控
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    提供详细的使用统计和实时监控面板，帮助您优化代理使用效率
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ApiOutlined style={{ fontSize: '2rem', color: '#0891b2' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    API集成
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    完整的RESTful API接口，支持自动化管理和第三方系统无缝集成
                  </Paragraph>
                </Card>
              </Col>
            </Row>
          </div>
        </div>

        {/* CTA Section */}
        <div style={{ 
          background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
          padding: '80px 24px',
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <Title level={2} style={{ color: 'white', fontSize: '2.5rem', marginBottom: '16px' }}>
              准备开始了吗？
            </Title>
            <Paragraph style={{ 
              fontSize: '1.25rem',
              marginBottom: '32px',
              opacity: 0.9,
              color: 'white'
            }}>
              加入10万+用户的行列，体验专业的代理服务平台
            </Paragraph>
            <Space size="large">
              <Link href="/register">
                <Button 
                  size="large"
                  style={{ 
                    height: '48px', 
                    padding: '0 32px', 
                    fontSize: '16px',
                    background: 'white',
                    color: '#2563eb',
                    border: 'none'
                  }}
                >
                  立即免费注册
                </Button>
              </Link>
              <Button 
                size="large"
                ghost
                style={{ 
                  height: '48px', 
                  padding: '0 32px', 
                  fontSize: '16px',
                  borderColor: 'white',
                  color: 'white'
                }}
              >
                联系销售团队
              </Button>
            </Space>
          </div>
        </div>
      </Content>

      {/* Footer */}
      <Footer style={{ 
        background: '#001529', 
        color: 'white',
        padding: '48px 24px 16px 24px'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', textAlign: 'center' }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
            marginBottom: '32px'
          }}>
            <Avatar 
              size={48} 
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <div>
              <Title level={3} style={{ color: '#2563eb', margin: 0 }}>
                ProxyHub
              </Title>
              <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
                企业级代理服务平台
              </Text>
            </div>
          </div>
          
          <Paragraph style={{ color: '#8c8c8c', marginBottom: '32px' }}>
            为全球用户提供稳定、高速、安全的代理服务解决方案
          </Paragraph>
          
          <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
            © 2024 ProxyHub. 保留所有权利.
          </Text>
        </div>
      </Footer>
    </Layout>
  );
}
