import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Shield, Server, Globe, Zap, Lock, BarChart3 } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-8 w-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">代理系统</h1>
          </div>
          <nav className="flex items-center space-x-4">
            <Link href="/login">
              <Button variant="ghost">登录</Button>
            </Link>
            <Link href="/register">
              <Button>注册</Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            专业的代理服务器管理平台
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            提供高质量的IPv4/IPv6代理服务，支持HTTP/HTTPS和SOCKS5协议，
            覆盖全球多个国家和地区，为您的业务提供稳定可靠的网络代理解决方案。
          </p>
          <div className="flex justify-center space-x-4">
            <Link href="/register">
              <Button size="lg" className="px-8 py-3">
                立即开始
              </Button>
            </Link>
            <Link href="/login">
              <Button variant="outline" size="lg" className="px-8 py-3">
                用户登录
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">
            核心功能特性
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <Server className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>多协议支持</CardTitle>
                <CardDescription>
                  支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景需求
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Globe className="h-12 w-12 text-green-600 mb-4" />
                <CardTitle>全球节点</CardTitle>
                <CardDescription>
                  覆盖全球多个国家和地区，提供低延迟的网络访问体验
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Zap className="h-12 w-12 text-yellow-600 mb-4" />
                <CardTitle>高速稳定</CardTitle>
                <CardDescription>
                  优质的网络基础设施，确保代理服务的高速度和高稳定性
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Lock className="h-12 w-12 text-purple-600 mb-4" />
                <CardTitle>安全可靠</CardTitle>
                <CardDescription>
                  采用先进的加密技术，保护您的数据传输安全和隐私
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <BarChart3 className="h-12 w-12 text-red-600 mb-4" />
                <CardTitle>实时监控</CardTitle>
                <CardDescription>
                  提供详细的使用统计和实时监控，帮助您优化代理使用
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Shield className="h-12 w-12 text-indigo-600 mb-4" />
                <CardTitle>API接口</CardTitle>
                <CardDescription>
                  完整的API接口，支持自动化管理和第三方系统集成
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-blue-600">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl font-bold text-white mb-6">
            准备开始使用了吗？
          </h3>
          <p className="text-xl text-blue-100 mb-8">
            立即注册账户，体验专业的代理服务管理平台
          </p>
          <Link href="/register">
            <Button size="lg" variant="secondary" className="px-8 py-3">
              免费注册
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Shield className="h-6 w-6" />
            <span className="text-lg font-semibold">代理系统</span>
          </div>
          <p className="text-gray-400">
            © 2024 代理系统. 保留所有权利.
          </p>
        </div>
      </footer>
    </div>
  );
}
