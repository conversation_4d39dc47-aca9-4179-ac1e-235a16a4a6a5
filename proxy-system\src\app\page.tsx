import Link from "next/link";
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  Row, 
  Col, 
  Typography, 
  Space, 
  Statistic, 
  Layout,
  Avatar
} from 'antd';
import {
  SafetyOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  LockOutlined,
  BarChartOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function Home() {
  const stats = [
    { title: '覆盖国家', value: 50, suffix: '+', icon: <GlobalOutlined /> },
    { title: '活跃用户', value: 100000, suffix: '+', icon: <SafetyOutlined /> },
    { title: '稳定性', value: 99.9, suffix: '%', icon: <ThunderboltOutlined /> },
    { title: '技术支持', value: '24/7', icon: <ApiOutlined /> }
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* Header */}
      <Header style={{ 
        background: '#fff', 
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        padding: '0 24px'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <Avatar 
              size={40} 
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <div>
              <Title level={4} style={{ margin: 0, color: '#2563eb' }}>
                ProxyHub
              </Title>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                企业级代理服务平台
              </Text>
            </div>
          </div>
          
          <Space size="middle">
            <Link href="/login">
              <Button type="text">登录</Button>
            </Link>
            <Link href="/register">
              <Button type="primary">免费注册</Button>
            </Link>
          </Space>
        </div>
      </Header>

      <Content>
        {/* Hero Section */}
        <div style={{ 
          background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',
          padding: '80px 24px',
          textAlign: 'center'
        }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <Title level={1} style={{ fontSize: '3rem', marginBottom: '24px' }}>
              企业级<br />
              <Text style={{ color: '#2563eb' }}>代理服务解决方案</Text>
            </Title>

            <Paragraph style={{ 
              fontSize: '1.25rem', 
              color: '#6b7280', 
              marginBottom: '48px',
              maxWidth: '800px',
              margin: '0 auto 48px auto'
            }}>
              提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议，
              <Text strong style={{ color: '#2563eb' }}>99.9%稳定性保证</Text>，
              助力您的业务全球化发展
            </Paragraph>

            {/* Stats */}
            <Row gutter={[32, 16]} style={{ marginBottom: '48px' }}>
              {stats.map((stat, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Card size="small" style={{ textAlign: 'center' }}>
                    <Statistic
                      title={stat.title}
                      value={stat.value}
                      suffix={stat.suffix}
                      prefix={<span style={{ fontSize: '1.2rem', color: '#2563eb' }}>{stat.icon}</span>}
                      valueStyle={{ color: '#2563eb', fontWeight: 'bold' }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>

            {/* CTA Buttons */}
            <Space size="large" wrap>
              <Link href="/register">
                <Button 
                  type="primary" 
                  size="large"
                  icon={<PlayCircleOutlined />}
                  style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
                >
                  立即开始免费试用
                </Button>
              </Link>
              <Button 
                size="large"
                icon={<EyeOutlined />}
                style={{ height: '48px', padding: '0 32px', fontSize: '16px' }}
              >
                观看演示
              </Button>
            </Space>

            {/* Trust indicators */}
            <div style={{ marginTop: '32px' }}>
              <Space size="large" wrap>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  无需信用卡
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  7天免费试用
                </Text>
                <Text type="secondary">
                  <CheckCircleOutlined style={{ color: '#059669', marginRight: '8px' }} />
                  随时取消
                </Text>
              </Space>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div style={{ padding: '80px 24px', background: '#fff' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
            <div style={{ textAlign: 'center', marginBottom: '64px' }}>
              <Title level={2} style={{ fontSize: '2.5rem', marginBottom: '16px' }}>
                为什么选择我们？
              </Title>
              <Paragraph style={{ 
                fontSize: '1.25rem', 
                color: '#6b7280',
                maxWidth: '600px',
                margin: '0 auto'
              }}>
                我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行
              </Paragraph>
            </div>

            <Row gutter={[32, 32]}>
              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <GlobalOutlined style={{ fontSize: '2rem', color: '#2563eb' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    多协议支持
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <SafetyOutlined style={{ fontSize: '2rem', color: '#059669' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    全球节点覆盖
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ThunderboltOutlined style={{ fontSize: '2rem', color: '#d97706' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    极速稳定
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <LockOutlined style={{ fontSize: '2rem', color: '#7c3aed' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    企业级安全
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    采用军用级加密技术，保护您的数据传输安全和隐私不被泄露
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <BarChartOutlined style={{ fontSize: '2rem', color: '#dc2626' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    实时监控
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    提供详细的使用统计和实时监控面板，帮助您优化代理使用效率
                  </Paragraph>
                </Card>
              </Col>

              <Col xs={24} md={12} lg={8}>
                <Card 
                  hoverable
                  style={{ height: '100%', textAlign: 'center' }}
                  bodyStyle={{ padding: '32px 24px' }}
                >
                  <div style={{ marginBottom: '16px' }}>
                    <ApiOutlined style={{ fontSize: '2rem', color: '#0891b2' }} />
                  </div>
                  <Title level={4} style={{ marginBottom: '12px' }}>
                    API集成
                  </Title>
                  <Paragraph style={{ color: '#6b7280', lineHeight: '1.6' }}>
                    完整的RESTful API接口，支持自动化管理和第三方系统无缝集成
                  </Paragraph>
                </Card>
              </Col>
            </Row>
          </div>
        </div>

        {/* CTA Section */}
        <div style={{ 
          background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
          padding: '80px 24px',
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <Title level={2} style={{ color: 'white', fontSize: '2.5rem', marginBottom: '16px' }}>
              准备开始了吗？
            </Title>
            <Paragraph style={{ 
              fontSize: '1.25rem',
              marginBottom: '32px',
              opacity: 0.9,
              color: 'white'
            }}>
              加入10万+用户的行列，体验专业的代理服务平台
            </Paragraph>
            <Space size="large">
              <Link href="/register">
                <Button 
                  size="large"
                  style={{ 
                    height: '48px', 
                    padding: '0 32px', 
                    fontSize: '16px',
                    background: 'white',
                    color: '#2563eb',
                    border: 'none'
                  }}
                >
                  立即免费注册
                </Button>
              </Link>
              <Button 
                size="large"
                ghost
                style={{ 
                  height: '48px', 
                  padding: '0 32px', 
                  fontSize: '16px',
                  borderColor: 'white',
                  color: 'white'
                }}
              >
                联系销售团队
              </Button>
            </Space>
          </div>
        </div>
      </Content>

      {/* Footer */}
      <Footer style={{ 
        background: '#001529', 
        color: 'white',
        padding: '48px 24px 16px 24px'
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', textAlign: 'center' }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
            marginBottom: '32px'
          }}>
            <Avatar 
              size={48} 
              style={{ backgroundColor: '#2563eb' }}
              icon={<SafetyOutlined />}
            />
            <div>
              <Title level={3} style={{ color: '#2563eb', margin: 0 }}>
                ProxyHub
              </Title>
              <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
                企业级代理服务平台
              </Text>
            </div>
          </div>
          
          <Paragraph style={{ color: '#8c8c8c', marginBottom: '32px' }}>
            为全球用户提供稳定、高速、安全的代理服务解决方案
          </Paragraph>
          
          <Text style={{ color: '#8c8c8c', fontSize: '14px' }}>
            © 2024 ProxyHub. 保留所有权利.
          </Text>
        </div>
      </Footer>
    </Layout>
  );
}
