import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getUserFromToken, getUserFromApiKey, logApiCall } from '@/lib/auth';
import { ProxyAPIClient } from '@/lib/api-client';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 认证
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user || !user.apiKey) {
      await logApiCall(
        null,
        'POST',
        '/api/proxy/prolong',
        {},
        { error: '未授权访问或未设置API密钥' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问或未设置API密钥' },
        { status: 401 }
      );
    }

    const { proxyIds, period } = await request.json();

    // 验证输入
    if (!proxyIds || !Array.isArray(proxyIds) || proxyIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要延期的代理' },
        { status: 400 }
      );
    }

    if (!period || period < 1 || period > 365) {
      return NextResponse.json(
        { error: '延期天数必须在1-365天之间' },
        { status: 400 }
      );
    }

    // 查找用户的代理
    const proxies = await db.proxy.findMany({
      where: {
        id: { in: proxyIds },
        userId: user.id,
      },
    });

    if (proxies.length === 0) {
      return NextResponse.json(
        { error: '未找到指定的代理' },
        { status: 404 }
      );
    }

    if (proxies.length !== proxyIds.length) {
      return NextResponse.json(
        { error: '部分代理不存在或不属于您' },
        { status: 400 }
      );
    }

    // 创建API客户端
    const proxyClient = new ProxyAPIClient(user.apiKey);

    // 构建外部ID列表
    const externalIds = proxies.map(p => p.externalId).join(',');

    // 延期代理
    const prolongResult = await proxyClient.prolongProxy({
      period,
      ids: externalIds,
    });

    // 检查用户余额
    if (user.balance < prolongResult.price) {
      return NextResponse.json(
        { error: '余额不足，请先充值' },
        { status: 400 }
      );
    }

    // 开始数据库事务
    const result = await db.$transaction(async (tx) => {
      // 扣除用户余额
      await tx.user.update({
        where: { id: user.id },
        data: {
          balance: {
            decrement: prolongResult.price,
          },
        },
      });

      // 记录交易
      const transaction = await tx.transaction.create({
        data: {
          userId: user.id,
          type: 'prolong',
          amount: -prolongResult.price,
          currency: user.currency,
          description: `延期 ${proxies.length} 个代理，期限 ${period} 天`,
          status: 'completed',
        },
      });

      // 更新代理到期时间
      const updatedProxies = [];
      for (const [externalId, prolongInfo] of Object.entries(prolongResult.list)) {
        const proxy = await tx.proxy.updateMany({
          where: {
            externalId: externalId.toString(),
            userId: user.id,
          },
          data: {
            expiryDate: new Date(prolongInfo.date_end),
          },
        });
        
        // 获取更新后的代理信息
        const updatedProxy = await tx.proxy.findFirst({
          where: {
            externalId: externalId.toString(),
            userId: user.id,
          },
        });
        
        if (updatedProxy) {
          updatedProxies.push(updatedProxy);
        }
      }

      return { transaction, proxies: updatedProxies };
    });

    const response = {
      message: '代理延期成功',
      transaction: result.transaction,
      proxies: result.proxies,
      totalCost: prolongResult.price,
      remainingBalance: user.balance - prolongResult.price,
    };

    await logApiCall(
      user.id,
      'POST',
      '/api/proxy/prolong',
      { proxyIds, period },
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Prolong proxy error:', error);
    
    let errorMessage = '延期代理失败';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API Error')) {
        errorMessage = error.message;
        statusCode = 400;
      }
    }

    await logApiCall(
      user?.id || null,
      'POST',
      '/api/proxy/prolong',
      {},
      { error: errorMessage },
      statusCode,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
