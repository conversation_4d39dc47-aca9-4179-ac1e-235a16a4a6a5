import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { verifyPassword, generateToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const { login, password } = await request.json();

    // 验证输入
    if (!login || !password) {
      return NextResponse.json(
        { error: '用户名/邮箱和密码都是必填项' },
        { status: 400 }
      );
    }

    // 查找用户（支持邮箱或用户名登录）
    const user = await db.user.findFirst({
      where: {
        OR: [
          { email: login },
          { username: login },
        ],
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: '用户名/邮箱或密码错误' },
        { status: 401 }
      );
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: '用户名/邮箱或密码错误' },
        { status: 401 }
      );
    }

    // 生成JWT令牌
    const token = generateToken({
      userId: user.id,
      email: user.email,
      username: user.username,
    });

    // 返回用户信息（不包含密码）
    const userResponse = {
      id: user.id,
      email: user.email,
      username: user.username,
      balance: user.balance,
      currency: user.currency,
      createdAt: user.createdAt,
    };

    return NextResponse.json({
      message: '登录成功',
      user: userResponse,
      token,
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: '登录失败，请稍后重试' },
      { status: 500 }
    );
  }
}
