# Next.js UI 组件库使用指南

## 📋 目录
- [当前状态](#当前状态)
- [推荐组件库](#推荐组件库)
- [迁移指南](#迁移指南)
- [最佳实践](#最佳实践)
- [性能优化](#性能优化)

## 🎯 当前状态

### 现有样式方案
项目目前使用**内联样式系统**，具有以下特点：

```tsx
// 示例：当前的按钮实现
<button style={{
  backgroundColor: '#2563eb',
  color: 'white',
  padding: '0.75rem 1.5rem',
  borderRadius: '0.375rem',
  border: 'none',
  cursor: 'pointer',
  fontSize: '0.875rem',
  fontWeight: '500'
}}>
  立即注册
</button>
```

### 优势
- ✅ **零依赖** - 无需安装任何 UI 库
- ✅ **100% 兼容** - 不会出现样式加载问题
- ✅ **轻量级** - 减少包体积
- ✅ **完全控制** - 精确控制样式
- ✅ **无冲突** - 避免样式优先级问题

### 劣势
- ❌ **开发效率低** - 需要手写大量样式代码
- ❌ **维护困难** - 样式分散在各个组件中
- ❌ **一致性差** - 难以保持设计系统一致性
- ❌ **复用性低** - 样式代码重复度高

## 🚀 推荐组件库

### 1. shadcn/ui (强烈推荐)

#### 特点
- 基于 Radix UI + Tailwind CSS
- 复制粘贴式使用，无运行时依赖
- 完全可定制
- TypeScript 原生支持

#### 安装步骤
```bash
# 1. 安装 Tailwind CSS
npm install tailwindcss postcss autoprefixer
npx tailwindcss init -p

# 2. 安装 shadcn/ui
npx shadcn-ui@latest init

# 3. 添加所需组件
npx shadcn-ui@latest add button
npx shadcn-ui@latest add card
npx shadcn-ui@latest add input
npx shadcn-ui@latest add label
npx shadcn-ui@latest add select
npx shadcn-ui@latest add dialog
```

#### 使用示例
```tsx
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

export default function LoginForm() {
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>用户登录</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Input placeholder="请输入邮箱" />
        <Input type="password" placeholder="请输入密码" />
        <Button className="w-full">登录</Button>
      </CardContent>
    </Card>
  )
}
```

### 2. Ant Design (企业级)

#### 特点
- 丰富的企业级组件
- 完善的设计语言
- 国际化支持
- 成熟稳定

#### 安装步骤
```bash
npm install antd @ant-design/nextjs-registry
```

#### 配置 (app/layout.tsx)
```tsx
import { AntdRegistry } from '@ant-design/nextjs-registry'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh">
      <body>
        <AntdRegistry>{children}</AntdRegistry>
      </body>
    </html>
  )
}
```

#### 使用示例
```tsx
import { Button, Card, Input, Form } from 'antd'

export default function LoginForm() {
  return (
    <Card title="用户登录" style={{ width: 400 }}>
      <Form layout="vertical">
        <Form.Item label="邮箱">
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item label="密码">
          <Input.Password placeholder="请输入密码" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" block>
            登录
          </Button>
        </Form.Item>
      </Form>
    </Card>
  )
}
```

### 3. Chakra UI (简洁优雅)

#### 安装步骤
```bash
npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion
```

#### 使用示例
```tsx
import { Box, Button, Input, VStack } from '@chakra-ui/react'

export default function LoginForm() {
  return (
    <Box maxW="md" mx="auto" p={6} borderWidth={1} borderRadius="lg">
      <VStack spacing={4}>
        <Input placeholder="请输入邮箱" />
        <Input type="password" placeholder="请输入密码" />
        <Button colorScheme="blue" width="full">
          登录
        </Button>
      </VStack>
    </Box>
  )
}
```

## 🔄 迁移指南

### 从内联样式迁移到 shadcn/ui

#### 1. 按钮组件迁移

**迁移前 (内联样式):**
```tsx
<button
  onClick={handleSubmit}
  disabled={isLoading}
  style={{
    width: '100%',
    padding: '0.75rem',
    backgroundColor: isLoading ? '#9ca3af' : '#2563eb',
    color: 'white',
    border: 'none',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    fontWeight: '500',
    cursor: isLoading ? 'not-allowed' : 'pointer'
  }}
>
  {isLoading ? '登录中...' : '登录'}
</button>
```

**迁移后 (shadcn/ui):**
```tsx
import { Button } from '@/components/ui/button'

<Button 
  onClick={handleSubmit}
  disabled={isLoading}
  className="w-full"
>
  {isLoading ? '登录中...' : '登录'}
</Button>
```

#### 2. 表单组件迁移

**迁移前:**
```tsx
<input
  type="email"
  value={email}
  onChange={(e) => setEmail(e.target.value)}
  style={{
    width: '100%',
    padding: '0.75rem',
    border: '1px solid #d1d5db',
    borderRadius: '0.375rem',
    fontSize: '0.875rem',
    outline: 'none'
  }}
  placeholder="请输入邮箱"
/>
```

**迁移后:**
```tsx
import { Input } from '@/components/ui/input'

<Input
  type="email"
  value={email}
  onChange={(e) => setEmail(e.target.value)}
  placeholder="请输入邮箱"
/>
```

#### 3. 卡片组件迁移

**迁移前:**
```tsx
<div style={{
  backgroundColor: 'white',
  borderRadius: '0.75rem',
  padding: '1.5rem',
  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  border: '1px solid #e5e7eb'
}}>
  <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
    标题
  </h3>
  <p style={{ color: '#6b7280' }}>内容</p>
</div>
```

**迁移后:**
```tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-gray-600">内容</p>
  </CardContent>
</Card>
```

### 迁移步骤

1. **准备工作**
   ```bash
   # 备份当前代码
   git add .
   git commit -m "backup: 迁移前备份"
   
   # 创建新分支
   git checkout -b feature/ui-library-migration
   ```

2. **安装组件库**
   ```bash
   # 选择一个组件库进行安装
   npx shadcn-ui@latest init
   ```

3. **逐步迁移**
   - 从最简单的组件开始（Button、Input）
   - 一次迁移一个页面
   - 保持功能不变，只替换样式实现

4. **测试验证**
   ```bash
   npm run dev
   # 测试所有页面功能是否正常
   ```

5. **清理代码**
   - 删除不再使用的内联样式
   - 统一组件使用方式
   - 更新类型定义

## 📚 最佳实践

### 1. 组件设计原则
- **一致性** - 使用统一的设计系统
- **可复用** - 创建可复用的组件
- **可访问** - 支持键盘导航和屏幕阅读器
- **响应式** - 适配不同设备尺寸

### 2. 主题定制
```tsx
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a',
        }
      }
    }
  }
}
```

### 3. 组件组合
```tsx
// 创建复合组件
export function LoginCard({ children }: { children: React.ReactNode }) {
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">用户登录</CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  )
}
```

## ⚡ 性能优化

### 1. 按需导入
```tsx
// ✅ 推荐：按需导入
import { Button } from '@/components/ui/button'

// ❌ 避免：全量导入
import * as UI from '@/components/ui'
```

### 2. 代码分割
```tsx
// 动态导入大型组件
const DataTable = dynamic(() => import('@/components/ui/data-table'), {
  loading: () => <div>加载中...</div>
})
```

### 3. 样式优化
```tsx
// 使用 CSS 变量进行主题切换
:root {
  --primary: 220 14% 96%;
  --primary-foreground: 220 9% 46%;
}

[data-theme="dark"] {
  --primary: 220 13% 18%;
  --primary-foreground: 220 9% 46%;
}
```

## 🎯 推荐方案

基于项目需求，推荐使用 **shadcn/ui** 作为主要 UI 组件库：

### 理由
1. **零运行时依赖** - 组件直接复制到项目中
2. **完全可定制** - 可以根据需求修改组件
3. **TypeScript 友好** - 原生 TypeScript 支持
4. **现代化设计** - 基于最新的设计趋势
5. **活跃社区** - 持续更新和维护

### 实施计划
1. **第一阶段** - 迁移基础组件（Button、Input、Card）
2. **第二阶段** - 迁移表单组件（Form、Select、Dialog）
3. **第三阶段** - 迁移复杂组件（Table、Chart、Layout）
4. **第四阶段** - 优化和定制主题

通过合理的迁移计划，可以在保持项目稳定性的同时，显著提升开发效率和用户体验。
