'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings, Save, RefreshCw } from 'lucide-react';
import { defaultPricingConfig, PricingConfig } from '@/lib/pricing';

export default function AdminPricingPage() {
  const [config, setConfig] = useState<PricingConfig>(defaultPricingConfig);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // 这里应该调用API保存配置
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
      alert('定价配置已保存');
    } catch (error) {
      alert('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setConfig(defaultPricingConfig);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">定价配置管理</h1>
          <p className="text-gray-600">管理代理商的定价策略和折扣规则</p>
        </div>

        <div className="grid gap-6">
          {/* 基础定价 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>基础定价设置</span>
              </CardTitle>
              <CardDescription>
                设置代理服务的基础价格和加价策略
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">基础价格（每个代理/天）</label>
                  <Input
                    type="number"
                    step="0.1"
                    value={config.basePrice}
                    onChange={(e) => setConfig({
                      ...config,
                      basePrice: parseFloat(e.target.value) || 0
                    })}
                  />
                  <p className="text-xs text-gray-500">从上游供应商采购的成本价格</p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">加价比例</label>
                  <Input
                    type="number"
                    step="0.01"
                    value={config.markup}
                    onChange={(e) => setConfig({
                      ...config,
                      markup: parseFloat(e.target.value) || 0
                    })}
                  />
                  <p className="text-xs text-gray-500">例如：0.4 表示40%加价</p>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">最小订购数量</label>
                <Input
                  type="number"
                  value={config.minimumOrder}
                  onChange={(e) => setConfig({
                    ...config,
                    minimumOrder: parseInt(e.target.value) || 1
                  })}
                />
              </div>
            </CardContent>
          </Card>

          {/* 数量折扣 */}
          <Card>
            <CardHeader>
              <CardTitle>数量折扣设置</CardTitle>
              <CardDescription>
                根据购买数量设置不同的折扣比例
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {config.discounts.map((discount, index) => (
                  <div key={index} className="grid md:grid-cols-3 gap-4 items-end">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">数量阈值</label>
                      <Input
                        type="number"
                        value={discount.quantity}
                        onChange={(e) => {
                          const newDiscounts = [...config.discounts];
                          newDiscounts[index].quantity = parseInt(e.target.value) || 0;
                          setConfig({ ...config, discounts: newDiscounts });
                        }}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">折扣比例</label>
                      <Input
                        type="number"
                        step="0.01"
                        value={discount.discount}
                        onChange={(e) => {
                          const newDiscounts = [...config.discounts];
                          newDiscounts[index].discount = parseFloat(e.target.value) || 0;
                          setConfig({ ...config, discounts: newDiscounts });
                        }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => {
                        const newDiscounts = config.discounts.filter((_, i) => i !== index);
                        setConfig({ ...config, discounts: newDiscounts });
                      }}
                    >
                      删除
                    </Button>
                  </div>
                ))}
                
                <Button
                  variant="outline"
                  onClick={() => {
                    setConfig({
                      ...config,
                      discounts: [...config.discounts, { quantity: 0, discount: 0 }]
                    });
                  }}
                >
                  添加折扣规则
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* VIP折扣 */}
          <Card>
            <CardHeader>
              <CardTitle>VIP等级折扣</CardTitle>
              <CardDescription>
                为不同VIP等级的用户设置专属折扣
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {config.vipDiscounts.map((vip, index) => (
                  <div key={index} className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">VIP等级</label>
                      <Input
                        value={vip.level}
                        disabled
                        className="bg-gray-50"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">折扣比例</label>
                      <Input
                        type="number"
                        step="0.01"
                        value={vip.discount}
                        onChange={(e) => {
                          const newVipDiscounts = [...config.vipDiscounts];
                          newVipDiscounts[index].discount = parseFloat(e.target.value) || 0;
                          setConfig({ ...config, vipDiscounts: newVipDiscounts });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={isSaving}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重置为默认
            </Button>
            <Button
              onClick={handleSave}
              disabled={isSaving}
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? '保存中...' : '保存配置'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
