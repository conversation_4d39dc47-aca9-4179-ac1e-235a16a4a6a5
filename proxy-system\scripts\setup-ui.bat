@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🎨 ProxyHub UI 组件库配置向导
echo ================================
echo.

echo 请选择要配置的 UI 组件库:
echo 1) shadcn/ui (推荐)
echo 2) Ant Design
echo 3) Chakra UI
echo 4) Mantine
echo 5) 保持当前内联样式
echo.

set /p choice="请输入选项 (1-5): "

if "%choice%"=="1" (
    echo.
    echo ✅ 正在配置 shadcn/ui...
    echo.
    
    echo 📦 安装 Tailwind CSS...
    call npm install -D tailwindcss postcss autoprefixer
    call npx tailwindcss init -p
    
    echo 📦 安装 shadcn/ui 依赖...
    call npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge lucide-react
    
    echo 🔧 初始化 shadcn/ui...
    call npx shadcn-ui@latest init --yes
    
    echo 📦 添加常用组件...
    call npx shadcn-ui@latest add button
    call npx shadcn-ui@latest add card
    call npx shadcn-ui@latest add input
    call npx shadcn-ui@latest add label
    call npx shadcn-ui@latest add select
    call npx shadcn-ui@latest add dialog
    call npx shadcn-ui@latest add badge
    call npx shadcn-ui@latest add alert
    
    echo.
    echo ✅ shadcn/ui 配置完成！
    echo 📖 使用指南: docs/UI_COMPONENTS.md
    
) else if "%choice%"=="2" (
    echo.
    echo ✅ 正在配置 Ant Design...
    echo.
    
    echo 📦 安装 Ant Design...
    call npm install antd @ant-design/nextjs-registry
    
    echo 🔧 创建配置文件...
    (
        echo 'use client'
        echo.
        echo import { AntdRegistry } from '@ant-design/nextjs-registry'
        echo.
        echo export function Providers({ children }: { children: React.ReactNode }) {
        echo   return ^<AntdRegistry^>{children}^</AntdRegistry^>
        echo }
    ) > src\app\providers.tsx
    
    echo.
    echo ✅ Ant Design 配置完成！
    echo ⚠️  请在 app/layout.tsx 中添加 Providers 组件
    
) else if "%choice%"=="3" (
    echo.
    echo ✅ 正在配置 Chakra UI...
    echo.
    
    echo 📦 安装 Chakra UI...
    call npm install @chakra-ui/react @emotion/react @emotion/styled framer-motion
    
    echo 🔧 创建配置文件...
    (
        echo 'use client'
        echo.
        echo import { ChakraProvider } from '@chakra-ui/react'
        echo.
        echo export function Providers({ children }: { children: React.ReactNode }) {
        echo   return ^<ChakraProvider^>{children}^</ChakraProvider^>
        echo }
    ) > src\app\providers.tsx
    
    echo.
    echo ✅ Chakra UI 配置完成！
    echo ⚠️  请在 app/layout.tsx 中添加 Providers 组件
    
) else if "%choice%"=="4" (
    echo.
    echo ✅ 正在配置 Mantine...
    echo.
    
    echo 📦 安装 Mantine...
    call npm install @mantine/core @mantine/hooks @mantine/form @mantine/notifications
    
    echo 🔧 创建配置文件...
    (
        echo 'use client'
        echo.
        echo import { MantineProvider } from '@mantine/core'
        echo.
        echo export function Providers({ children }: { children: React.ReactNode }) {
        echo   return ^<MantineProvider^>{children}^</MantineProvider^>
        echo }
    ) > src\app\providers.tsx
    
    echo.
    echo ✅ Mantine 配置完成！
    echo ⚠️  请在 app/layout.tsx 中添加 Providers 组件
    
) else if "%choice%"=="5" (
    echo.
    echo 🔵 保持当前内联样式系统
    echo ✅ 当前配置无需更改
    echo 📖 内联样式使用指南: docs/UI_COMPONENTS.md
    
) else (
    echo.
    echo ❌ 无效选项，请重新运行脚本
    pause
    exit /b 1
)

echo.
echo 🎉 配置完成！
echo.
echo 📚 更多信息:
echo - 📖 UI 组件库使用指南: docs/UI_COMPONENTS.md
echo - 🚀 项目文档: PROJECT_SUMMARY.md
echo - 💻 开发指南: docs/DEVELOPMENT.md
echo.
echo 💡 提示: 运行 'npm run dev' 启动开发服务器
echo.
pause
