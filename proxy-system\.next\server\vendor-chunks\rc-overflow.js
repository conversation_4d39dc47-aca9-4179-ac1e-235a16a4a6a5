"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-overflow";
exports.ids = ["vendor-chunks/rc-overflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-overflow/es/Item.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-overflow/es/Item.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n\n\n\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\n\n\n\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(!invalidate && prefixCls, className),\n    style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalItem);\nItem.displayName = 'Item';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Item);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/Overflow.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-overflow/es/Overflow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\");\n/* harmony import */ var _RawItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RawItem */ \"(ssr)/./node_modules/rc-overflow/es/RawItem.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\n\n\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\n\n\n\n\n\n\n\n\n\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\n\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__.useBatcher)();\n  var _useEffectState = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, null),\n    _useEffectState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, new Map()),\n    _useEffectState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0),\n    _useEffectState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),\n    _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),\n    _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n      key: key,\n      value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n    value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = _RawItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardOverflow);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Overflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/RawItem.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/RawItem.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\nvar _excluded = [\"component\"],\n  _excluded2 = [\"className\"],\n  _excluded3 = [\"className\"];\n\n\n\n\nvar InternalRawItem = function InternalRawItem(props, ref) {\n  var context = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext);\n\n  // Render directly when context not provided\n  if (!context) {\n    var _props$component = props.component,\n      Component = _props$component === void 0 ? 'div' : _props$component,\n      _restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, _restProps, {\n      ref: ref\n    }));\n  }\n  var contextClassName = context.className,\n    restContext = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(context, _excluded2);\n  var className = props.className,\n    restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded3);\n\n  // Do not pass context to sub item to avoid multiple measure\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext.Provider, {\n    value: null\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Item__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(contextClassName, className)\n  }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalRawItem);\nRawItem.displayName = 'RawItem';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RawItem);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/RawItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/context.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* binding */ OverflowContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OverflowContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEI7QUFDbkIsbUNBQW1DLDBEQUFtQiIsInNvdXJjZXMiOlsiRTpcXOS7o+eggVxcUHJveHlcXHZzY29kZV9wcm94eVxccHJveHktc3lzdGVtXFxub2RlX21vZHVsZXNcXHJjLW92ZXJmbG93XFxlc1xcY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuZXhwb3J0IHZhciBPdmVyZmxvd0NvbnRleHQgPSAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlQ29udGV4dChudWxsKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/channelUpdate.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ channelUpdate)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n\nfunction channelUpdate(callback) {\n  if (typeof MessageChannel === 'undefined') {\n    (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n  } else {\n    var channel = new MessageChannel();\n    channel.port1.onmessage = function () {\n      return callback();\n    };\n    channel.port2.postMessage(undefined);\n  }\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvY2hhbm5lbFVwZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUNsQjtBQUNmO0FBQ0EsSUFBSSwwREFBRztBQUNQLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXOS7o+eggVxcUHJveHlcXHZzY29kZV9wcm94eVxccHJveHktc3lzdGVtXFxub2RlX21vZHVsZXNcXHJjLW92ZXJmbG93XFxlc1xcaG9va3NcXGNoYW5uZWxVcGRhdGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJhZiBmcm9tIFwicmMtdXRpbC9lcy9yYWZcIjtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGNoYW5uZWxVcGRhdGUoY2FsbGJhY2spIHtcbiAgaWYgKHR5cGVvZiBNZXNzYWdlQ2hhbm5lbCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByYWYoY2FsbGJhY2spO1xuICB9IGVsc2Uge1xuICAgIHZhciBjaGFubmVsID0gbmV3IE1lc3NhZ2VDaGFubmVsKCk7XG4gICAgY2hhbm5lbC5wb3J0MS5vbm1lc3NhZ2UgPSBmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gY2FsbGJhY2soKTtcbiAgICB9O1xuICAgIGNoYW5uZWwucG9ydDIucG9zdE1lc3NhZ2UodW5kZWZpbmVkKTtcbiAgfVxufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState),\n/* harmony export */   useBatcher: () => (/* binding */ useBatcher)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _channelUpdate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./channelUpdate */ \"(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\");\n\n\n\n\n\n/**\n * Batcher for record any `useEffectState` need update.\n */\nfunction useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      (0,_channelUpdate__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function () {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_batchedUpdates)(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nfunction useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(defaultValue),\n    _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-overflow/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Overflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Overflow */ \"(ssr)/./node_modules/rc-overflow/es/Overflow.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Overflow__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWUsaURBQVEiLCJzb3VyY2VzIjpbIkU6XFzku6PnoIFcXFByb3h5XFx2c2NvZGVfcHJveHlcXHByb3h5LXN5c3RlbVxcbm9kZV9tb2R1bGVzXFxyYy1vdmVyZmxvd1xcZXNcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBPdmVyZmxvdyBmcm9tIFwiLi9PdmVyZmxvd1wiO1xuZXhwb3J0IGRlZmF1bHQgT3ZlcmZsb3c7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/index.js\n");

/***/ })

};
;