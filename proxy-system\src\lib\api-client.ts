import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  BaseResponse,
  SuccessResponse,
  ErrorResponse,
  GetPriceParams,
  GetPriceResponse,
  GetCountParams,
  GetCountResponse,
  GetCountryParams,
  GetCountryResponse,
  GetProxyParams,
  GetProxyResponse,
  SetTypeParams,
  SetDescrParams,
  SetDescrResponse,
  BuyProxyParams,
  BuyProxyResponse,
  ProlongProxyParams,
  ProlongProxyResponse,
  DeleteProxyParams,
  DeleteProxyResponse,
  CheckProxyParams,
  CheckProxyResponse,
  ERROR_CODES
} from '@/types/api';

export class ProxyAPIError extends Error {
  constructor(
    public errorId: number,
    public errorMessage: string
  ) {
    super(`API Error ${errorId}: ${errorMessage}`);
    this.name = 'ProxyAPIError';
  }
}

export class ProxyAPIClient {
  private client: AxiosInstance;
  private apiKey: string;
  private baseURL = 'https://px6.link/api';

  constructor(apiKey?: string) {
    // 使用系统配置的API密钥，而不是用户的密钥
    this.apiKey = apiKey || process.env.PX6_API_KEY || '';
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 添加响应拦截器处理错误
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 429) {
          throw new ProxyAPIError(429, 'Too Many Requests - API rate limit exceeded');
        }
        throw error;
      }
    );
  }

  private async makeRequest<T extends BaseResponse>(
    method: string,
    params?: Record<string, any>
  ): Promise<T> {
    const url = `/${this.apiKey}${method ? `/${method}` : ''}`;
    
    try {
      const response: AxiosResponse<T> = await this.client.get(url, {
        params: params || {},
      });

      const data = response.data;

      // 检查API响应状态
      if (data.status === 'no') {
        const errorData = data as ErrorResponse;
        const errorMessage = ERROR_CODES[errorData.error_id as keyof typeof ERROR_CODES] || errorData.error;
        throw new ProxyAPIError(errorData.error_id, errorMessage);
      }

      return data;
    } catch (error) {
      if (error instanceof ProxyAPIError) {
        throw error;
      }
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new ProxyAPIError(429, 'Too Many Requests - API rate limit exceeded');
        }
        throw new ProxyAPIError(0, `Network error: ${error.message}`);
      }
      
      throw new ProxyAPIError(0, `Unknown error: ${error}`);
    }
  }

  // 验证API密钥
  async validateKey(): Promise<SuccessResponse> {
    return this.makeRequest<SuccessResponse>('');
  }

  // 获取价格信息
  async getPrice(params: GetPriceParams): Promise<GetPriceResponse> {
    return this.makeRequest<GetPriceResponse>('getprice', {
      count: params.count,
      period: params.period,
      version: params.version || '6',
    });
  }

  // 获取可用代理数量
  async getCount(params: GetCountParams): Promise<GetCountResponse> {
    return this.makeRequest<GetCountResponse>('getcount', {
      country: params.country,
      version: params.version || '6',
    });
  }

  // 获取可用国家列表
  async getCountry(params: GetCountryParams = {}): Promise<GetCountryResponse> {
    return this.makeRequest<GetCountryResponse>('getcountry', {
      version: params.version || '6',
    });
  }

  // 获取代理列表
  async getProxy(params: GetProxyParams = {}): Promise<GetProxyResponse> {
    const requestParams: Record<string, any> = {};
    
    if (params.state) requestParams.state = params.state;
    if (params.descr) requestParams.descr = params.descr;
    if (params.nokey) requestParams.nokey = '';
    if (params.page) requestParams.page = params.page;
    if (params.limit) requestParams.limit = params.limit;

    return this.makeRequest<GetProxyResponse>('getproxy', requestParams);
  }

  // 设置代理类型
  async setType(params: SetTypeParams): Promise<SuccessResponse> {
    return this.makeRequest<SuccessResponse>('settype', {
      ids: params.ids,
      type: params.type,
    });
  }

  // 设置代理描述
  async setDescr(params: SetDescrParams): Promise<SetDescrResponse> {
    const requestParams: Record<string, any> = {
      new: params.new,
    };
    
    if (params.old) requestParams.old = params.old;
    if (params.ids) requestParams.ids = params.ids;

    return this.makeRequest<SetDescrResponse>('setdescr', requestParams);
  }

  // 购买代理
  async buyProxy(params: BuyProxyParams): Promise<BuyProxyResponse> {
    const requestParams: Record<string, any> = {
      count: params.count,
      period: params.period,
      country: params.country,
    };

    if (params.version) requestParams.version = params.version;
    if (params.type) requestParams.type = params.type;
    if (params.descr) requestParams.descr = params.descr;
    if (params.auto_prolong) requestParams.auto_prolong = '';
    if (params.nokey) requestParams.nokey = '';

    return this.makeRequest<BuyProxyResponse>('buy', requestParams);
  }

  // 延期代理
  async prolongProxy(params: ProlongProxyParams): Promise<ProlongProxyResponse> {
    const requestParams: Record<string, any> = {
      period: params.period,
      ids: params.ids,
    };

    if (params.nokey) requestParams.nokey = '';

    return this.makeRequest<ProlongProxyResponse>('prolong', requestParams);
  }

  // 删除代理
  async deleteProxy(params: DeleteProxyParams): Promise<DeleteProxyResponse> {
    const requestParams: Record<string, any> = {};
    
    if (params.ids) requestParams.ids = params.ids;
    if (params.descr) requestParams.descr = params.descr;

    return this.makeRequest<DeleteProxyResponse>('delete', requestParams);
  }

  // 检查代理
  async checkProxy(params: CheckProxyParams): Promise<CheckProxyResponse> {
    return this.makeRequest<CheckProxyResponse>('check', {
      ids: params.ids,
    });
  }
}
