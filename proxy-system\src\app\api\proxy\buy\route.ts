import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getUserFromToken, getUserFromApiKey, logApiCall } from '@/lib/auth';
import { ProxyAPIClient } from '@/lib/api-client';

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 认证
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user || !user.apiKey) {
      await logApiCall(
        null,
        'POST',
        '/api/proxy/buy',
        {},
        { error: '未授权访问或未设置API密钥' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问或未设置API密钥' },
        { status: 401 }
      );
    }

    const { count, period, country, version = '6', type = 'http', description } = await request.json();

    // 验证输入
    if (!count || !period || !country) {
      return NextResponse.json(
        { error: '数量、期限和国家都是必填项' },
        { status: 400 }
      );
    }

    if (count < 1 || count > 1000) {
      return NextResponse.json(
        { error: '代理数量必须在1-1000之间' },
        { status: 400 }
      );
    }

    if (period < 1 || period > 365) {
      return NextResponse.json(
        { error: '代理期限必须在1-365天之间' },
        { status: 400 }
      );
    }

    // 创建API客户端
    const proxyClient = new ProxyAPIClient(user.apiKey);

    // 先获取价格
    const priceInfo = await proxyClient.getPrice({
      count,
      period,
      version,
    });

    // 检查用户余额
    if (user.balance < priceInfo.price) {
      return NextResponse.json(
        { error: '余额不足，请先充值' },
        { status: 400 }
      );
    }

    // 购买代理
    const buyResult = await proxyClient.buyProxy({
      count,
      period,
      country,
      version,
      type,
      descr: description,
    });

    // 开始数据库事务
    const result = await db.$transaction(async (tx) => {
      // 扣除用户余额
      await tx.user.update({
        where: { id: user.id },
        data: {
          balance: {
            decrement: buyResult.price,
          },
        },
      });

      // 记录交易
      const transaction = await tx.transaction.create({
        data: {
          userId: user.id,
          type: 'buy',
          amount: -buyResult.price,
          currency: user.currency,
          description: `购买 ${count} 个 ${country} 代理，期限 ${period} 天`,
          status: 'completed',
        },
      });

      // 保存代理信息
      const proxies = [];
      for (const [id, proxyInfo] of Object.entries(buyResult.list)) {
        const proxy = await tx.proxy.create({
          data: {
            userId: user.id,
            externalId: proxyInfo.id,
            ip: proxyInfo.ip,
            host: proxyInfo.host,
            port: proxyInfo.port,
            username: proxyInfo.user,
            password: proxyInfo.pass,
            type: proxyInfo.type,
            version,
            country: proxyInfo.country,
            description: description || '',
            purchaseDate: new Date(proxyInfo.date),
            expiryDate: new Date(proxyInfo.date_end),
          },
        });
        proxies.push(proxy);
      }

      return { transaction, proxies };
    });

    const response = {
      message: '代理购买成功',
      transaction: result.transaction,
      proxies: result.proxies,
      totalCost: buyResult.price,
      remainingBalance: user.balance - buyResult.price,
    };

    await logApiCall(
      user.id,
      'POST',
      '/api/proxy/buy',
      { count, period, country, version, type, description },
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Buy proxy error:', error);
    
    let errorMessage = '购买代理失败';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API Error')) {
        errorMessage = error.message;
        statusCode = 400;
      }
    }

    await logApiCall(
      user?.id || null,
      'POST',
      '/api/proxy/buy',
      {},
      { error: errorMessage },
      statusCode,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
