'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Form, 
  Input, 
  Button, 
  Card, 
  Typography, 
  Space, 
  Alert,
  Layout,
  Avatar,
  Divider,
  Progress,
  List,
  Tooltip
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  SafetyOutlined,
  UserAddOutlined,
  HomeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Content } = Layout;

interface RegisterFormData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
}

// 密码强度验证
const validatePassword = (password: string) => {
  const errors = [];
  if (password.length < 8) errors.push('密码长度至少8位');
  if (!/[A-Z]/.test(password)) errors.push('需要包含大写字母');
  if (!/[a-z]/.test(password)) errors.push('需要包含小写字母');
  if (!/[0-9]/.test(password)) errors.push('需要包含数字');
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) errors.push('需要包含特殊字符');
  return { isValid: errors.length === 0, errors, strength: Math.max(0, 5 - errors.length) };
};

const isValidEmail = (email: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

export default function RegisterPage() {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    errors: [] as string[],
    strength: 0
  });
  const router = useRouter();

  const handleSubmit = async (values: RegisterFormData) => {
    setIsLoading(true);
    setError('');

    // 验证表单
    if (!isValidEmail(values.email)) {
      setError('请输入有效的邮箱地址');
      setIsLoading(false);
      return;
    }

    if (!passwordValidation.isValid) {
      setError('密码不符合要求');
      setIsLoading(false);
      return;
    }

    if (values.password !== values.confirmPassword) {
      setError('两次输入的密码不一致');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: values.email,
          username: values.username,
          password: values.password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token到localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        
        // 跳转到仪表板
        router.push('/dashboard');
      } else {
        setError(data.error || '注册失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value;
    setPasswordValidation(validatePassword(password));
  };

  const getPasswordStrengthColor = (strength: number) => {
    if (strength <= 1) return '#ff4d4f';
    if (strength <= 2) return '#faad14';
    if (strength <= 3) return '#1890ff';
    return '#52c41a';
  };

  const getPasswordStrengthText = (strength: number) => {
    if (strength <= 1) return '弱';
    if (strength <= 2) return '中等';
    if (strength <= 3) return '强';
    return '很强';
  };

  return (
    <Layout style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'
    }}>
      <Content style={{ 
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '24px'
      }}>
        <div style={{ width: '100%', maxWidth: '480px' }}>
          {/* Logo */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Link href="/" style={{ textDecoration: 'none' }}>
              <Space direction="vertical" size="small">
                <Avatar
                  size={64}
                  style={{ backgroundColor: '#2563eb' }}
                  icon={<SafetyOutlined />}
                />
                <div>
                  <Title level={2} style={{ margin: 0, color: '#2563eb' }}>
                    ProxyHub
                  </Title>
                  <Text type="secondary">企业级代理服务平台</Text>
                </div>
              </Space>
            </Link>
          </div>

          {/* Register Form */}
          <Card 
            style={{ 
              boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
              borderRadius: '12px'
            }}
            bodyStyle={{ padding: '32px' }}
          >
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <Title level={3} style={{ marginBottom: '8px' }}>
                用户注册
              </Title>
              <Paragraph type="secondary">
                创建您的账户，开始使用代理服务
              </Paragraph>
            </div>

            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{ marginBottom: '24px' }}
                closable
                onClose={() => setError('')}
              />
            )}

            <Form
              form={form}
              name="register"
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
              autoComplete="off"
            >
              <Form.Item
                label="邮箱地址"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请输入邮箱地址"
                  autoComplete="email"
                />
              </Form.Item>

              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                  { max: 20, message: '用户名最多20个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请输入用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                label={
                  <Space>
                    <span>密码</span>
                    <Tooltip title="密码需要包含大小写字母、数字和特殊字符，长度至少8位">
                      <InfoCircleOutlined style={{ color: '#8c8c8c' }} />
                    </Tooltip>
                  </Space>
                }
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 8, message: '密码至少8个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请输入密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  autoComplete="new-password"
                  onChange={handlePasswordChange}
                />
              </Form.Item>

              {/* 密码强度指示器 */}
              {form.getFieldValue('password') && (
                <div style={{ marginBottom: '24px' }}>
                  <div style={{ marginBottom: '8px' }}>
                    <Space>
                      <Text style={{ fontSize: '12px' }}>密码强度:</Text>
                      <Text 
                        style={{ 
                          fontSize: '12px', 
                          color: getPasswordStrengthColor(passwordValidation.strength),
                          fontWeight: 'bold'
                        }}
                      >
                        {getPasswordStrengthText(passwordValidation.strength)}
                      </Text>
                    </Space>
                  </div>
                  <Progress
                    percent={(passwordValidation.strength / 5) * 100}
                    strokeColor={getPasswordStrengthColor(passwordValidation.strength)}
                    showInfo={false}
                    size="small"
                  />
                  {passwordValidation.errors.length > 0 && (
                    <List
                      size="small"
                      style={{ marginTop: '8px' }}
                      dataSource={passwordValidation.errors}
                      renderItem={(error) => (
                        <List.Item style={{ padding: '2px 0', border: 'none' }}>
                          <Text style={{ fontSize: '12px', color: '#ff4d4f' }}>
                            <CloseCircleOutlined style={{ marginRight: '4px' }} />
                            {error}
                          </Text>
                        </List.Item>
                      )}
                    />
                  )}
                  {passwordValidation.isValid && (
                    <div style={{ marginTop: '8px' }}>
                      <Text style={{ fontSize: '12px', color: '#52c41a' }}>
                        <CheckCircleOutlined style={{ marginRight: '4px' }} />
                        密码强度符合要求
                      </Text>
                    </div>
                  )}
                </div>
              )}

              <Form.Item
                label="确认密码"
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请再次输入密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  autoComplete="new-password"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: '16px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  block
                  icon={<UserAddOutlined />}
                  style={{ height: '48px', fontSize: '16px' }}
                  disabled={!passwordValidation.isValid}
                >
                  {isLoading ? '注册中...' : '注册'}
                </Button>
              </Form.Item>
            </Form>

            <Divider plain>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                其他选项
              </Text>
            </Divider>

            <div style={{ textAlign: 'center' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Text type="secondary">
                  已有账户？{' '}
                  <Link href="/login" style={{ color: '#2563eb', fontWeight: 500 }}>
                    立即登录
                  </Link>
                </Text>
                
                <Link href="/">
                  <Button 
                    type="text" 
                    icon={<HomeOutlined />}
                    style={{ color: '#8c8c8c' }}
                  >
                    返回首页
                  </Button>
                </Link>
              </Space>
            </div>
          </Card>

          {/* Terms Notice */}
          <Card 
            size="small"
            style={{ 
              marginTop: '24px',
              background: '#f6ffed',
              borderColor: '#b7eb8f'
            }}
          >
            <Text style={{ fontSize: '12px', color: '#389e0d' }}>
              <InfoCircleOutlined style={{ marginRight: '4px' }} />
              注册即表示您同意我们的
              <Link href="/terms" style={{ color: '#389e0d', textDecoration: 'underline' }}>
                服务条款
              </Link>
              和
              <Link href="/privacy" style={{ color: '#389e0d', textDecoration: 'underline' }}>
                隐私政策
              </Link>
            </Text>
          </Card>
        </div>
      </Content>
    </Layout>
  );
}
