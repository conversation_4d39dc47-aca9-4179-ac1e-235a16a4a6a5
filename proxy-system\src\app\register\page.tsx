'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// 简单的验证函数
const validatePassword = (password: string) => {
  const errors = [];
  if (password.length < 8) errors.push('密码长度至少8位');
  if (!/[A-Z]/.test(password)) errors.push('需要包含大写字母');
  if (!/[a-z]/.test(password)) errors.push('需要包含小写字母');
  if (!/[0-9]/.test(password)) errors.push('需要包含数字');
  return { isValid: errors.length === 0, errors };
};

const isValidEmail = (email: string) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    errors: [] as string[],
  });
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // 验证表单
    if (!isValidEmail(formData.email)) {
      setError('请输入有效的邮箱地址');
      setIsLoading(false);
      return;
    }

    if (!passwordValidation.isValid) {
      setError('密码不符合要求');
      setIsLoading(false);
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError('两次输入的密码不一致');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          username: formData.username,
          password: formData.password,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token到localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        
        // 跳转到仪表板
        router.push('/dashboard');
      } else {
        setError(data.error || '注册失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // 实时验证密码
    if (name === 'password') {
      setPasswordValidation(validatePassword(value));
    }
  };

  return (
    <div style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #dbeafe 0%, #c7d2fe 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '1rem',
      fontFamily: 'system-ui, sans-serif'
    }}>
      <div style={{ width: '100%', maxWidth: '400px' }}>
        {/* Logo */}
        <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
          <Link href="/" style={{ 
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            textDecoration: 'none',
            color: '#1f2937'
          }}>
            <div style={{ 
              backgroundColor: '#2563eb',
              padding: '0.5rem',
              borderRadius: '0.5rem',
              color: 'white',
              fontSize: '1.5rem'
            }}>
              🛡️
            </div>
            <span style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>ProxyHub</span>
          </Link>
        </div>

        {/* Register Form */}
        <div style={{ 
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '2rem',
          boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <h1 style={{ 
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 0.5rem 0'
            }}>用户注册</h1>
            <p style={{ 
              color: '#6b7280',
              fontSize: '0.875rem',
              margin: 0
            }}>
              创建您的账户，开始使用代理服务
            </p>
          </div>

          <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            {error && (
              <div style={{ 
                padding: '0.75rem',
                fontSize: '0.875rem',
                color: '#dc2626',
                backgroundColor: '#fef2f2',
                border: '1px solid #fecaca',
                borderRadius: '0.375rem'
              }}>
                {error}
              </div>
            )}

            {/* Email */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <label htmlFor="email" style={{ 
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                邮箱地址
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                value={formData.email}
                onChange={handleChange}
                placeholder="请输入邮箱地址"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  fontSize: '0.875rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#2563eb'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* Username */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <label htmlFor="username" style={{ 
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                用户名
              </label>
              <input
                id="username"
                name="username"
                type="text"
                required
                value={formData.username}
                onChange={handleChange}
                placeholder="请输入用户名"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  fontSize: '0.875rem',
                  outline: 'none',
                  transition: 'border-color 0.2s',
                  boxSizing: 'border-box'
                }}
                onFocus={(e) => e.target.style.borderColor = '#2563eb'}
                onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
              />
            </div>

            {/* Password */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              <label htmlFor="password" style={{ 
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151'
              }}>
                密码
              </label>
              <div style={{ position: 'relative' }}>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="请输入密码"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    paddingRight: '2.5rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'border-color 0.2s',
                    boxSizing: 'border-box'
                  }}
                  onFocus={(e) => e.target.style.borderColor = '#2563eb'}
                  onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  style={{
                    position: 'absolute',
                    right: '0.75rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    background: 'none',
                    border: 'none',
                    color: '#9ca3af',
                    cursor: 'pointer',
                    fontSize: '0.875rem'
                  }}
                >
                  {showPassword ? '🙈' : '👁️'}
                </button>
              </div>
              
              {/* 密码强度提示 */}
              {formData.password && (
                <div style={{ fontSize: '0.75rem', display: 'flex', flexDirection: 'column', gap: '0.25rem' }}>
                  {passwordValidation.errors.map((error, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', color: '#dc2626' }}>
                      <span>❌</span>
                      <span>{error}</span>
                    </div>
                  ))}
                  {passwordValidation.isValid && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem', color: '#059669' }}>
                      <span>✅</span>
                      <span>密码强度符合要求</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            <button
              type="submit"
              disabled={isLoading || !passwordValidation.isValid || formData.password !== formData.confirmPassword}
              style={{
                width: '100%',
                padding: '0.75rem',
                backgroundColor: (isLoading || !passwordValidation.isValid || formData.password !== formData.confirmPassword) ? '#9ca3af' : '#2563eb',
                color: 'white',
                border: 'none',
                borderRadius: '0.375rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: (isLoading || !passwordValidation.isValid || formData.password !== formData.confirmPassword) ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s'
              }}
            >
              {isLoading ? '注册中...' : '注册'}
            </button>
          </form>

          <div style={{ 
            marginTop: '1.5rem',
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            gap: '0.5rem'
          }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280' }}>
              已有账户？{' '}
              <Link href="/login" style={{ 
                color: '#2563eb',
                textDecoration: 'none',
                fontWeight: '500'
              }}>
                立即登录
              </Link>
            </p>
            <Link href="/" style={{ 
              fontSize: '0.875rem',
              color: '#9ca3af',
              textDecoration: 'none'
            }}>
              返回首页
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
