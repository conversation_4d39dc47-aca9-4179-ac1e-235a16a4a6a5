"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/__barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!./node_modules/@ant-design/icons/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloudOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,ShieldCheckOutlined,TeamOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('1');\n    // 核心统计数据\n    const stats = [\n        {\n            title: '全球节点',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, this),\n            color: '#3b82f6',\n            description: '覆盖全球主要国家和地区'\n        },\n        {\n            title: '企业用户',\n            value: 10000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 72,\n                columnNumber: 13\n            }, this),\n            color: '#10b981',\n            description: '服务全球企业客户'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__.ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, this),\n            color: '#f59e0b',\n            description: 'SLA 服务等级保证'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 13\n            }, this),\n            color: '#8b5cf6',\n            description: '全天候专业技术支持'\n        }\n    ];\n    // 核心功能特性\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',\n            tags: [\n                'HTTP/HTTPS',\n                'SOCKS5',\n                '高兼容性'\n            ],\n            color: '#3b82f6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 13\n            }, this),\n            title: '全球节点网络',\n            description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',\n            tags: [\n                '50+ 国家',\n                '低延迟',\n                '高速度'\n            ],\n            color: '#10b981'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__.ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，多重安全防护，保障数据传输安全',\n            tags: [\n                '军用级加密',\n                '多重防护',\n                '隐私保护'\n            ],\n            color: '#f59e0b'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 117,\n                columnNumber: 13\n            }, this),\n            title: '智能监控',\n            description: '实时监控系统状态，智能故障检测，确保服务稳定运行',\n            tags: [\n                '实时监控',\n                '智能检测',\n                '自动恢复'\n            ],\n            color: '#8b5cf6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 13\n            }, this),\n            title: 'API 集成',\n            description: '完整的 RESTful API，支持自动化管理和第三方系统集成',\n            tags: [\n                'RESTful API',\n                '自动化',\n                '易集成'\n            ],\n            color: '#ef4444'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 13\n            }, this),\n            title: '灵活配置',\n            description: '支持自定义配置，满足不同业务场景的个性化需求',\n            tags: [\n                '自定义配置',\n                '灵活部署',\n                '场景适配'\n            ],\n            color: '#06b6d4'\n        }\n    ];\n    // 使用步骤\n    const steps = [\n        {\n            title: '注册账户',\n            description: '快速注册，获取专属 API 密钥',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '选择套餐',\n            description: '根据业务需求选择合适的服务套餐',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '配置接入',\n            description: '通过 API 或控制面板配置代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '开始使用',\n            description: '享受稳定高效的全球代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 159,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: '某大型电商平台',\n            industry: '电子商务',\n            content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',\n            avatar: 'E',\n            color: '#3b82f6'\n        },\n        {\n            company: '某金融科技公司',\n            industry: '金融科技',\n            content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',\n            avatar: 'F',\n            color: '#10b981'\n        },\n        {\n            company: '某数据分析公司',\n            industry: '数据分析',\n            content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',\n            avatar: 'D',\n            color: '#f59e0b'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                    padding: '0 24px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000,\n                    borderBottom: '1px solid rgba(0,0,0,0.06)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1400px',\n                            margin: '0 auto',\n                            height: '72px'\n                        },\n                        className: \"jsx-2af30dff10d881d1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '16px'\n                                    },\n                                    className: \"jsx-2af30dff10d881d1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '48px',\n                                                height: '48px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                            },\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                style: {\n                                                    fontSize: '24px',\n                                                    color: '#fff'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 3,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#1e293b',\n                                                        fontSize: '24px',\n                                                        fontWeight: '700',\n                                                        lineHeight: '1'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '13px',\n                                                        color: '#64748b',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Enterprise Proxy Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-2af30dff10d881d1\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"开发者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e2e8f0',\n                                                height: '24px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#475569',\n                                                    fontSize: '15px',\n                                                    height: '40px',\n                                                    borderRadius: '8px'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.target.style.color = '#3b82f6';\n                                                    e.target.style.background = '#f1f5f9';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.target.style.color = '#475569';\n                                                    e.target.style.background = 'transparent';\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                    borderColor: 'transparent',\n                                                    borderRadius: '10px',\n                                                    height: '44px',\n                                                    paddingLeft: '24px',\n                                                    paddingRight: '24px',\n                                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                                                    border: 'none'\n                                                },\n                                                children: \"免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                className: \"jsx-2af30dff10d881d1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-2af30dff10d881d1\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '8px',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none',\n                                            fontSize: '18px',\n                                            width: '44px',\n                                            height: '44px',\n                                            borderRadius: '8px'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"2af30dff10d881d1\",\n                        children: \"@media(min-width:1024px){.desktop-nav.jsx-2af30dff10d881d1{display:block!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:1023px)and (min-width:640px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:639px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            size: 32,\n                            style: {\n                                backgroundColor: '#2563eb'\n                            },\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 21\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: '#2563eb',\n                                fontWeight: 'bold'\n                            },\n                            children: \"ProxyHub\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 280,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '8px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"产品特性\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"价格方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '48px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#374151',\n                                justifyContent: 'flex-start'\n                            },\n                            children: \"帮助文档\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            style: {\n                                margin: '16px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#374151'\n                                },\n                                children: \"登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '48px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: '#2563eb',\n                                    borderColor: '#2563eb'\n                                },\n                                children: \"免费注册\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 434,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%)',\n                            padding: '120px 24px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.4\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '48px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '12px',\n                                                background: 'rgba(255,255,255,0.1)',\n                                                backdropFilter: 'blur(10px)',\n                                                border: '1px solid rgba(255,255,255,0.2)',\n                                                borderRadius: '50px',\n                                                padding: '8px 24px',\n                                                marginBottom: '24px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        borderRadius: '50%',\n                                                        background: '#10b981',\n                                                        boxShadow: '0 0 8px #10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        color: 'rgba(255,255,255,0.9)',\n                                                        fontSize: '14px',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"已服务 100,000+ 企业用户\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(2.5rem, 6vw, 4rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '700',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.02em'\n                                        },\n                                        children: [\n                                            \"全球代理服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#3b82f6',\n                                                    fontWeight: '700'\n                                                },\n                                                children: \"企业级解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.1rem, 2.5vw, 1.3rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '48px',\n                                            maxWidth: '700px',\n                                            margin: '0 auto 48px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"提供高质量的全球代理网络，支持 HTTP/HTTPS 和 SOCKS5 协议\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% 稳定性保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"企业级安全\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: \"large\",\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    style: {\n                                                        height: '56px',\n                                                        fontSize: '16px',\n                                                        fontWeight: '600',\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '12px',\n                                                        paddingLeft: '32px',\n                                                        paddingRight: '32px',\n                                                        boxShadow: '0 8px 25px rgba(59, 130, 246, 0.3)',\n                                                        border: 'none'\n                                                    },\n                                                    children: \"立即免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '56px',\n                                                    fontSize: '16px',\n                                                    fontWeight: '500',\n                                                    background: 'rgba(255,255,255,0.1)',\n                                                    border: '2px solid rgba(255,255,255,0.2)',\n                                                    color: '#ffffff',\n                                                    backdropFilter: 'blur(10px)',\n                                                    borderRadius: '12px',\n                                                    paddingLeft: '32px',\n                                                    paddingRight: '32px'\n                                                },\n                                                children: \"了解更多\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        gutter: [\n                                            32,\n                                            32\n                                        ],\n                                        style: {\n                                            marginBottom: '0'\n                                        },\n                                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    style: {\n                                                        textAlign: 'center',\n                                                        background: 'rgba(255,255,255,0.98)',\n                                                        backdropFilter: 'blur(20px)',\n                                                        border: '1px solid rgba(255,255,255,0.3)',\n                                                        borderRadius: '20px',\n                                                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                                                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                        cursor: 'default',\n                                                        overflow: 'hidden'\n                                                    },\n                                                    bodyStyle: {\n                                                        padding: '32px 24px'\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(-8px)';\n                                                        e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        e.currentTarget.style.transform = 'translateY(0)';\n                                                        e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                marginBottom: '20px',\n                                                                display: 'flex',\n                                                                justifyContent: 'center',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    width: '60px',\n                                                                    height: '60px',\n                                                                    borderRadius: '16px',\n                                                                    background: \"linear-gradient(135deg, \".concat(stat.color, \"15, \").concat(stat.color, \"25)\"),\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    justifyContent: 'center',\n                                                                    border: \"2px solid \".concat(stat.color, \"20\")\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '24px',\n                                                                        color: stat.color\n                                                                    },\n                                                                    children: stat.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 693,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                style: {\n                                                                    color: '#64748b',\n                                                                    fontSize: '14px',\n                                                                    fontWeight: '500',\n                                                                    textTransform: 'uppercase',\n                                                                    letterSpacing: '0.5px'\n                                                                },\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 25\n                                                            }, void 0),\n                                                            value: stat.value,\n                                                            suffix: stat.suffix,\n                                                            valueStyle: {\n                                                                color: '#1e293b',\n                                                                fontWeight: '700',\n                                                                fontSize: '28px',\n                                                                lineHeight: '1.2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 701,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 655,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 752,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 751,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 795,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 811,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 804,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 829,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 831,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 847,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 865,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 864,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 870,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 885,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 876,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 785,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 769,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 898,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_ShieldCheckOutlined_TeamOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 970,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 969,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 979,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 956,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 951,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"R+rp0qcabHV79vV49OYD5uovxls=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});