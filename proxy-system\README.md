# 代理系统 - Proxy Management System

一个基于 Next.js 的专业代理服务器管理系统，集成 px6.me API，提供完整的代理购买、管理和监控功能。

## 🚀 功能特性

### 核心功能
- **用户认证系统** - 注册、登录、JWT认证
- **代理管理** - 购买、查看、延期、删除代理
- **多协议支持** - HTTP/HTTPS 和 SOCKS5 协议
- **全球节点** - 支持多个国家和地区的代理服务器
- **实时监控** - 代理状态监控和使用统计
- **API接口** - 完整的RESTful API和第三方集成

### 技术特性
- **现代化架构** - Next.js 15 + TypeScript + Tailwind CSS
- **数据库支持** - PostgreSQL + Prisma ORM
- **安全认证** - JWT + bcrypt 密码加密
- **响应式设计** - 移动端友好的用户界面
- **API集成** - 完整的 px6.me API 客户端封装

## 🛠️ 技术栈

### 前端
- **Next.js 15** - React 全栈框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Lucide React** - 现代化图标库
- **Headless UI** - 无样式组件库

### 后端
- **Next.js API Routes** - 服务端 API
- **Prisma** - 现代化数据库 ORM
- **PostgreSQL** - 关系型数据库
- **JWT** - JSON Web Token 认证
- **bcryptjs** - 密码加密

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript** - 静态类型检查

## 📦 安装指南

### 环境要求
- Node.js 18.0 或更高版本
- PostgreSQL 12 或更高版本
- npm 或 yarn 包管理器

### 1. 克隆项目
```bash
git clone <repository-url>
cd proxy-system
```

### 2. 安装依赖
```bash
npm install
# 或
yarn install
```

### 3. 环境配置
复制环境变量示例文件并配置：
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置以下变量：
```env
# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/proxy_system"

# JWT密钥
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# NextAuth配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# API配置
API_BASE_URL="https://px6.link/api"

# 环境配置
NODE_ENV="development"
```

### 4. 数据库设置
```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push

# (可选) 查看数据库
npx prisma studio
```

### 5. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🔧 配置说明

### 数据库配置
系统使用 PostgreSQL 作为主数据库，通过 Prisma ORM 进行数据访问。主要数据表包括：

- `users` - 用户信息表
- `proxies` - 代理信息表
- `transactions` - 交易记录表
- `api_logs` - API调用日志表
- `system_configs` - 系统配置表

### API密钥配置
用户注册后会自动生成 API 密钥，用于调用 px6.me API。用户可以在账户设置中重新生成密钥。

### 环境变量说明
- `DATABASE_URL` - PostgreSQL 数据库连接字符串
- `JWT_SECRET` - JWT 令牌签名密钥
- `NEXTAUTH_SECRET` - NextAuth 会话密钥
- `API_BASE_URL` - px6.me API 基础URL

## 📚 API 文档

### 认证接口

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "login": "<EMAIL>",
  "password": "password123"
}
```

#### 获取用户信息
```http
GET /api/auth/me
Authorization: Bearer <token>
```

### 代理管理接口

#### 获取代理列表
```http
GET /api/proxy/list?page=1&limit=20&status=active
Authorization: Bearer <token>
```

#### 购买代理
```http
POST /api/proxy/buy
Authorization: Bearer <token>
Content-Type: application/json

{
  "count": 5,
  "period": 30,
  "country": "ru",
  "version": "6",
  "type": "http",
  "description": "测试代理"
}
```

#### 延期代理
```http
POST /api/proxy/prolong
Authorization: Bearer <token>
Content-Type: application/json

{
  "proxyIds": ["proxy-id-1", "proxy-id-2"],
  "period": 30
}
```

#### 删除代理
```http
DELETE /api/proxy/delete
Authorization: Bearer <token>
Content-Type: application/json

{
  "proxyIds": ["proxy-id-1", "proxy-id-2"]
}
```

### 查询接口

#### 获取价格信息
```http
GET /api/proxy/price?count=5&period=30&version=6
Authorization: Bearer <token>
```

#### 获取可用国家
```http
GET /api/proxy/countries?version=6
Authorization: Bearer <token>
```

## 🎨 用户界面

### 主要页面
- **首页** - 产品介绍和功能展示
- **登录/注册** - 用户认证页面
- **仪表板** - 用户主控制面板
- **代理列表** - 代理管理和监控
- **购买代理** - 代理购买配置
- **交易记录** - 历史交易查看
- **账户设置** - 用户设置和API密钥管理

### 设计特点
- **响应式设计** - 适配桌面端和移动端
- **现代化UI** - 简洁美观的用户界面
- **交互友好** - 直观的操作流程
- **状态反馈** - 实时的操作状态提示

---

**代理系统** - 专业的代理服务器管理平台 🚀
