# ProxyHub 代理商系统设置指南

## 🚀 快速开始

### 1. 获取 px6.me API 密钥
1. 访问 [px6.me](https://px6.me/developers)
2. 注册代理商账户
3. 获取您的 API 密钥

### 2. 配置环境变量
编辑 `.env.local` 文件，添加您的 px6.me API 密钥：

```env
# 您的 px6.me API 密钥（重要！）
PX6_API_KEY="your-actual-px6-api-key-here"

# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/proxy_system"

# JWT密钥（请更改为随机字符串）
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"

# NextAuth配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# API配置
API_BASE_URL="https://px6.link/api"

# 环境配置
NODE_ENV="development"
```

### 3. 启动系统
```bash
# 安装依赖
npm install

# 生成数据库客户端
npx prisma generate

# 推送数据库架构
npx prisma db push

# 启动开发服务器
npm run dev
```

### 4. 访问系统
- 前端网站: http://localhost:3000
- 数据库管理: `npx prisma studio`

## 💰 定价策略配置

### 默认定价规则
- **基础价格**: 2.5元/代理/天
- **加价比例**: 40%
- **IPv4加价**: 20%

### 数量折扣
- 10个以上: 5%折扣
- 50个以上: 10%折扣  
- 100个以上: 15%折扣

### VIP等级折扣
- 基础用户: 0%
- 专业用户: 5%
- 企业用户: 10%

### 修改定价
1. 编辑 `src/lib/pricing.ts` 文件
2. 修改 `defaultPricingConfig` 对象
3. 重启服务器应用更改

## 🎨 界面定制

### 品牌信息
在 `src/app/page.tsx` 中修改：
- 网站名称: "ProxyHub"
- 标语和描述
- 联系信息

### 颜色主题
在 `src/app/globals.css` 中修改CSS变量：
```css
:root {
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  /* 其他颜色变量 */
}
```

### Logo和图标
- 替换 `public/` 目录下的图标文件
- 修改 `src/app/layout.tsx` 中的元数据

## 📊 业务管理

### 用户管理
- 用户注册自动创建账户
- 余额管理通过数据库操作
- 可以手动调整用户余额和等级

### 订单管理
- 所有交易记录保存在 `transactions` 表
- 代理信息保存在 `proxies` 表
- API调用日志保存在 `api_logs` 表

### 财务统计
```sql
-- 查看总收入
SELECT SUM(ABS(amount)) FROM transactions WHERE type = 'buy';

-- 查看用户余额总计
SELECT SUM(balance) FROM users;

-- 查看活跃代理数量
SELECT COUNT(*) FROM proxies WHERE is_active = true;
```

## 🔧 高级配置

### 数据库优化
```sql
-- 创建索引提升性能
CREATE INDEX idx_proxies_user_expiry ON proxies(user_id, expiry_date);
CREATE INDEX idx_transactions_user_date ON transactions(user_id, created_at);
CREATE INDEX idx_api_logs_date ON api_logs(created_at);
```

### 缓存配置
可以添加 Redis 缓存来提升性能：
```bash
npm install redis @types/redis
```

### 监控和日志
- 使用 PM2 进行进程管理
- 配置日志轮转
- 设置性能监控

## 🚀 生产环境部署

### 1. 环境准备
```bash
# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装 PM2
sudo npm install -g pm2
```

### 2. 数据库设置
```bash
sudo -u postgres psql
CREATE DATABASE proxy_system;
CREATE USER proxy_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE proxy_system TO proxy_user;
\q
```

### 3. 应用部署
```bash
# 克隆代码
git clone <your-repo>
cd proxy-system

# 安装依赖
npm ci --only=production

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 设置生产环境变量

# 构建应用
npm run build

# 启动应用
pm2 start npm --name "proxy-system" -- start
pm2 save
pm2 startup
```

### 4. Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 5. SSL 证书
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 📈 营销建议

### 定价策略
- 提供免费试用吸引用户
- 设置合理的数量折扣
- 为长期用户提供优惠

### 用户获取
- SEO优化网站内容
- 社交媒体推广
- 技术博客和教程
- 合作伙伴推荐

### 客户服务
- 24/7技术支持
- 详细的使用文档
- 快速响应用户问题
- 定期更新和改进

## 🔒 安全注意事项

### API密钥安全
- 定期更换 px6.me API 密钥
- 不要在代码中硬编码密钥
- 使用环境变量管理敏感信息

### 数据安全
- 定期备份数据库
- 使用强密码
- 启用防火墙
- 监控异常访问

### 用户隐私
- 遵守数据保护法规
- 不记录用户敏感信息
- 提供数据删除选项

## 📞 技术支持

如果您在设置过程中遇到问题：

1. 检查环境变量配置
2. 确认数据库连接正常
3. 验证 px6.me API 密钥有效
4. 查看应用日志排查错误
5. 参考项目文档和API文档

---

**祝您的代理服务平台运营成功！** 🎉
