"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/divider/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/drawer/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Divider,Drawer,Layout,Row,Space,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CloudOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DashboardOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SettingOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/RocketOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/MenuOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/TrophyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,CheckOutlined,CloudOutlined,DashboardOutlined,EyeOutlined,GlobalOutlined,LockOutlined,MenuOutlined,PlayCircleOutlined,RocketOutlined,SafetyOutlined,SettingOutlined,TeamOutlined,ThunderboltOutlined,TrophyOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nfunction Home() {\n    _s();\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('1');\n    // 核心统计数据\n    const stats = [\n        {\n            title: '全球节点',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 13\n            }, this),\n            color: '#3b82f6',\n            description: '覆盖全球主要国家和地区'\n        },\n        {\n            title: '企业用户',\n            value: 10000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 13\n            }, this),\n            color: '#10b981',\n            description: '服务全球企业客户'\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 13\n            }, this),\n            color: '#f59e0b',\n            description: 'SLA 服务等级保证'\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, this),\n            color: '#8b5cf6',\n            description: '全天候专业技术支持'\n        }\n    ];\n    // 核心功能特性\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 13\n            }, this),\n            title: '多协议支持',\n            description: '支持 HTTP/HTTPS、SOCKS5 等多种协议，满足不同业务场景需求',\n            tags: [\n                'HTTP/HTTPS',\n                'SOCKS5',\n                '高兼容性'\n            ],\n            color: '#3b82f6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 13\n            }, this),\n            title: '全球节点网络',\n            description: '覆盖 50+ 个国家和地区的高质量节点，确保最佳连接体验',\n            tags: [\n                '50+ 国家',\n                '低延迟',\n                '高速度'\n            ],\n            color: '#10b981'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShieldCheckOutlined, {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, this),\n            title: '企业级安全',\n            description: '采用军用级加密技术，多重安全防护，保障数据传输安全',\n            tags: [\n                '军用级加密',\n                '多重防护',\n                '隐私保护'\n            ],\n            color: '#f59e0b'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 13\n            }, this),\n            title: '智能监控',\n            description: '实时监控系统状态，智能故障检测，确保服务稳定运行',\n            tags: [\n                '实时监控',\n                '智能检测',\n                '自动恢复'\n            ],\n            color: '#8b5cf6'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 13\n            }, this),\n            title: 'API 集成',\n            description: '完整的 RESTful API，支持自动化管理和第三方系统集成',\n            tags: [\n                'RESTful API',\n                '自动化',\n                '易集成'\n            ],\n            color: '#ef4444'\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 13\n            }, this),\n            title: '灵活配置',\n            description: '支持自定义配置，满足不同业务场景的个性化需求',\n            tags: [\n                '自定义配置',\n                '灵活部署',\n                '场景适配'\n            ],\n            color: '#06b6d4'\n        }\n    ];\n    // 使用步骤\n    const steps = [\n        {\n            title: '注册账户',\n            description: '快速注册，获取专属 API 密钥',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '选择套餐',\n            description: '根据业务需求选择合适的服务套餐',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '配置接入',\n            description: '通过 API 或控制面板配置代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 153,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            title: '开始使用',\n            description: '享受稳定高效的全球代理服务',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    // 客户案例\n    const testimonials = [\n        {\n            company: '某大型电商平台',\n            industry: '电子商务',\n            content: '使用 ProxyHub 后，我们的全球业务数据采集效率提升了 300%，服务稳定性达到了企业级标准。',\n            avatar: 'E',\n            color: '#3b82f6'\n        },\n        {\n            company: '某金融科技公司',\n            industry: '金融科技',\n            content: 'ProxyHub 的安全性和稳定性完全满足我们的合规要求，是值得信赖的企业级服务商。',\n            avatar: 'F',\n            color: '#10b981'\n        },\n        {\n            company: '某数据分析公司',\n            industry: '数据分析',\n            content: '24/7 的技术支持和 99.9% 的稳定性保证，让我们的业务运行更加安心。',\n            avatar: 'D',\n            color: '#f59e0b'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                    padding: '0 24px',\n                    position: 'sticky',\n                    top: 0,\n                    zIndex: 1000,\n                    borderBottom: '1px solid rgba(0,0,0,0.06)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            maxWidth: '1400px',\n                            margin: '0 auto',\n                            height: '72px'\n                        },\n                        className: \"jsx-2af30dff10d881d1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/\",\n                                style: {\n                                    textDecoration: 'none'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '16px'\n                                    },\n                                    className: \"jsx-2af30dff10d881d1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '48px',\n                                                height: '48px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                            },\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                style: {\n                                                    fontSize: '24px',\n                                                    color: '#fff'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2af30dff10d881d1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                    level: 3,\n                                                    style: {\n                                                        margin: 0,\n                                                        color: '#1e293b',\n                                                        fontSize: '24px',\n                                                        fontWeight: '700',\n                                                        lineHeight: '1'\n                                                    },\n                                                    children: \"ProxyHub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    style: {\n                                                        fontSize: '13px',\n                                                        color: '#64748b',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Enterprise Proxy Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'none'\n                                },\n                                className: \"jsx-2af30dff10d881d1\" + \" \" + \"desktop-nav\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"产品特性\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"价格方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            type: \"text\",\n                                            style: {\n                                                fontWeight: '500',\n                                                color: '#475569',\n                                                fontSize: '15px',\n                                                height: '40px',\n                                                borderRadius: '8px'\n                                            },\n                                            onMouseEnter: (e)=>{\n                                                e.target.style.color = '#3b82f6';\n                                                e.target.style.background = '#f1f5f9';\n                                            },\n                                            onMouseLeave: (e)=>{\n                                                e.target.style.color = '#475569';\n                                                e.target.style.background = 'transparent';\n                                            },\n                                            children: \"开发者\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            type: \"vertical\",\n                                            style: {\n                                                borderColor: '#e2e8f0',\n                                                height: '24px'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/login\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                type: \"text\",\n                                                style: {\n                                                    fontWeight: '500',\n                                                    color: '#475569',\n                                                    fontSize: '15px',\n                                                    height: '40px',\n                                                    borderRadius: '8px'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.target.style.color = '#3b82f6';\n                                                    e.target.style.background = '#f1f5f9';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.target.style.color = '#475569';\n                                                    e.target.style.background = 'transparent';\n                                                },\n                                                children: \"登录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                type: \"primary\",\n                                                style: {\n                                                    fontWeight: '600',\n                                                    fontSize: '15px',\n                                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                    borderColor: 'transparent',\n                                                    borderRadius: '10px',\n                                                    height: '44px',\n                                                    paddingLeft: '24px',\n                                                    paddingRight: '24px',\n                                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                                                    border: 'none'\n                                                },\n                                                children: \"免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                className: \"jsx-2af30dff10d881d1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '8px'\n                                        },\n                                        className: \"jsx-2af30dff10d881d1\" + \" \" + \"mobile-nav\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"text\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"登录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"middle\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        borderColor: '#3b82f6',\n                                                        borderRadius: '8px',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        type: \"text\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setMobileMenuOpen(true),\n                                        style: {\n                                            display: 'none',\n                                            fontSize: '18px',\n                                            width: '44px',\n                                            height: '44px',\n                                            borderRadius: '8px'\n                                        },\n                                        className: \"mobile-menu-btn\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        id: \"2af30dff10d881d1\",\n                        children: \"@media(min-width:1024px){.desktop-nav.jsx-2af30dff10d881d1{display:block!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:1023px)and (min-width:640px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:none!important}}@media(max-width:639px){.desktop-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-nav.jsx-2af30dff10d881d1{display:none!important}.mobile-menu-btn.jsx-2af30dff10d881d1{display:-webkit-inline-box!important;display:-webkit-inline-flex!important;display:-moz-inline-box!important;display:-ms-inline-flexbox!important;display:inline-flex!important}}\"\n                    }, void 0, false, void 0, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '16px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                width: '40px',\n                                height: '40px',\n                                borderRadius: '10px',\n                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                style: {\n                                    fontSize: '20px',\n                                    color: '#fff'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#1e293b',\n                                        fontWeight: '700',\n                                        fontSize: '18px'\n                                    },\n                                    children: \"ProxyHub\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        color: '#64748b',\n                                        fontSize: '12px'\n                                    },\n                                    children: \"Enterprise Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, void 0),\n                placement: \"right\",\n                onClose: ()=>setMobileMenuOpen(false),\n                open: mobileMenuOpen,\n                width: 320,\n                styles: {\n                    body: {\n                        padding: '24px'\n                    },\n                    header: {\n                        borderBottom: '1px solid #f1f5f9',\n                        paddingBottom: '16px'\n                    }\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        gap: '12px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"导航菜单\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#3b82f6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                \"产品特性\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#10b981'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, this),\n                                \"解决方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#f59e0b'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                \"价格方案\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            type: \"text\",\n                            block: true,\n                            style: {\n                                textAlign: 'left',\n                                height: '52px',\n                                fontSize: '16px',\n                                fontWeight: '500',\n                                color: '#475569',\n                                justifyContent: 'flex-start',\n                                borderRadius: '12px',\n                                border: '1px solid transparent'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.background = '#f8fafc';\n                                e.target.style.borderColor = '#e2e8f0';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.background = 'transparent';\n                                e.target.style.borderColor = 'transparent';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    style: {\n                                        marginRight: '12px',\n                                        color: '#8b5cf6'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                \"开发者\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            style: {\n                                margin: '24px 0'\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginBottom: '16px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                style: {\n                                    fontSize: '14px',\n                                    color: '#64748b',\n                                    fontWeight: '500'\n                                },\n                                children: \"账户操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/login\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"text\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '500',\n                                    color: '#475569',\n                                    borderRadius: '12px',\n                                    border: '1px solid #e2e8f0'\n                                },\n                                children: \"登录账户\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/register\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                type: \"primary\",\n                                block: true,\n                                style: {\n                                    height: '52px',\n                                    fontSize: '16px',\n                                    fontWeight: '600',\n                                    background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                    borderColor: 'transparent',\n                                    borderRadius: '12px',\n                                    marginTop: '8px',\n                                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)'\n                                },\n                                children: \"免费试用\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',\n                            padding: '140px 24px 120px',\n                            textAlign: 'center',\n                            position: 'relative',\n                            overflow: 'hidden'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: 0,\n                                    left: 0,\n                                    right: 0,\n                                    bottom: 0,\n                                    backgroundImage: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n                                    opacity: 0.6\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '20%',\n                                    left: '10%',\n                                    width: '300px',\n                                    height: '300px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(40px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'absolute',\n                                    top: '60%',\n                                    right: '10%',\n                                    width: '200px',\n                                    height: '200px',\n                                    borderRadius: '50%',\n                                    background: 'radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)',\n                                    filter: 'blur(30px)'\n                                }\n                            }, void 0, false, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    maxWidth: '1200px',\n                                    margin: '0 auto',\n                                    position: 'relative',\n                                    zIndex: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '56px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'inline-flex',\n                                                alignItems: 'center',\n                                                gap: '16px',\n                                                background: 'rgba(255,255,255,0.08)',\n                                                backdropFilter: 'blur(20px)',\n                                                border: '1px solid rgba(255,255,255,0.12)',\n                                                borderRadius: '60px',\n                                                padding: '12px 32px',\n                                                marginBottom: '32px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: '10px',\n                                                                height: '10px',\n                                                                borderRadius: '50%',\n                                                                background: '#10b981',\n                                                                boxShadow: '0 0 12px #10b981',\n                                                                animation: 'pulse 2s infinite'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 684,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"服务 10,000+ 企业客户\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 679,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    type: \"vertical\",\n                                                    style: {\n                                                        borderColor: 'rgba(255,255,255,0.2)',\n                                                        height: '16px'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 700,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        gap: '8px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            style: {\n                                                                color: '#f59e0b',\n                                                                fontSize: '16px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                            style: {\n                                                                color: 'rgba(255,255,255,0.9)',\n                                                                fontSize: '15px',\n                                                                fontWeight: '600'\n                                                            },\n                                                            children: \"行业领先\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 668,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                        level: 1,\n                                        style: {\n                                            fontSize: 'clamp(3rem, 8vw, 5rem)',\n                                            marginBottom: '32px',\n                                            color: '#ffffff',\n                                            fontWeight: '800',\n                                            lineHeight: '1.1',\n                                            letterSpacing: '-0.03em',\n                                            textShadow: '0 4px 20px rgba(0,0,0,0.3)'\n                                        },\n                                        children: [\n                                            \"企业级全球代理\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #3b82f6, #06b6d4)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    backgroundClip: 'text',\n                                                    fontWeight: '800'\n                                                },\n                                                children: \"解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                        style: {\n                                            fontSize: 'clamp(1.2rem, 3vw, 1.5rem)',\n                                            color: 'rgba(255,255,255,0.8)',\n                                            marginBottom: '56px',\n                                            maxWidth: '800px',\n                                            margin: '0 auto 56px auto',\n                                            lineHeight: '1.6',\n                                            fontWeight: '400'\n                                        },\n                                        children: [\n                                            \"为企业提供稳定、安全、高速的全球代理网络服务\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"99.9% SLA 保证\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#34d399',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"50+ 国家节点\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" •\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontWeight: '600'\n                                                },\n                                                children: \"24/7 技术支持\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 755,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '80px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: \"large\",\n                                                wrap: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                        href: \"/register\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            type: \"primary\",\n                                                            size: \"large\",\n                                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 27\n                                                            }, void 0),\n                                                            style: {\n                                                                height: '64px',\n                                                                fontSize: '18px',\n                                                                fontWeight: '700',\n                                                                background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                                                                borderColor: 'transparent',\n                                                                borderRadius: '16px',\n                                                                paddingLeft: '40px',\n                                                                paddingRight: '40px',\n                                                                boxShadow: '0 12px 32px rgba(59, 130, 246, 0.4)',\n                                                                border: 'none',\n                                                                minWidth: '200px'\n                                                            },\n                                                            children: \"立即免费试用\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 761,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        size: \"large\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 785,\n                                                            columnNumber: 25\n                                                        }, void 0),\n                                                        style: {\n                                                            height: '64px',\n                                                            fontSize: '18px',\n                                                            fontWeight: '600',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            border: '2px solid rgba(255,255,255,0.16)',\n                                                            color: '#ffffff',\n                                                            backdropFilter: 'blur(20px)',\n                                                            borderRadius: '16px',\n                                                            paddingLeft: '40px',\n                                                            paddingRight: '40px',\n                                                            minWidth: '180px'\n                                                        },\n                                                        children: \"观看演示\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '48px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                        style: {\n                                                            color: 'rgba(255,255,255,0.6)',\n                                                            fontSize: '14px',\n                                                            display: 'block',\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: \"已有数千家企业选择我们\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: \"large\",\n                                                        wrap: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"10K+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"企业用户\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 819,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 815,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"50+\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 824,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"国家节点\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 823,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: '#ffffff',\n                                                                            fontSize: '24px',\n                                                                            fontWeight: '700',\n                                                                            display: 'block'\n                                                                        },\n                                                                        children: \"99.9%\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 832,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '12px'\n                                                                        },\n                                                                        children: \"稳定性\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: 'rgba(255,255,255,0.05)',\n                                            backdropFilter: 'blur(20px)',\n                                            border: '1px solid rgba(255,255,255,0.1)',\n                                            borderRadius: '32px',\n                                            padding: '48px 32px',\n                                            marginBottom: '0'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            gutter: [\n                                                32,\n                                                32\n                                            ],\n                                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    xs: 12,\n                                                    lg: 6,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            textAlign: 'center',\n                                                            background: 'rgba(255,255,255,0.08)',\n                                                            backdropFilter: 'blur(20px)',\n                                                            border: '1px solid rgba(255,255,255,0.12)',\n                                                            borderRadius: '24px',\n                                                            padding: '40px 24px',\n                                                            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                            cursor: 'default',\n                                                            position: 'relative',\n                                                            overflow: 'hidden'\n                                                        },\n                                                        onMouseEnter: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.12)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.2)';\n                                                        },\n                                                        onMouseLeave: (e)=>{\n                                                            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                                            e.currentTarget.style.background = 'rgba(255,255,255,0.08)';\n                                                            e.currentTarget.style.borderColor = 'rgba(255,255,255,0.12)';\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'absolute',\n                                                                    top: '50%',\n                                                                    left: '50%',\n                                                                    transform: 'translate(-50%, -50%)',\n                                                                    width: '120px',\n                                                                    height: '120px',\n                                                                    borderRadius: '50%',\n                                                                    background: \"radial-gradient(circle, \".concat(stat.color, \"15 0%, transparent 70%)\"),\n                                                                    filter: 'blur(20px)',\n                                                                    zIndex: 0\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    position: 'relative',\n                                                                    zIndex: 1\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '24px',\n                                                                            display: 'flex',\n                                                                            justifyContent: 'center',\n                                                                            alignItems: 'center'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                width: '72px',\n                                                                                height: '72px',\n                                                                                borderRadius: '20px',\n                                                                                background: \"linear-gradient(135deg, \".concat(stat.color, \"20, \").concat(stat.color, \"30)\"),\n                                                                                display: 'flex',\n                                                                                alignItems: 'center',\n                                                                                justifyContent: 'center',\n                                                                                border: \"2px solid \".concat(stat.color, \"40\"),\n                                                                                boxShadow: \"0 8px 32px \".concat(stat.color, \"20\")\n                                                                            },\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    fontSize: '32px',\n                                                                                    color: stat.color,\n                                                                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n                                                                                },\n                                                                                children: stat.icon\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 912,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 901,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 895,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '12px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: '#ffffff',\n                                                                                fontSize: '36px',\n                                                                                fontWeight: '800',\n                                                                                lineHeight: '1',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: [\n                                                                                stat.value,\n                                                                                stat.suffix\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 924,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            marginBottom: '8px'\n                                                                        },\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                            style: {\n                                                                                color: 'rgba(255,255,255,0.9)',\n                                                                                fontSize: '16px',\n                                                                                fontWeight: '600',\n                                                                                display: 'block'\n                                                                            },\n                                                                            children: stat.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 937,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 936,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                                        style: {\n                                                                            color: 'rgba(255,255,255,0.6)',\n                                                                            fontSize: '13px',\n                                                                            lineHeight: '1.4'\n                                                                        },\n                                                                        children: stat.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 855,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    type: \"primary\",\n                                                    size: \"large\",\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 968,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    style: {\n                                                        height: '48px',\n                                                        padding: '0 32px',\n                                                        fontSize: '16px'\n                                                    },\n                                                    children: \"立即开始免费试用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 965,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 964,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"观看演示\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 963,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '32px'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: \"large\",\n                                            wrap: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"无需信用卡\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"7天免费试用\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                    type: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            style: {\n                                                                color: '#059669',\n                                                                marginRight: '8px'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"随时取消\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1028,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1027,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1022,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1046,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1045,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1051,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1082,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1087,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1100,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1099,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1102,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1117,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1020,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1005,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1004,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1167,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Divider_Drawer_Layout_Row_Space_Typography_antd__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_CheckOutlined_CloudOutlined_DashboardOutlined_EyeOutlined_GlobalOutlined_LockOutlined_MenuOutlined_PlayCircleOutlined_RocketOutlined_SafetyOutlined_SettingOutlined_TeamOutlined_ThunderboltOutlined_TrophyOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1202,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 1218,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 1191,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 1186,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"R+rp0qcabHV79vV49OYD5uovxls=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});