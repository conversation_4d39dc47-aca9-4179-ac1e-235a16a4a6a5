# 开发指南

本文档为开发者提供详细的开发环境设置、代码规范、架构说明和贡献指南。

## 开发环境设置

### 前置要求
- Node.js 18.0+
- PostgreSQL 12+
- Git
- VS Code (推荐)

### 推荐的 VS Code 扩展
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "prisma.prisma",
    "ms-vscode.vscode-json"
  ]
}
```

### 环境配置
1. **克隆仓库**
```bash
git clone <repository-url>
cd proxy-system
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
```

4. **设置数据库**
```bash
# 创建数据库
createdb proxy_system_dev

# 生成 Prisma 客户端
npx prisma generate

# 推送数据库架构
npx prisma db push

# (可选) 查看数据库
npx prisma studio
```

5. **启动开发服务器**
```bash
npm run dev
```

## 项目架构

### 目录结构
```
proxy-system/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 路由
│   │   ├── dashboard/         # 仪表板页面
│   │   ├── login/             # 登录页面
│   │   ├── register/          # 注册页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React 组件
│   │   └── ui/               # UI 组件库
│   ├── lib/                   # 工具库
│   │   ├── api-client.ts     # API 客户端
│   │   ├── auth.ts           # 认证工具
│   │   ├── db.ts             # 数据库连接
│   │   └── utils.ts          # 通用工具
│   ├── types/                 # TypeScript 类型定义
│   │   └── api.ts            # API 类型
│   └── middleware.ts          # Next.js 中间件
├── prisma/
│   └── schema.prisma          # 数据库架构
├── docs/                      # 文档
├── public/                    # 静态资源
├── .env.example              # 环境变量示例
├── .env.local                # 本地环境变量
├── next.config.js            # Next.js 配置
├── tailwind.config.ts        # Tailwind CSS 配置
├── tsconfig.json             # TypeScript 配置
└── package.json              # 项目配置
```

### 技术栈说明

#### 前端
- **Next.js 15**: 使用 App Router 进行路由管理
- **TypeScript**: 提供类型安全
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库
- **React Hook Form**: 表单处理

#### 后端
- **Next.js API Routes**: 服务端 API
- **Prisma**: 数据库 ORM
- **JWT**: 身份认证
- **bcryptjs**: 密码加密

#### 数据库
- **PostgreSQL**: 主数据库
- **Prisma**: ORM 和迁移工具

## 代码规范

### TypeScript 规范
```typescript
// 使用接口定义类型
interface User {
  id: string;
  email: string;
  username: string;
}

// 使用泛型
interface ApiResponse<T> {
  data: T;
  message: string;
}

// 使用枚举
enum ProxyStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  EXPIRING = 'expiring'
}
```

### React 组件规范
```tsx
// 使用函数组件和 TypeScript
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

export const Button: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary' 
}) => {
  return (
    <button 
      className={`btn btn-${variant}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
};
```

### API 路由规范
```typescript
// API 路由结构
import { NextRequest, NextResponse } from 'next/server';
import { getUserFromToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // 认证检查
    const user = await getUserFromToken(
      request.headers.get('authorization')
    );
    
    if (!user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 业务逻辑
    const data = await fetchData(user.id);

    return NextResponse.json({ data });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: '服务器错误' },
      { status: 500 }
    );
  }
}
```

### 数据库规范
```prisma
// Prisma 模型定义
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  proxies Proxy[]

  @@map("users")
}
```

## 开发工作流

### Git 工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交更改
git add .
git commit -m "feat: add new feature"

# 推送分支
git push origin feature/new-feature

# 创建 Pull Request
```

### 提交信息规范
使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
feat: 新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 代码审查清单
- [ ] 代码符合项目规范
- [ ] 包含适当的错误处理
- [ ] 添加了必要的注释
- [ ] 更新了相关文档
- [ ] 通过了所有测试
- [ ] 没有安全漏洞

## 测试

### 单元测试
```typescript
// __tests__/utils.test.ts
import { formatPrice, validatePassword } from '@/lib/utils';

describe('Utils', () => {
  test('formatPrice should format price correctly', () => {
    expect(formatPrice(100.5, 'RUB')).toBe('100.50 ₽');
    expect(formatPrice(50, 'USD')).toBe('50.00 $');
  });

  test('validatePassword should validate password strength', () => {
    const result = validatePassword('Password123');
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });
});
```

### API 测试
```typescript
// __tests__/api/auth.test.ts
import { POST } from '@/app/api/auth/login/route';
import { NextRequest } from 'next/server';

describe('/api/auth/login', () => {
  test('should login with valid credentials', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        login: '<EMAIL>',
        password: 'password123'
      })
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.token).toBeDefined();
  });
});
```

### 运行测试
```bash
# 运行所有测试
npm test

# 运行特定测试
npm test -- --testNamePattern="login"

# 生成覆盖率报告
npm test -- --coverage
```

## 调试

### 开发工具
```typescript
// 使用 console.log 进行调试
console.log('Debug info:', { user, data });

// 使用 debugger 断点
debugger;

// 使用 VS Code 调试器
// 在 .vscode/launch.json 中配置
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### 数据库调试
```bash
# 查看数据库内容
npx prisma studio

# 重置数据库
npx prisma db push --force-reset

# 查看生成的 SQL
npx prisma db push --preview-feature
```

## 性能优化

### 前端优化
```typescript
// 使用 React.memo 优化组件
export const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{data}</div>;
});

// 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 使用 useCallback 缓存函数
const handleClick = useCallback(() => {
  onClick(id);
}, [onClick, id]);
```

### 后端优化
```typescript
// 数据库查询优化
const users = await db.user.findMany({
  select: {
    id: true,
    email: true,
    username: true,
    // 只选择需要的字段
  },
  where: {
    isActive: true,
  },
  take: 20, // 限制结果数量
});

// 使用事务
const result = await db.$transaction(async (tx) => {
  const user = await tx.user.update({...});
  const proxy = await tx.proxy.create({...});
  return { user, proxy };
});
```

## 安全最佳实践

### 输入验证
```typescript
// 使用 Zod 进行输入验证
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

export async function POST(request: NextRequest) {
  const body = await request.json();
  
  try {
    const { email, password } = loginSchema.parse(body);
    // 处理验证后的数据
  } catch (error) {
    return NextResponse.json(
      { error: '输入数据无效' },
      { status: 400 }
    );
  }
}
```

### 认证和授权
```typescript
// 中间件认证
export async function middleware(request: NextRequest) {
  const token = request.headers.get('authorization');
  
  if (!token) {
    return NextResponse.redirect('/login');
  }
  
  const user = await verifyToken(token);
  if (!user) {
    return NextResponse.redirect('/login');
  }
  
  return NextResponse.next();
}
```

### 数据脱敏
```typescript
// 移除敏感信息
const sanitizeUser = (user: User) => {
  const { password, ...safeUser } = user;
  return safeUser;
};
```

## 部署和 CI/CD

### GitHub Actions
```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test
    
    - name: Build
      run: npm run build
```

## 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request
5. 代码审查
6. 合并到主分支

### 问题报告
使用 GitHub Issues 报告问题，包含：
- 问题描述
- 重现步骤
- 期望行为
- 实际行为
- 环境信息

### 功能请求
提交功能请求时，请包含：
- 功能描述
- 使用场景
- 预期收益
- 实现建议

## 常见问题

### Q: 如何添加新的 API 端点？
A: 在 `src/app/api/` 目录下创建新的路由文件，遵循 Next.js App Router 规范。

### Q: 如何修改数据库架构？
A: 修改 `prisma/schema.prisma` 文件，然后运行 `npx prisma db push`。

### Q: 如何添加新的 UI 组件？
A: 在 `src/components/ui/` 目录下创建新组件，遵循项目的组件规范。

### Q: 如何处理环境变量？
A: 在 `.env.local` 文件中添加变量，在代码中使用 `process.env.VARIABLE_NAME` 访问。

## 资源链接

- [Next.js 文档](https://nextjs.org/docs)
- [Prisma 文档](https://www.prisma.io/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 文档](https://www.typescriptlang.org/docs)
- [React 文档](https://react.dev)
