'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ShoppingCart, Globe, Server, Calculator } from 'lucide-react';

interface Country {
  code: string;
  name: string;
  availableCount: number;
}

interface PriceInfo {
  totalPrice: number;
  pricePerProxy: number;
  canAfford: boolean;
}

export default function BuyProxyPage() {
  const [countries, setCountries] = useState<Country[]>([]);
  const [formData, setFormData] = useState({
    count: 1,
    period: 30,
    country: '',
    version: '6',
    type: 'http',
    description: '',
  });
  const [priceInfo, setPriceInfo] = useState<PriceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [userBalance, setUserBalance] = useState(0);

  useEffect(() => {
    fetchCountries();
  }, []);

  useEffect(() => {
    if (formData.count && formData.period) {
      fetchPrice();
    }
  }, [formData.count, formData.period, formData.version]);

  const fetchCountries = async () => {
    // 模拟数据，实际应该调用API
    const mockCountries: Country[] = [
      { code: 'ru', name: '俄罗斯', availableCount: 1000 },
      { code: 'us', name: '美国', availableCount: 800 },
      { code: 'de', name: '德国', availableCount: 600 },
      { code: 'fr', name: '法国', availableCount: 400 },
      { code: 'gb', name: '英国', availableCount: 300 },
    ];
    setCountries(mockCountries);
  };

  const fetchPrice = async () => {
    try {
      // 模拟价格计算
      const basePrice = formData.version === '6' ? 2.5 : 3.0;
      const totalPrice = formData.count * formData.period * basePrice;
      const pricePerProxy = formData.period * basePrice;
      
      setPriceInfo({
        totalPrice,
        pricePerProxy,
        canAfford: true, // 这里应该检查用户余额
      });
    } catch (error) {
      console.error('Failed to fetch price:', error);
    }
  };

  const handlePurchase = async () => {
    if (!formData.country) {
      alert('请选择国家');
      return;
    }

    setIsPurchasing(true);
    try {
      // 这里应该调用购买API
      await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟API调用
      alert('购买成功！');
    } catch (error) {
      alert('购买失败，请重试');
    } finally {
      setIsPurchasing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">购买代理</h1>
        <p className="text-gray-600">选择您需要的代理配置并完成购买</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5" />
                <span>代理配置</span>
              </CardTitle>
              <CardDescription>
                配置您要购买的代理参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Proxy Version */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">代理版本</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant={formData.version === '6' ? 'default' : 'outline'}
                      onClick={() => setFormData({ ...formData, version: '6' })}
                      className="justify-start"
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      IPv6
                    </Button>
                    <Button
                      variant={formData.version === '4' ? 'default' : 'outline'}
                      onClick={() => setFormData({ ...formData, version: '4' })}
                      className="justify-start"
                    >
                      <Server className="h-4 w-4 mr-2" />
                      IPv4
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">协议类型</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant={formData.type === 'http' ? 'default' : 'outline'}
                      onClick={() => setFormData({ ...formData, type: 'http' })}
                    >
                      HTTP/HTTPS
                    </Button>
                    <Button
                      variant={formData.type === 'socks' ? 'default' : 'outline'}
                      onClick={() => setFormData({ ...formData, type: 'socks' })}
                    >
                      SOCKS5
                    </Button>
                  </div>
                </div>
              </div>

              {/* Country Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium">选择国家</label>
                <select
                  value={formData.country}
                  onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">请选择国家</option>
                  {countries.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.name} (可用: {country.availableCount})
                    </option>
                  ))}
                </select>
              </div>

              {/* Count and Period */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">代理数量</label>
                  <Input
                    type="number"
                    min="1"
                    max="1000"
                    value={formData.count}
                    onChange={(e) => setFormData({ ...formData, count: parseInt(e.target.value) || 1 })}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">使用期限 (天)</label>
                  <Input
                    type="number"
                    min="1"
                    max="365"
                    value={formData.period}
                    onChange={(e) => setFormData({ ...formData, period: parseInt(e.target.value) || 30 })}
                  />
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <label className="text-sm font-medium">描述 (可选)</label>
                <Input
                  placeholder="为这批代理添加描述..."
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Price Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>价格明细</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {priceInfo ? (
                <>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>代理数量:</span>
                      <span>{formData.count} 个</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>使用期限:</span>
                      <span>{formData.period} 天</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>单价:</span>
                      <span>₽{priceInfo.pricePerProxy.toFixed(2)}/个</span>
                    </div>
                    <hr />
                    <div className="flex justify-between font-medium">
                      <span>总价:</span>
                      <span className="text-lg">₽{priceInfo.totalPrice.toFixed(2)}</span>
                    </div>
                  </div>

                  <Button
                    onClick={handlePurchase}
                    disabled={isPurchasing || !formData.country || !priceInfo.canAfford}
                    className="w-full"
                  >
                    {isPurchasing ? '购买中...' : '立即购买'}
                  </Button>

                  {!priceInfo.canAfford && (
                    <p className="text-sm text-red-600 text-center">
                      余额不足，请先充值
                    </p>
                  )}
                </>
              ) : (
                <div className="text-center text-gray-500">
                  <Calculator className="h-8 w-8 mx-auto mb-2" />
                  <p>配置参数以查看价格</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Presets */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-lg">快速配置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setFormData({ ...formData, count: 5, period: 30 })}
              >
                5个代理 - 30天
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setFormData({ ...formData, count: 10, period: 30 })}
              >
                10个代理 - 30天
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setFormData({ ...formData, count: 20, period: 30 })}
              >
                20个代理 - 30天
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
