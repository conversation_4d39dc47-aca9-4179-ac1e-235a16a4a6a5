import { NextRequest, NextResponse } from 'next/server';
import { getUser<PERSON>romToken, getUserFromApiKey, logApiCall } from '@/lib/auth';
import { ProxyAPIClient } from '@/lib/api-client';

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 认证
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user || !user.apiKey) {
      await logApiCall(
        null,
        'GET',
        '/api/proxy/price',
        Object.fromEntries(request.nextUrl.searchParams),
        { error: '未授权访问或未设置API密钥' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问或未设置API密钥' },
        { status: 401 }
      );
    }

    // 获取查询参数
    const count = parseInt(request.nextUrl.searchParams.get('count') || '1');
    const period = parseInt(request.nextUrl.searchParams.get('period') || '30');
    const version = request.nextUrl.searchParams.get('version') || '6';

    // 验证参数
    if (count < 1 || count > 1000) {
      return NextResponse.json(
        { error: '代理数量必须在1-1000之间' },
        { status: 400 }
      );
    }

    if (period < 1 || period > 365) {
      return NextResponse.json(
        { error: '代理期限必须在1-365天之间' },
        { status: 400 }
      );
    }

    // 创建API客户端
    const proxyClient = new ProxyAPIClient(user.apiKey);

    // 获取价格信息
    const priceInfo = await proxyClient.getPrice({
      count,
      period,
      version: version as any,
    });

    const response = {
      count: priceInfo.count,
      period: priceInfo.period,
      version,
      totalPrice: priceInfo.price,
      pricePerProxy: priceInfo.price_single,
      currency: user.currency,
      userBalance: user.balance,
      canAfford: user.balance >= priceInfo.price,
    };

    await logApiCall(
      user.id,
      'GET',
      '/api/proxy/price',
      { count, period, version },
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get price error:', error);
    
    let errorMessage = '获取价格信息失败';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API Error')) {
        errorMessage = error.message;
        statusCode = 400;
      }
    }

    await logApiCall(
      user?.id || null,
      'GET',
      '/api/proxy/price',
      Object.fromEntries(request.nextUrl.searchParams),
      { error: errorMessage },
      statusCode,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
