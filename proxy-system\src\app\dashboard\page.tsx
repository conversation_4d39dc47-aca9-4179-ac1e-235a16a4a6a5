'use client';

import { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Space,
  Button,
  Alert,
  List,
  Tag,
  Progress,
  Avatar,
  Divider,
  Empty
} from 'antd';
import {
  DesktopOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  SettingOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  DollarOutlined,
  HistoryOutlined,
  GlobalOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface DashboardStats {
  totalProxies: number;
  activeProxies: number;
  expiringProxies: number;
  expiredProxies: number;
  totalSpent: number;
  recentTransactions: number;
}

export default function DashboardPage() {
  const [user, setUser] = useState<any>(null);
  const [stats, setStats] = useState<DashboardStats>({
    totalProxies: 0,
    activeProxies: 0,
    expiringProxies: 0,
    expiredProxies: 0,
    totalSpent: 0,
    recentTransactions: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 从localStorage获取用户信息
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // 模拟API调用
      setTimeout(() => {
        setStats({
          totalProxies: 25,
          activeProxies: 20,
          expiringProxies: 3,
          expiredProxies: 2,
          totalSpent: 1250.50,
          recentTransactions: 8,
        });
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setIsLoading(false);
    }
  };

  // 模拟最近活动数据
  const recentActivities = [
    { 
      id: 1, 
      action: '购买代理', 
      details: 'US-East-1 HTTP代理 (30天)', 
      time: '2小时前', 
      type: 'success',
      amount: '$15.99'
    },
    { 
      id: 2, 
      action: '代理到期提醒', 
      details: 'UK-London SOCKS5代理即将到期', 
      time: '1天前', 
      type: 'warning',
      amount: null
    },
    { 
      id: 3, 
      action: '续费代理', 
      details: 'JP-Tokyo HTTP代理 (30天)', 
      time: '3天前', 
      type: 'info',
      amount: '$18.99'
    },
    { 
      id: 4, 
      action: '删除代理', 
      details: 'DE-Frankfurt SOCKS5代理', 
      time: '5天前', 
      type: 'error',
      amount: null
    }
  ];

  const quickActions = [
    {
      title: '购买代理',
      description: '购买新的代理服务器',
      icon: <ShoppingCartOutlined />,
      color: '#1890ff',
      action: () => console.log('购买代理')
    },
    {
      title: '查看代理',
      description: '管理现有代理服务器',
      icon: <EyeOutlined />,
      color: '#52c41a',
      action: () => console.log('查看代理')
    },
    {
      title: '系统设置',
      description: '配置账户和偏好设置',
      icon: <SettingOutlined />,
      color: '#722ed1',
      action: () => console.log('系统设置')
    },
    {
      title: '刷新数据',
      description: '更新仪表板数据',
      icon: <ReloadOutlined />,
      color: '#fa8c16',
      action: fetchDashboardData
    }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info': return <EyeOutlined style={{ color: '#1890ff' }} />;
      case 'error': return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default: return <SettingOutlined />;
    }
  };

  const getActivityTagColor = (type: string) => {
    switch (type) {
      case 'success': return 'success';
      case 'warning': return 'warning';
      case 'info': return 'processing';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  if (isLoading) {
    return (
      <div style={{ padding: '24px' }}>
        <Row gutter={[24, 24]}>
          {[...Array(4)].map((_, i) => (
            <Col xs={24} sm={12} lg={6} key={i}>
              <Card loading />
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 欢迎信息 */}
      <Card 
        style={{ 
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          border: 'none',
          marginBottom: '24px'
        }}
        bodyStyle={{ padding: '32px' }}
      >
        <Row align="middle" justify="space-between">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={2} style={{ color: 'white', margin: 0 }}>
                欢迎回来，{user?.username || '用户'}！
              </Title>
              <Paragraph style={{ color: 'rgba(255,255,255,0.9)', margin: 0, fontSize: '16px' }}>
                管理您的代理服务，监控使用情况
              </Paragraph>
            </Space>
          </Col>
          <Col>
            <Avatar 
              size={64} 
              style={{ backgroundColor: 'rgba(255,255,255,0.2)' }}
              icon={<TrophyOutlined />}
            />
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '32px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card hoverable>
            <Statistic
              title="总代理数"
              value={stats.totalProxies}
              prefix={<DesktopOutlined style={{ color: '#1890ff' }} />}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                所有代理服务器
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card hoverable>
            <Statistic
              title="活跃代理"
              value={stats.activeProxies}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                正常运行中
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card hoverable>
            <Statistic
              title="即将到期"
              value={stats.expiringProxies}
              prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                7天内到期
              </Text>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card hoverable>
            <Statistic
              title="总消费"
              value={stats.totalSpent}
              prefix={<DollarOutlined style={{ color: '#722ed1' }} />}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: '8px' }}>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                累计消费金额
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Row gutter={[24, 24]}>
        {/* 快速操作 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ThunderboltOutlined style={{ color: '#1890ff' }} />
                <span>快速操作</span>
              </Space>
            }
            extra={
              <Button type="text" size="small">
                更多
              </Button>
            }
          >
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Card 
                    size="small"
                    hoverable
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                    bodyStyle={{ padding: '16px 8px' }}
                    onClick={action.action}
                  >
                    <div style={{ 
                      fontSize: '24px', 
                      color: action.color, 
                      marginBottom: '8px' 
                    }}>
                      {action.icon}
                    </div>
                    <div style={{ fontSize: '12px', fontWeight: 'bold' }}>
                      {action.title}
                    </div>
                    <div style={{ 
                      fontSize: '10px', 
                      color: '#8c8c8c',
                      marginTop: '4px'
                    }}>
                      {action.description}
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <HistoryOutlined style={{ color: '#52c41a' }} />
                <span>最近活动</span>
              </Space>
            }
            extra={
              <Button type="text" size="small">
                查看全部
              </Button>
            }
          >
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActivityIcon(item.type)}
                    title={
                      <Space>
                        <span style={{ fontSize: '14px' }}>{item.action}</span>
                        <Tag 
                          color={getActivityTagColor(item.type)} 
                          size="small"
                        >
                          {item.type === 'success' ? '成功' : 
                           item.type === 'warning' ? '警告' :
                           item.type === 'info' ? '信息' : '错误'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                          {item.details}
                        </div>
                        <div style={{ 
                          fontSize: '11px', 
                          color: '#bfbfbf',
                          marginTop: '2px'
                        }}>
                          {item.time}
                        </div>
                      </div>
                    }
                  />
                  {item.amount && (
                    <div style={{ 
                      fontSize: '12px', 
                      fontWeight: 'bold',
                      color: item.type === 'success' ? '#52c41a' : '#1890ff'
                    }}>
                      {item.amount}
                    </div>
                  )}
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 系统状态 */}
      <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <GlobalOutlined style={{ color: '#722ed1' }} />
                <span>系统状态</span>
              </Space>
            }
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={99.9}
                    format={() => '99.9%'}
                    strokeColor="#52c41a"
                    size={80}
                  />
                  <div style={{ marginTop: '8px', fontSize: '14px', fontWeight: 'bold' }}>
                    系统稳定性
                  </div>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                    过去30天
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={85}
                    format={() => '85ms'}
                    strokeColor="#1890ff"
                    size={80}
                  />
                  <div style={{ marginTop: '8px', fontSize: '14px', fontWeight: 'bold' }}>
                    平均延迟
                  </div>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                    全球节点
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={92}
                    format={() => '92%'}
                    strokeColor="#722ed1"
                    size={80}
                  />
                  <div style={{ marginTop: '8px', fontSize: '14px', fontWeight: 'bold' }}>
                    用户满意度
                  </div>
                  <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                    本月评分
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
