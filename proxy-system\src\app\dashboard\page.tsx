'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface DashboardStats {
  totalProxies: number;
  activeProxies: number;
  expiringProxies: number;
  expiredProxies: number;
  totalSpent: number;
  recentTransactions: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProxies: 0,
    activeProxies: 0,
    expiringProxies: 0,
    expiredProxies: 0,
    totalSpent: 0,
    recentTransactions: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // 这里可以调用API获取仪表板数据
      // 暂时使用模拟数据
      setTimeout(() => {
        setStats({
          totalProxies: 25,
          activeProxies: 20,
          expiringProxies: 3,
          expiredProxies: 2,
          totalSpent: 1250.50,
          recentTransactions: 8,
        });
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div style={{ 
        padding: '2rem',
        fontFamily: 'system-ui, sans-serif',
        backgroundColor: '#f9fafb',
        minHeight: '100vh'
      }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
          {[...Array(4)].map((_, i) => (
            <div key={i} style={{
              backgroundColor: 'white',
              borderRadius: '0.75rem',
              padding: '1.5rem',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}>
              <div style={{
                height: '1rem',
                backgroundColor: '#e5e7eb',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                width: '75%'
              }}></div>
              <div style={{
                height: '2rem',
                backgroundColor: '#e5e7eb',
                borderRadius: '0.25rem',
                width: '50%'
              }}></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '2rem',
      fontFamily: 'system-ui, sans-serif',
      backgroundColor: '#f9fafb',
      minHeight: '100vh'
    }}>
      {/* Page Header */}
      <div style={{ marginBottom: '2rem' }}>
        <h1 style={{ 
          fontSize: '2rem',
          fontWeight: 'bold',
          color: '#111827',
          margin: '0 0 0.5rem 0'
        }}>仪表板</h1>
        <p style={{ color: '#6b7280', margin: 0 }}>欢迎回来，查看您的代理服务概况</p>
      </div>

      {/* Stats Cards */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280', margin: 0 }}>总代理数</h3>
            <span style={{ fontSize: '1.25rem' }}>🖥️</span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#111827' }}>{stats.totalProxies}</div>
          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
            活跃: {stats.activeProxies} | 过期: {stats.expiredProxies}
          </p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280', margin: 0 }}>活跃代理</h3>
            <span style={{ fontSize: '1.25rem' }}>✅</span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#059669' }}>{stats.activeProxies}</div>
          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
            正常运行中
          </p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280', margin: 0 }}>即将到期</h3>
            <span style={{ fontSize: '1.25rem' }}>⏰</span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#d97706' }}>{stats.expiringProxies}</div>
          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
            3天内到期
          </p>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
            <h3 style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280', margin: 0 }}>总消费</h3>
            <span style={{ fontSize: '1.25rem' }}>💳</span>
          </div>
          <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#111827' }}>₽{stats.totalSpent.toFixed(2)}</div>
          <p style={{ fontSize: '0.75rem', color: '#6b7280', margin: '0.25rem 0 0 0' }}>
            {stats.recentTransactions} 笔交易
          </p>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ 
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ marginBottom: '1rem' }}>
            <h3 style={{ 
              fontSize: '1.125rem',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 0.5rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <span>🛒</span>
              快速购买
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
              快速购买新的代理服务器
            </p>
          </div>
          
          <div style={{ 
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '1rem',
            marginBottom: '1rem'
          }}>
            <div style={{ 
              textAlign: 'center',
              padding: '1rem',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🌐</div>
              <p style={{ fontWeight: '500', margin: '0 0 0.25rem 0' }}>IPv6 代理</p>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>高速稳定</p>
            </div>
            <div style={{ 
              textAlign: 'center',
              padding: '1rem',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🖥️</div>
              <p style={{ fontWeight: '500', margin: '0 0 0.25rem 0' }}>IPv4 代理</p>
              <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>兼容性好</p>
            </div>
          </div>
          
          <Link href="/dashboard/buy" style={{ textDecoration: 'none' }}>
            <button style={{
              width: '100%',
              padding: '0.75rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#1d4ed8'}
            onMouseOut={(e) => e.target.style.backgroundColor = '#2563eb'}
            >
              立即购买代理
            </button>
          </Link>
        </div>

        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.75rem',
          padding: '1.5rem',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ marginBottom: '1rem' }}>
            <h3 style={{ 
              fontSize: '1.125rem',
              fontWeight: 'bold',
              color: '#111827',
              margin: '0 0 0.5rem 0',
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              <span>⚠️</span>
              注意事项
            </h3>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
              重要提醒和系统通知
            </p>
          </div>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            {stats.expiringProxies > 0 && (
              <div style={{ 
                padding: '0.75rem',
                backgroundColor: '#fef3c7',
                border: '1px solid #fbbf24',
                borderRadius: '0.375rem'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                  <span>⏰</span>
                  <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#92400e' }}>
                    {stats.expiringProxies} 个代理即将到期
                  </span>
                </div>
                <p style={{ fontSize: '0.75rem', color: '#92400e', margin: 0 }}>
                  请及时续费以避免服务中断
                </p>
              </div>
            )}
            
            {stats.expiredProxies > 0 && (
              <div style={{ 
                padding: '0.75rem',
                backgroundColor: '#fef2f2',
                border: '1px solid #fca5a5',
                borderRadius: '0.375rem'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                  <span>⚠️</span>
                  <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#991b1b' }}>
                    {stats.expiredProxies} 个代理已过期
                  </span>
                </div>
                <p style={{ fontSize: '0.75rem', color: '#991b1b', margin: 0 }}>
                  已过期的代理无法使用
                </p>
              </div>
            )}

            <div style={{ 
              padding: '0.75rem',
              backgroundColor: '#dbeafe',
              border: '1px solid #60a5fa',
              borderRadius: '0.375rem'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.25rem' }}>
                <span>📈</span>
                <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#1e40af' }}>
                  系统运行正常
                </span>
              </div>
              <p style={{ fontSize: '0.75rem', color: '#1e40af', margin: 0 }}>
                所有服务运行稳定
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '0.75rem',
        padding: '1.5rem',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        border: '1px solid #e5e7eb'
      }}>
        <div style={{ marginBottom: '1rem' }}>
          <h3 style={{ 
            fontSize: '1.125rem',
            fontWeight: 'bold',
            color: '#111827',
            margin: '0 0 0.5rem 0'
          }}>最近活动</h3>
          <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: 0 }}>
            查看您最近的操作记录
          </p>
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingBottom: '0.5rem',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ width: '0.5rem', height: '0.5rem', backgroundColor: '#059669', borderRadius: '50%' }}></div>
              <span style={{ fontSize: '0.875rem' }}>购买了 5 个俄罗斯代理</span>
            </div>
            <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>2小时前</span>
          </div>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingBottom: '0.5rem',
            borderBottom: '1px solid #e5e7eb'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ width: '0.5rem', height: '0.5rem', backgroundColor: '#2563eb', borderRadius: '50%' }}></div>
              <span style={{ fontSize: '0.875rem' }}>延期了 3 个美国代理</span>
            </div>
            <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>1天前</span>
          </div>
          <div style={{ 
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ width: '0.5rem', height: '0.5rem', backgroundColor: '#d97706', borderRadius: '50%' }}></div>
              <span style={{ fontSize: '0.875rem' }}>删除了 2 个过期代理</span>
            </div>
            <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>3天前</span>
          </div>
        </div>
        
        <div style={{ marginTop: '1rem' }}>
          <Link href="/dashboard/transactions" style={{ textDecoration: 'none' }}>
            <button style={{
              width: '100%',
              padding: '0.75rem',
              backgroundColor: 'white',
              color: '#374151',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              cursor: 'pointer',
              transition: 'background-color 0.2s'
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = '#f9fafb'}
            onMouseOut={(e) => e.target.style.backgroundColor = 'white'}
            >
              查看所有交易记录
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
