'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Server, 
  ShoppingCart, 
  Clock, 
  AlertTriangle,
  TrendingUp,
  Globe,
  Activity,
  CreditCard
} from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
  totalProxies: number;
  activeProxies: number;
  expiringProxies: number;
  expiredProxies: number;
  totalSpent: number;
  recentTransactions: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProxies: 0,
    activeProxies: 0,
    expiringProxies: 0,
    expiredProxies: 0,
    totalSpent: 0,
    recentTransactions: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // 这里可以调用API获取仪表板数据
      // 暂时使用模拟数据
      setTimeout(() => {
        setStats({
          totalProxies: 25,
          activeProxies: 20,
          expiringProxies: 3,
          expiredProxies: 2,
          totalSpent: 1250.50,
          recentTransactions: 8,
        });
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
        <p className="text-gray-600">欢迎回来，查看您的代理服务概况</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总代理数</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProxies}</div>
            <p className="text-xs text-muted-foreground">
              活跃: {stats.activeProxies} | 过期: {stats.expiredProxies}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃代理</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.activeProxies}</div>
            <p className="text-xs text-muted-foreground">
              正常运行中
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">即将到期</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.expiringProxies}</div>
            <p className="text-xs text-muted-foreground">
              3天内到期
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总消费</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₽{stats.totalSpent.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {stats.recentTransactions} 笔交易
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ShoppingCart className="h-5 w-5" />
              <span>快速购买</span>
            </CardTitle>
            <CardDescription>
              快速购买新的代理服务器
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Globe className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                <p className="font-medium">IPv6 代理</p>
                <p className="text-sm text-gray-500">高速稳定</p>
              </div>
              <div className="text-center p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <Server className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <p className="font-medium">IPv4 代理</p>
                <p className="text-sm text-gray-500">兼容性好</p>
              </div>
            </div>
            <Link href="/dashboard/buy">
              <Button className="w-full">
                立即购买代理
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5" />
              <span>注意事项</span>
            </CardTitle>
            <CardDescription>
              重要提醒和系统通知
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {stats.expiringProxies > 0 && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">
                    {stats.expiringProxies} 个代理即将到期
                  </span>
                </div>
                <p className="text-xs text-yellow-700 mt-1">
                  请及时续费以避免服务中断
                </p>
              </div>
            )}
            
            {stats.expiredProxies > 0 && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">
                    {stats.expiredProxies} 个代理已过期
                  </span>
                </div>
                <p className="text-xs text-red-700 mt-1">
                  已过期的代理无法使用
                </p>
              </div>
            )}

            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">
                  系统运行正常
                </span>
              </div>
              <p className="text-xs text-blue-700 mt-1">
                所有服务运行稳定
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>最近活动</CardTitle>
          <CardDescription>
            查看您最近的操作记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b">
              <div className="flex items-center space-x-3">
                <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                <span className="text-sm">购买了 5 个俄罗斯代理</span>
              </div>
              <span className="text-xs text-gray-500">2小时前</span>
            </div>
            <div className="flex items-center justify-between py-2 border-b">
              <div className="flex items-center space-x-3">
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm">延期了 3 个美国代理</span>
              </div>
              <span className="text-xs text-gray-500">1天前</span>
            </div>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                <span className="text-sm">删除了 2 个过期代理</span>
              </div>
              <span className="text-xs text-gray-500">3天前</span>
            </div>
          </div>
          <div className="mt-4">
            <Link href="/dashboard/transactions">
              <Button variant="outline" className="w-full">
                查看所有交易记录
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
