import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { db } from './db';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export interface JWTPayload {
  userId: string;
  email: string;
  username: string;
}

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

// 验证密码
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

// 生成JWT令牌
export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '7d',
  });
}

// 验证JWT令牌
export function verifyToken(token: string): JWTPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as JWTPayload;
  } catch (error) {
    return null;
  }
}

// 生成API密钥
export function generateApiKey(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 从请求头获取用户信息
export async function getUserFromToken(authHeader: string | null): Promise<any> {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7);
  const payload = verifyToken(token);
  
  if (!payload) {
    return null;
  }

  try {
    const user = await db.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        username: true,
        apiKey: true,
        balance: true,
        currency: true,
        isActive: true,
      },
    });

    return user;
  } catch (error) {
    return null;
  }
}

// 从API密钥获取用户信息
export async function getUserFromApiKey(apiKey: string): Promise<any> {
  try {
    const user = await db.user.findUnique({
      where: { apiKey },
      select: {
        id: true,
        email: true,
        username: true,
        apiKey: true,
        balance: true,
        currency: true,
        isActive: true,
      },
    });

    return user;
  } catch (error) {
    return null;
  }
}

// 记录API调用日志
export async function logApiCall(
  userId: string | null,
  method: string,
  endpoint: string,
  params: any,
  response: any,
  statusCode: number,
  duration: number,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  try {
    await db.apiLog.create({
      data: {
        userId,
        method,
        endpoint,
        params,
        response,
        statusCode,
        duration,
        ipAddress,
        userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log API call:', error);
  }
}
