"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// tslint:disable:no-console\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createKey: function() {\n        return createKey;\n    },\n    default: function() {\n        return Router;\n    },\n    matchesMiddleware: function() {\n        return matchesMiddleware;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _removetrailingslash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/remove-trailing-slash.js\");\nconst _routeloader = __webpack_require__(/*! ../../../client/route-loader */ \"(pages-dir-browser)/./node_modules/next/dist/client/route-loader.js\");\nconst _script = __webpack_require__(/*! ../../../client/script */ \"(pages-dir-browser)/./node_modules/next/dist/client/script.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ../../../lib/is-error */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _denormalizepagepath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/page-path/denormalize-page-path.js\");\nconst _normalizelocalepath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/i18n/normalize-locale-path.js\");\nconst _mitt = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../mitt */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/mitt.js\"));\nconst _utils = __webpack_require__(/*! ../utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _isdynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js\");\nconst _parserelativeurl = __webpack_require__(/*! ./utils/parse-relative-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/parse-relative-url.js\");\nconst _resolverewrites = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./utils/resolve-rewrites */ \"?506d\"));\nconst _routematcher = __webpack_require__(/*! ./utils/route-matcher */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js\");\nconst _routeregex = __webpack_require__(/*! ./utils/route-regex */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js\");\nconst _formaturl = __webpack_require__(/*! ./utils/format-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _detectdomainlocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/detect-domain-locale.js\");\nconst _parsepath = __webpack_require__(/*! ./utils/parse-path */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/parse-path.js\");\nconst _addlocale = __webpack_require__(/*! ../../../client/add-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _removelocale = __webpack_require__(/*! ../../../client/remove-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-locale.js\");\nconst _removebasepath = __webpack_require__(/*! ../../../client/remove-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _addbasepath = __webpack_require__(/*! ../../../client/add-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../client/has-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _resolvehref = __webpack_require__(/*! ../../../client/resolve-href */ \"(pages-dir-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _isapiroute = __webpack_require__(/*! ../../../lib/is-api-route */ \"(pages-dir-browser)/./node_modules/next/dist/lib/is-api-route.js\");\nconst _getnextpathnameinfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/get-next-pathname-info.js\");\nconst _formatnextpathnameinfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-next-pathname-info.js\");\nconst _comparestates = __webpack_require__(/*! ./utils/compare-states */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/compare-states.js\");\nconst _islocalurl = __webpack_require__(/*! ./utils/is-local-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _isbot = __webpack_require__(/*! ./utils/is-bot */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _omit = __webpack_require__(/*! ./utils/omit */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js\");\nconst _interpolateas = __webpack_require__(/*! ./utils/interpolate-as */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js\");\nconst _handlesmoothscroll = __webpack_require__(/*! ./utils/handle-smooth-scroll */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/handle-smooth-scroll.js\");\nconst _constants = __webpack_require__(/*! ../../../lib/constants */ \"(pages-dir-browser)/./node_modules/next/dist/lib/constants.js\");\nfunction buildCancellationError() {\n    return Object.assign(Object.defineProperty(new Error('Route Cancelled'), \"__NEXT_ERROR_CODE\", {\n        value: \"E315\",\n        enumerable: false,\n        configurable: true\n    }), {\n        cancelled: true\n    });\n}\nasync function matchesMiddleware(options) {\n    const matchers = await Promise.resolve(options.router.pageLoader.getMiddleware());\n    if (!matchers) return false;\n    const { pathname: asPathname } = (0, _parsepath.parsePath)(options.asPath);\n    // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n    const cleanedAs = (0, _hasbasepath.hasBasePath)(asPathname) ? (0, _removebasepath.removeBasePath)(asPathname) : asPathname;\n    const asWithBasePathAndLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(cleanedAs, options.locale));\n    // Check only path match on client. Matching \"has\" should be done on server\n    // where we can access more info such as headers, HttpOnly cookie, etc.\n    return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils.getLocationOrigin)();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, url, true);\n    const origin = (0, _utils.getLocationOrigin)();\n    const hrefWasAbsolute = resolvedHref.startsWith(origin);\n    const asWasAbsolute = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefWasAbsolute ? resolvedHref : (0, _addbasepath.addBasePath)(resolvedHref);\n    const preparedAs = as ? stripOrigin((0, _resolvehref.resolveHref)(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asWasAbsolute ? preparedAs : (0, _addbasepath.addBasePath)(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removetrailingslash.removeTrailingSlash)((0, _denormalizepagepath.denormalizePagePath)(pathname));\n    if (cleanPathname === '/404' || cleanPathname === '/_error') {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isdynamic.isDynamicRoute)(page) && (0, _routeregex.getRouteRegex)(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removetrailingslash.removeTrailingSlash)(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get('x-nextjs-rewrite');\n    let rewriteTarget = rewriteHeader || response.headers.get('x-nextjs-matched-path');\n    const matchedPath = response.headers.get(_constants.MATCHED_PATH_HEADER);\n    if (matchedPath && !rewriteTarget && !matchedPath.includes('__next_data_catchall') && !matchedPath.includes('/_error') && !matchedPath.includes('/404')) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith('/') || false) {\n            const parsedRewriteTarget = (0, _parserelativeurl.parseRelativeUrl)(rewriteTarget);\n            const pathnameInfo = (0, _getnextpathnameinfo.getNextPathnameInfo)(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removetrailingslash.removeTrailingSlash)(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)()\n            ]).then((param)=>{\n                let [pages, { __rewrites: rewrites }] = param;\n                let as = (0, _addlocale.addLocale)(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isdynamic.isDynamicRoute)(as) || !rewriteHeader && pages.includes((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getnextpathnameinfo.getNextPathnameInfo)((0, _parserelativeurl.parseRelativeUrl)(source).pathname, {\n                        nextConfig:  false ? 0 : nextConfig,\n                        parseData: true\n                    });\n                    as = (0, _addbasepath.addBasePath)(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizelocalepath.normalizeLocalePath)((0, _removebasepath.removeBasePath)(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isdynamic.isDynamicRoute)(resolvedHref)) {\n                    const matches = (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: 'rewrite',\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsepath.parsePath)(source);\n        const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n            ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                nextConfig,\n                parseData: true\n            }),\n            defaultLocale: options.router.defaultLocale,\n            buildId: ''\n        });\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: \"\" + pathname + src.query + src.hash\n        });\n    }\n    const redirectTarget = response.headers.get('x-nextjs-redirect');\n    if (redirectTarget) {\n        if (redirectTarget.startsWith('/')) {\n            const src = (0, _parsepath.parsePath)(redirectTarget);\n            const pathname = (0, _formatnextpathnameinfo.formatNextPathnameInfo)({\n                ...(0, _getnextpathnameinfo.getNextPathnameInfo)(src.pathname, {\n                    nextConfig,\n                    parseData: true\n                }),\n                defaultLocale: options.router.defaultLocale,\n                buildId: ''\n            });\n            return Promise.resolve({\n                type: 'redirect-internal',\n                newAs: \"\" + pathname + src.query + src.hash,\n                newUrl: \"\" + pathname + src.query + src.hash\n            });\n        }\n        return Promise.resolve({\n            type: 'redirect-external',\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: 'next'\n    });\n}\nasync function withMiddlewareEffects(options) {\n    const matches = await matchesMiddleware(options);\n    if (!matches || !options.fetchData) {\n        return null;\n    }\n    const data = await options.fetchData();\n    const effect = await getMiddlewareData(data.dataHref, data.response, options);\n    return {\n        dataHref: data.dataHref,\n        json: data.json,\n        response: data.response,\n        text: data.text,\n        cacheKey: data.cacheKey,\n        effect\n    };\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND');\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: 'same-origin',\n        method: options.method || 'GET',\n        headers: Object.assign({}, options.headers, {\n            'x-nextjs-data': '1'\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData(param) {\n    let { dataHref, inflightCache, isPrefetch, hasMiddleware, isServerRender, parseJSON, persistCache, isBackground, unstable_skipClientCache } = param;\n    const { href: cacheKey } = new URL(dataHref, window.location.href);\n    const getData = (params)=>{\n        var _params_method;\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: Object.assign({}, isPrefetch ? {\n                purpose: 'prefetch'\n            } : {}, isPrefetch && hasMiddleware ? {\n                'x-middleware-prefetch': '1'\n            } : {},  false ? 0 : {}),\n            method: (_params_method = params == null ? void 0 : params.method) != null ? _params_method : 'GET'\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === 'HEAD') {\n                return {\n                    dataHref,\n                    response,\n                    text: '',\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (response.status === 404) {\n                        var _tryToParseAsJSON;\n                        if ((_tryToParseAsJSON = tryToParseAsJSON(text)) == null ? void 0 : _tryToParseAsJSON.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = Object.defineProperty(new Error(\"Failed to load static props\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E124\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeloader.markAssetError)(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== 'production' || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            if (!unstable_skipClientCache) {\n                delete inflightCache[cacheKey];\n            }\n            if (err.message === 'Failed to fetch' || // firefox\n            err.message === 'NetworkError when attempting to fetch resource.' || // safari\n            err.message === 'Load failed') {\n                (0, _routeloader.markAssetError)(err);\n            }\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            if (data.response.headers.get('x-middleware-cache') !== 'no-cache') {\n                // only update cache if not marked as no-cache\n                inflightCache[cacheKey] = Promise.resolve(data);\n            }\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: 'HEAD'\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation(param) {\n    let { url, router } = param;\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(router.asPath, router.locale))) {\n        throw Object.defineProperty(new Error(\"Invariant: attempted to hard navigate to the same URL \" + url + \" \" + location.href), \"__NEXT_ERROR_CODE\", {\n            value: \"E282\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = (param)=>{\n    let { route, router } = param;\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = Object.defineProperty(new Error('Abort fetching component for route: \"' + route + '\"'), \"__NEXT_ERROR_CODE\", {\n                value: \"E483\",\n                enumerable: false,\n                configurable: true\n            });\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Go forward in history\n   */ forward() {\n        window.history.forward();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options) {\n        if (options === void 0) options = {};\n        if (false) {}\n        ;\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change('pushState', url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options) {\n        if (options === void 0) options = {};\n        ;\n        ({ url, as } = prepareUrlAs(this, url, as));\n        return this.change('replaceState', url, as, options);\n    }\n    async _bfl(as, resolvedAs, locale, skipNavigate) {\n        if (true) {\n            if (!this._bfl_s && !this._bfl_d) {\n                const { BloomFilter } = __webpack_require__(/*! ../../lib/bloom-filter */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/bloom-filter.js\");\n                let staticFilterData;\n                let dynamicFilterData;\n                try {\n                    ;\n                    ({ __routerFilterStatic: staticFilterData, __routerFilterDynamic: dynamicFilterData } = await (0, _routeloader.getClientBuildManifest)());\n                } catch (err) {\n                    // failed to load build manifest hard navigate\n                    // to be safe\n                    console.error(err);\n                    if (skipNavigate) {\n                        return true;\n                    }\n                    handleHardNavigation({\n                        url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                const routerFilterSValue = {\"numItems\":17,\"errorRate\":0.0001,\"numBits\":326,\"numHashes\":14,\"bitArray\":[0,1,1,1,1,0,0,0,1,0,0,1,1,0,0,1,1,1,1,0,1,1,0,1,1,0,0,0,0,1,0,1,0,1,1,0,0,0,1,0,1,1,1,0,1,0,1,0,0,0,1,0,1,1,0,0,1,1,1,0,1,0,0,1,0,1,1,1,1,0,0,1,1,0,1,1,1,1,1,0,0,1,0,1,1,1,0,1,1,1,0,0,0,0,0,1,1,1,0,1,1,1,0,0,0,0,1,0,0,1,1,1,0,0,0,1,0,1,1,1,1,1,1,1,0,1,1,1,0,1,1,1,1,1,0,0,1,0,1,0,1,0,1,0,0,0,0,0,0,1,0,1,0,1,1,0,0,1,0,0,0,0,1,1,1,1,1,1,0,1,1,1,1,0,1,0,1,1,1,1,1,1,0,1,0,1,0,0,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,0,1,0,1,1,1,0,0,1,0,0,0,1,0,0,0,1,1,0,1,1,1,0,0,1,1,1,1,0,0,0,1,1,0,1,0,1,1,1,1,1,0,1,0,1,0,1,1,0,0,1,1,1,1,0,0,0,1,1,0,1,1,1,1,0,1,0,1,1,0,0,0,0,1,1,1,1,0,1,1,0,1,1,1,1,0,0,0,0,0,0,0,1,1,0,0,1,0,0,1,1,1,1,1,0,1,1,1,1,1,0,1,1,0,1,0,0,1]};\n                if (!staticFilterData && routerFilterSValue) {\n                    staticFilterData = routerFilterSValue ? routerFilterSValue : undefined;\n                }\n                const routerFilterDValue = {\"numItems\":0,\"errorRate\":0.0001,\"numBits\":0,\"numHashes\":null,\"bitArray\":[]};\n                if (!dynamicFilterData && routerFilterDValue) {\n                    dynamicFilterData = routerFilterDValue ? routerFilterDValue : undefined;\n                }\n                if (staticFilterData == null ? void 0 : staticFilterData.numHashes) {\n                    this._bfl_s = new BloomFilter(staticFilterData.numItems, staticFilterData.errorRate);\n                    this._bfl_s.import(staticFilterData);\n                }\n                if (dynamicFilterData == null ? void 0 : dynamicFilterData.numHashes) {\n                    this._bfl_d = new BloomFilter(dynamicFilterData.numItems, dynamicFilterData.errorRate);\n                    this._bfl_d.import(dynamicFilterData);\n                }\n            }\n            let matchesBflStatic = false;\n            let matchesBflDynamic = false;\n            const pathsToCheck = [\n                {\n                    as\n                },\n                {\n                    as: resolvedAs\n                }\n            ];\n            for (const { as: curAs, allowMatchCurrent } of pathsToCheck){\n                if (curAs) {\n                    const asNoSlash = (0, _removetrailingslash.removeTrailingSlash)(new URL(curAs, 'http://n').pathname);\n                    const asNoSlashLocale = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(asNoSlash, locale || this.locale));\n                    if (allowMatchCurrent || asNoSlash !== (0, _removetrailingslash.removeTrailingSlash)(new URL(this.asPath, 'http://n').pathname)) {\n                        var _this__bfl_s, _this__bfl_s1;\n                        matchesBflStatic = matchesBflStatic || !!((_this__bfl_s = this._bfl_s) == null ? void 0 : _this__bfl_s.contains(asNoSlash)) || !!((_this__bfl_s1 = this._bfl_s) == null ? void 0 : _this__bfl_s1.contains(asNoSlashLocale));\n                        for (const normalizedAS of [\n                            asNoSlash,\n                            asNoSlashLocale\n                        ]){\n                            // if any sub-path of as matches a dynamic filter path\n                            // it should be hard navigated\n                            const curAsParts = normalizedAS.split('/');\n                            for(let i = 0; !matchesBflDynamic && i < curAsParts.length + 1; i++){\n                                var _this__bfl_d;\n                                const currentPart = curAsParts.slice(0, i).join('/');\n                                if (currentPart && ((_this__bfl_d = this._bfl_d) == null ? void 0 : _this__bfl_d.contains(currentPart))) {\n                                    matchesBflDynamic = true;\n                                    break;\n                                }\n                            }\n                        }\n                        // if the client router filter is matched then we trigger\n                        // a hard navigation\n                        if (matchesBflStatic || matchesBflDynamic) {\n                            if (skipNavigate) {\n                                return true;\n                            }\n                            handleHardNavigation({\n                                url: (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, locale || this.locale, this.defaultLocale)),\n                                router: this\n                            });\n                            return new Promise(()=>{});\n                        }\n                    }\n                }\n            }\n        }\n        return false;\n    }\n    async change(method, url, as, options, forcedScroll) {\n        var _this_components_pathname;\n        if (!(0, _islocalurl.isLocalURL)(url)) {\n            handleHardNavigation({\n                url,\n                router: this\n            });\n            return false;\n        }\n        // WARNING: `_h` is an internal option for handing Next.js client-side\n        // hydration. Your app should _never_ use this property. It may change at\n        // any time without notice.\n        const isQueryUpdating = options._h === 1;\n        if (!isQueryUpdating && !options.shallow) {\n            await this._bfl(as, undefined, options.locale);\n        }\n        let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsepath.parsePath)(url).pathname === (0, _parsepath.parsePath)(as).pathname;\n        const nextState = {\n            ...this.state\n        };\n        // for static pages with query params in the URL we delay\n        // marking the router ready until after the query is updated\n        // or a navigation has occurred\n        const readyStateChange = this.isReady !== true;\n        this.isReady = true;\n        const isSsr = this.isSsr;\n        if (!isQueryUpdating) {\n            this.isSsr = false;\n        }\n        // if a route transition is already in progress before\n        // the query updating is triggered ignore query updating\n        if (isQueryUpdating && this.clc) {\n            return false;\n        }\n        const prevLocale = nextState.locale;\n        if (false) { var _this_locales; }\n        // marking route changes as a navigation start entry\n        if (_utils.ST) {\n            performance.mark('routeChange');\n        }\n        const { shallow = false, scroll = true } = options;\n        const routeProps = {\n            shallow\n        };\n        if (this._inFlightRoute && this.clc) {\n            if (!isSsr) {\n                Router.events.emit('routeChangeError', buildCancellationError(), this._inFlightRoute, routeProps);\n            }\n            this.clc();\n            this.clc = null;\n        }\n        as = (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, options.locale, this.defaultLocale));\n        const cleanedAs = (0, _removelocale.removeLocale)((0, _hasbasepath.hasBasePath)(as) ? (0, _removebasepath.removeBasePath)(as) : as, nextState.locale);\n        this._inFlightRoute = as;\n        const localeChange = prevLocale !== nextState.locale;\n        // If the url change is only related to a hash change\n        // We should not proceed. We should only change the state.\n        if (!isQueryUpdating && this.onlyAHashChange(cleanedAs) && !localeChange) {\n            nextState.asPath = cleanedAs;\n            Router.events.emit('hashChangeStart', as, routeProps);\n            // TODO: do we need the resolved href when only a hash change?\n            this.changeState(method, url, as, {\n                ...options,\n                scroll: false\n            });\n            if (scroll) {\n                this.scrollToHash(cleanedAs);\n            }\n            try {\n                await this.set(nextState, this.components[nextState.route], null);\n            } catch (err) {\n                if ((0, _iserror.default)(err) && err.cancelled) {\n                    Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n                }\n                throw err;\n            }\n            Router.events.emit('hashChangeComplete', as, routeProps);\n            return true;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        let { pathname, query } = parsed;\n        // The build manifest needs to be loaded before auto-static dynamic pages\n        // get their query parameters to allow ensuring they can be parsed properly\n        // when rewritten to\n        let pages, rewrites;\n        try {\n            ;\n            [pages, { __rewrites: rewrites }] = await Promise.all([\n                this.pageLoader.getPageList(),\n                (0, _routeloader.getClientBuildManifest)(),\n                this.pageLoader.getMiddleware()\n            ]);\n        } catch (err) {\n            // If we fail to resolve the page list or client-build manifest, we must\n            // do a server-side transition:\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        // If asked to change the current URL we should reload the current page\n        // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n        // We also need to set the method = replaceState always\n        // as this should not go into the history (That's how browsers work)\n        // We should compare the new asPath to the current asPath, not the url\n        if (!this.urlIsNew(cleanedAs) && !localeChange) {\n            method = 'replaceState';\n        }\n        // we need to resolve the as value using rewrites for dynamic SSG\n        // pages to allow building the data URL correctly\n        let resolvedAs = as;\n        // url and as should always be prefixed with basePath by this\n        // point by either next/link or router.push/replace so strip the\n        // basePath from the pathname to match the pages dir 1-to-1\n        pathname = pathname ? (0, _removetrailingslash.removeTrailingSlash)((0, _removebasepath.removeBasePath)(pathname)) : pathname;\n        let route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        const parsedAsPathname = as.startsWith('/') && (0, _parserelativeurl.parseRelativeUrl)(as).pathname;\n        // if we detected the path as app route during prefetching\n        // trigger hard navigation\n        if ((_this_components_pathname = this.components[pathname]) == null ? void 0 : _this_components_pathname.__appRouter) {\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return new Promise(()=>{});\n        }\n        const isMiddlewareRewrite = !!(parsedAsPathname && route !== parsedAsPathname && (!(0, _isdynamic.isDynamicRoute)(route) || !(0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(route))(parsedAsPathname)));\n        // we don't attempt resolve asPath when we need to execute\n        // middleware as the resolving will occur server-side\n        const isMiddlewareMatch = !options.shallow && await matchesMiddleware({\n            asPath: as,\n            locale: nextState.locale,\n            router: this\n        });\n        if (isQueryUpdating && isMiddlewareMatch) {\n            shouldResolveHref = false;\n        }\n        if (shouldResolveHref && pathname !== '/_error') {\n            ;\n            options._shouldResolveHref = true;\n            if (false) {} else {\n                parsed.pathname = resolveDynamicRoute(pathname, pages);\n                if (parsed.pathname !== pathname) {\n                    pathname = parsed.pathname;\n                    parsed.pathname = (0, _addbasepath.addBasePath)(pathname);\n                    if (!isMiddlewareMatch) {\n                        url = (0, _formaturl.formatWithValidation)(parsed);\n                    }\n                }\n            }\n        }\n        if (!(0, _islocalurl.isLocalURL)(as)) {\n            if (true) {\n                throw Object.defineProperty(new Error('Invalid href: \"' + url + '\" and as: \"' + as + '\", received relative href and external as' + \"\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as\"), \"__NEXT_ERROR_CODE\", {\n                    value: \"E380\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            return false;\n        }\n        resolvedAs = (0, _removelocale.removeLocale)((0, _removebasepath.removeBasePath)(resolvedAs), nextState.locale);\n        route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        let routeMatch = false;\n        if ((0, _isdynamic.isDynamicRoute)(route)) {\n            const parsedAs = (0, _parserelativeurl.parseRelativeUrl)(resolvedAs);\n            const asPathname = parsedAs.pathname;\n            const routeRegex = (0, _routeregex.getRouteRegex)(route);\n            routeMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(asPathname);\n            const shouldInterpolate = route === asPathname;\n            const interpolatedAs = shouldInterpolate ? (0, _interpolateas.interpolateAs)(route, asPathname, query) : {};\n            if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param] && !routeRegex.groups[param].optional);\n                if (missingParams.length > 0 && !isMiddlewareMatch) {\n                    if (true) {\n                        console.warn(\"\" + (shouldInterpolate ? \"Interpolating href\" : \"Mismatching `as` and `href`\") + \" failed to manually provide \" + (\"the params: \" + missingParams.join(', ') + \" in the `href`'s `query`\"));\n                    }\n                    throw Object.defineProperty(new Error((shouldInterpolate ? \"The provided `href` (\" + url + \") value is missing query values (\" + missingParams.join(', ') + \") to be interpolated properly. \" : \"The provided `as` value (\" + asPathname + \") is incompatible with the `href` value (\" + route + \"). \") + (\"Read more: https://nextjs.org/docs/messages/\" + (shouldInterpolate ? 'href-interpolation-failed' : 'incompatible-href-as'))), \"__NEXT_ERROR_CODE\", {\n                        value: \"E344\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            } else if (shouldInterpolate) {\n                as = (0, _formaturl.formatWithValidation)(Object.assign({}, parsedAs, {\n                    pathname: interpolatedAs.result,\n                    query: (0, _omit.omit)(query, interpolatedAs.params)\n                }));\n            } else {\n                // Merge params into `query`, overwriting any specified in search\n                Object.assign(query, routeMatch);\n            }\n        }\n        if (!isQueryUpdating) {\n            Router.events.emit('routeChangeStart', as, routeProps);\n        }\n        const isErrorRoute = this.pathname === '/404' || this.pathname === '/_error';\n        try {\n            var _self___NEXT_DATA___props_pageProps, _self___NEXT_DATA___props, _routeInfo_props;\n            let routeInfo = await this.getRouteInfo({\n                route,\n                pathname,\n                query,\n                as,\n                resolvedAs,\n                routeProps,\n                locale: nextState.locale,\n                isPreview: nextState.isPreview,\n                hasMiddleware: isMiddlewareMatch,\n                unstable_skipClientCache: options.unstable_skipClientCache,\n                isQueryUpdating: isQueryUpdating && !this.isFallback,\n                isMiddlewareRewrite\n            });\n            if (!isQueryUpdating && !options.shallow) {\n                await this._bfl(as, 'resolvedAs' in routeInfo ? routeInfo.resolvedAs : undefined, nextState.locale);\n            }\n            if ('route' in routeInfo && isMiddlewareMatch) {\n                pathname = routeInfo.route || route;\n                route = pathname;\n                if (!routeProps.shallow) {\n                    query = Object.assign({}, routeInfo.query || {}, query);\n                }\n                const cleanedParsedPathname = (0, _hasbasepath.hasBasePath)(parsed.pathname) ? (0, _removebasepath.removeBasePath)(parsed.pathname) : parsed.pathname;\n                if (routeMatch && pathname !== cleanedParsedPathname) {\n                    Object.keys(routeMatch).forEach((key)=>{\n                        if (routeMatch && query[key] === routeMatch[key]) {\n                            delete query[key];\n                        }\n                    });\n                }\n                if ((0, _isdynamic.isDynamicRoute)(pathname)) {\n                    const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(new URL(as, location.href).pathname, nextState.locale), true);\n                    let rewriteAs = prefixedAs;\n                    if ((0, _hasbasepath.hasBasePath)(rewriteAs)) {\n                        rewriteAs = (0, _removebasepath.removeBasePath)(rewriteAs);\n                    }\n                    if (false) {}\n                    const routeRegex = (0, _routeregex.getRouteRegex)(pathname);\n                    const curRouteMatch = (0, _routematcher.getRouteMatcher)(routeRegex)(new URL(rewriteAs, location.href).pathname);\n                    if (curRouteMatch) {\n                        Object.assign(query, curRouteMatch);\n                    }\n                }\n            }\n            // If the routeInfo brings a redirect we simply apply it.\n            if ('type' in routeInfo) {\n                if (routeInfo.type === 'redirect-internal') {\n                    return this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                } else {\n                    handleHardNavigation({\n                        url: routeInfo.destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n            }\n            const component = routeInfo.Component;\n            if (component && component.unstable_scriptLoader) {\n                const scripts = [].concat(component.unstable_scriptLoader());\n                scripts.forEach((script)=>{\n                    (0, _script.handleClientScriptLoad)(script.props);\n                });\n            }\n            // handle redirect on client-transition\n            if ((routeInfo.__N_SSG || routeInfo.__N_SSP) && routeInfo.props) {\n                if (routeInfo.props.pageProps && routeInfo.props.pageProps.__N_REDIRECT) {\n                    // Use the destination from redirect without adding locale\n                    options.locale = false;\n                    const destination = routeInfo.props.pageProps.__N_REDIRECT;\n                    // check if destination is internal (resolves to a page) and attempt\n                    // client-navigation if it is falling back to hard navigation if\n                    // it's not\n                    if (destination.startsWith('/') && routeInfo.props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                        const parsedHref = (0, _parserelativeurl.parseRelativeUrl)(destination);\n                        parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                        const { url: newUrl, as: newAs } = prepareUrlAs(this, destination, destination);\n                        return this.change(method, newUrl, newAs, options);\n                    }\n                    handleHardNavigation({\n                        url: destination,\n                        router: this\n                    });\n                    return new Promise(()=>{});\n                }\n                nextState.isPreview = !!routeInfo.props.__N_PREVIEW;\n                // handle SSG data 404\n                if (routeInfo.props.notFound === SSG_DATA_NOT_FOUND) {\n                    let notFoundRoute;\n                    try {\n                        await this.fetchComponent('/404');\n                        notFoundRoute = '/404';\n                    } catch (_) {\n                        notFoundRoute = '/_error';\n                    }\n                    routeInfo = await this.getRouteInfo({\n                        route: notFoundRoute,\n                        pathname: notFoundRoute,\n                        query,\n                        as,\n                        resolvedAs,\n                        routeProps: {\n                            shallow: false\n                        },\n                        locale: nextState.locale,\n                        isPreview: nextState.isPreview,\n                        isNotFound: true\n                    });\n                    if ('type' in routeInfo) {\n                        throw Object.defineProperty(new Error(\"Unexpected middleware effect on /404\"), \"__NEXT_ERROR_CODE\", {\n                            value: \"E158\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                }\n            }\n            if (isQueryUpdating && this.pathname === '/_error' && ((_self___NEXT_DATA___props = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps = _self___NEXT_DATA___props.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps.statusCode) === 500 && ((_routeInfo_props = routeInfo.props) == null ? void 0 : _routeInfo_props.pageProps)) {\n                // ensure statusCode is still correct for static 500 page\n                // when updating query information\n                routeInfo.props.pageProps.statusCode = 500;\n            }\n            var _routeInfo_route;\n            // shallow routing is only allowed for same page URL changes.\n            const isValidShallowRoute = options.shallow && nextState.route === ((_routeInfo_route = routeInfo.route) != null ? _routeInfo_route : route);\n            var _options_scroll;\n            const shouldScroll = (_options_scroll = options.scroll) != null ? _options_scroll : !isQueryUpdating && !isValidShallowRoute;\n            const resetScroll = shouldScroll ? {\n                x: 0,\n                y: 0\n            } : null;\n            const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n            // the new state that the router gonna set\n            const upcomingRouterState = {\n                ...nextState,\n                route,\n                pathname,\n                query,\n                asPath: cleanedAs,\n                isFallback: false\n            };\n            // When the page being rendered is the 404 page, we should only update the\n            // query parameters. Route changes here might add the basePath when it\n            // wasn't originally present. This is also why this block is before the\n            // below `changeState` call which updates the browser's history (changing\n            // the URL).\n            if (isQueryUpdating && isErrorRoute) {\n                var _self___NEXT_DATA___props_pageProps1, _self___NEXT_DATA___props1, _routeInfo_props1;\n                routeInfo = await this.getRouteInfo({\n                    route: this.pathname,\n                    pathname: this.pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps: {\n                        shallow: false\n                    },\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    isQueryUpdating: isQueryUpdating && !this.isFallback\n                });\n                if ('type' in routeInfo) {\n                    throw Object.defineProperty(new Error(\"Unexpected middleware effect on \" + this.pathname), \"__NEXT_ERROR_CODE\", {\n                        value: \"E225\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (this.pathname === '/_error' && ((_self___NEXT_DATA___props1 = self.__NEXT_DATA__.props) == null ? void 0 : (_self___NEXT_DATA___props_pageProps1 = _self___NEXT_DATA___props1.pageProps) == null ? void 0 : _self___NEXT_DATA___props_pageProps1.statusCode) === 500 && ((_routeInfo_props1 = routeInfo.props) == null ? void 0 : _routeInfo_props1.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    routeInfo.props.pageProps.statusCode = 500;\n                }\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (err) {\n                    if ((0, _iserror.default)(err) && err.cancelled) {\n                        Router.events.emit('routeChangeError', err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                return true;\n            }\n            Router.events.emit('beforeHistoryChange', as, routeProps);\n            this.changeState(method, url, as, options);\n            // for query updates we can skip it if the state is unchanged and we don't\n            // need to scroll\n            // https://github.com/vercel/next.js/issues/37139\n            const canSkipUpdating = isQueryUpdating && !upcomingScrollState && !readyStateChange && !localeChange && (0, _comparestates.compareRouterStates)(upcomingRouterState, this.state);\n            if (!canSkipUpdating) {\n                try {\n                    await this.set(upcomingRouterState, routeInfo, upcomingScrollState);\n                } catch (e) {\n                    if (e.cancelled) routeInfo.error = routeInfo.error || e;\n                    else throw e;\n                }\n                if (routeInfo.error) {\n                    if (!isQueryUpdating) {\n                        Router.events.emit('routeChangeError', routeInfo.error, cleanedAs, routeProps);\n                    }\n                    throw routeInfo.error;\n                }\n                if (false) {}\n                if (!isQueryUpdating) {\n                    Router.events.emit('routeChangeComplete', as, routeProps);\n                }\n                // A hash mark # is the optional last part of a URL\n                const hashRegex = /#.+$/;\n                if (shouldScroll && hashRegex.test(as)) {\n                    this.scrollToHash(as);\n                }\n            }\n            return true;\n        } catch (err) {\n            if ((0, _iserror.default)(err) && err.cancelled) {\n                return false;\n            }\n            throw err;\n        }\n    }\n    changeState(method, url, as, options) {\n        if (options === void 0) options = {};\n        if (true) {\n            if (typeof window.history === 'undefined') {\n                console.error(\"Warning: window.history is not available.\");\n                return;\n            }\n            if (typeof window.history[method] === 'undefined') {\n                console.error(\"Warning: window.history.\" + method + \" is not available\");\n                return;\n            }\n        }\n        if (method !== 'pushState' || (0, _utils.getURL)() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== 'pushState' ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/docs/Web/API/History/replaceState\n            '', as);\n        }\n    }\n    async handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        if (err.cancelled) {\n            // bubble up cancellation errors\n            throw err;\n        }\n        if ((0, _routeloader.isAssetError)(err) || loadErrorFail) {\n            Router.events.emit('routeChangeError', err, as, routeProps);\n            // If we can't load the page it could be one of following reasons\n            //  1. Page doesn't exists\n            //  2. Page does exist in a different zone\n            //  3. Internal error while loading the page\n            // So, doing a hard reload is the proper way to deal with this.\n            handleHardNavigation({\n                url: as,\n                router: this\n            });\n            // Changing the URL doesn't block executing the current code path.\n            // So let's throw a cancellation error stop the routing logic.\n            throw buildCancellationError();\n        }\n        console.error(err);\n        try {\n            let props;\n            const { page: Component, styleSheets } = await this.fetchComponent('/_error');\n            const routeInfo = {\n                props,\n                Component,\n                styleSheets,\n                err,\n                error: err\n            };\n            if (!routeInfo.props) {\n                try {\n                    routeInfo.props = await this.getInitialProps(Component, {\n                        err,\n                        pathname,\n                        query\n                    });\n                } catch (gipErr) {\n                    console.error('Error in error page `getInitialProps`: ', gipErr);\n                    routeInfo.props = {};\n                }\n            }\n            return routeInfo;\n        } catch (routeInfoErr) {\n            return this.handleRouteInfoError((0, _iserror.default)(routeInfoErr) ? routeInfoErr : Object.defineProperty(new Error(routeInfoErr + ''), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            }), pathname, query, as, routeProps, true);\n        }\n    }\n    async getRouteInfo(param) {\n        let { route: requestedRoute, pathname, query, as, resolvedAs, routeProps, locale, hasMiddleware, isPreview, unstable_skipClientCache, isQueryUpdating, isMiddlewareRewrite, isNotFound } = param;\n        /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n        try {\n            var _data_effect, _data_effect1, _data_effect2, _data_response;\n            let existingInfo = this.components[route];\n            if (routeProps.shallow && existingInfo && this.route === route) {\n                return existingInfo;\n            }\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: this\n            });\n            if (hasMiddleware) {\n                existingInfo = undefined;\n            }\n            let cachedRouteInfo = existingInfo && !('initial' in existingInfo) && \"development\" !== 'development' ? 0 : undefined;\n            const isBackground = isQueryUpdating;\n            const fetchNextDataParams = {\n                dataHref: this.pageLoader.getDataHref({\n                    href: (0, _formaturl.formatWithValidation)({\n                        pathname,\n                        query\n                    }),\n                    skipInterpolation: true,\n                    asPath: isNotFound ? '/404' : resolvedAs,\n                    locale\n                }),\n                hasMiddleware: true,\n                isServerRender: this.isSsr,\n                parseJSON: true,\n                inflightCache: isBackground ? this.sbc : this.sdc,\n                persistCache: !isPreview,\n                isPrefetch: false,\n                unstable_skipClientCache,\n                isBackground\n            };\n            let data = isQueryUpdating && !isMiddlewareRewrite ? null : await withMiddlewareEffects({\n                fetchData: ()=>fetchNextData(fetchNextDataParams),\n                asPath: isNotFound ? '/404' : resolvedAs,\n                locale: locale,\n                router: this\n            }).catch((err)=>{\n                // we don't hard error during query updating\n                // as it's un-necessary and doesn't need to be fatal\n                // unless it is a fallback route and the props can't\n                // be loaded\n                if (isQueryUpdating) {\n                    return null;\n                }\n                throw err;\n            });\n            // when rendering error routes we don't apply middleware\n            // effects\n            if (data && (pathname === '/_error' || pathname === '/404')) {\n                data.effect = undefined;\n            }\n            if (isQueryUpdating) {\n                if (!data) {\n                    data = {\n                        json: self.__NEXT_DATA__.props\n                    };\n                } else {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n            }\n            handleCancelled();\n            if ((data == null ? void 0 : (_data_effect = data.effect) == null ? void 0 : _data_effect.type) === 'redirect-internal' || (data == null ? void 0 : (_data_effect1 = data.effect) == null ? void 0 : _data_effect1.type) === 'redirect-external') {\n                return data.effect;\n            }\n            if ((data == null ? void 0 : (_data_effect2 = data.effect) == null ? void 0 : _data_effect2.type) === 'rewrite') {\n                const resolvedRoute = (0, _removetrailingslash.removeTrailingSlash)(data.effect.resolvedHref);\n                const pages = await this.pageLoader.getPageList();\n                // during query updating the page must match although during\n                // client-transition a redirect that doesn't match a page\n                // can be returned and this should trigger a hard navigation\n                // which is valid for incremental migration\n                if (!isQueryUpdating || pages.includes(resolvedRoute)) {\n                    route = resolvedRoute;\n                    pathname = data.effect.resolvedHref;\n                    query = {\n                        ...query,\n                        ...data.effect.parsedAs.query\n                    };\n                    resolvedAs = (0, _removebasepath.removeBasePath)((0, _normalizelocalepath.normalizeLocalePath)(data.effect.parsedAs.pathname, this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = this.components[route];\n                    if (routeProps.shallow && existingInfo && this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return {\n                            ...existingInfo,\n                            route\n                        };\n                    }\n                }\n            }\n            if ((0, _isapiroute.isAPIRoute)(route)) {\n                handleHardNavigation({\n                    url: as,\n                    router: this\n                });\n                return new Promise(()=>{});\n            }\n            const routeInfo = cachedRouteInfo || await this.fetchComponent(route).then((res)=>({\n                    Component: res.page,\n                    styleSheets: res.styleSheets,\n                    __N_SSG: res.mod.__N_SSG,\n                    __N_SSP: res.mod.__N_SSP\n                }));\n            if (true) {\n                const { isValidElementType } = __webpack_require__(/*! next/dist/compiled/react-is */ \"(pages-dir-browser)/./node_modules/next/dist/compiled/react-is/index.js\");\n                if (!isValidElementType(routeInfo.Component)) {\n                    throw Object.defineProperty(new Error('The default export is not a React Component in page: \"' + pathname + '\"'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E286\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            const wasBailedPrefetch = data == null ? void 0 : (_data_response = data.response) == null ? void 0 : _data_response.headers.get('x-middleware-skip');\n            const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n            // For non-SSG prefetches that bailed before sending data\n            // we clear the cache to fetch full response\n            if (wasBailedPrefetch && (data == null ? void 0 : data.dataHref)) {\n                delete this.sdc[data.dataHref];\n            }\n            const { props, cacheKey } = await this._getData(async ()=>{\n                if (shouldFetchData) {\n                    if ((data == null ? void 0 : data.json) && !wasBailedPrefetch) {\n                        return {\n                            cacheKey: data.cacheKey,\n                            props: data.json\n                        };\n                    }\n                    const dataHref = (data == null ? void 0 : data.dataHref) ? data.dataHref : this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname,\n                            query\n                        }),\n                        asPath: resolvedAs,\n                        locale\n                    });\n                    const fetched = await fetchNextData({\n                        dataHref,\n                        isServerRender: this.isSsr,\n                        parseJSON: true,\n                        inflightCache: wasBailedPrefetch ? {} : this.sdc,\n                        persistCache: !isPreview,\n                        isPrefetch: false,\n                        unstable_skipClientCache\n                    });\n                    return {\n                        cacheKey: fetched.cacheKey,\n                        props: fetched.json || {}\n                    };\n                }\n                return {\n                    headers: {},\n                    props: await this.getInitialProps(routeInfo.Component, {\n                        pathname,\n                        query,\n                        asPath: as,\n                        locale,\n                        locales: this.locales,\n                        defaultLocale: this.defaultLocale\n                    })\n                };\n            });\n            // Only bust the data cache for SSP routes although\n            // middleware can skip cache per request with\n            // x-middleware-cache: no-cache as well\n            if (routeInfo.__N_SSP && fetchNextDataParams.dataHref && cacheKey) {\n                delete this.sdc[cacheKey];\n            }\n            // we kick off a HEAD request in the background\n            // when a non-prefetch request is made to signal revalidation\n            if (!this.isPreview && routeInfo.__N_SSG && \"development\" !== 'development' && 0) {}\n            props.pageProps = Object.assign({}, props.pageProps);\n            routeInfo.props = props;\n            routeInfo.route = route;\n            routeInfo.query = query;\n            routeInfo.resolvedAs = resolvedAs;\n            this.components[route] = routeInfo;\n            return routeInfo;\n        } catch (err) {\n            return this.handleRouteInfoError((0, _iserror.getProperError)(err), pathname, query, as, routeProps);\n        }\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components['/_app'].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split('#', 2);\n        const [newUrlNoHash, newHash] = as.split('#', 2);\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = ''] = as.split('#', 2);\n        (0, _handlesmoothscroll.handleSmoothScroll)(()=>{\n            // Scroll to top if the hash is just `#` with no value or `#top`\n            // To mirror browsers\n            if (hash === '' || hash === 'top') {\n                window.scrollTo(0, 0);\n                return;\n            }\n            // Decode hash to make non-latin anchor works.\n            const rawHash = decodeURIComponent(hash);\n            // First we check if the element by id is found\n            const idEl = document.getElementById(rawHash);\n            if (idEl) {\n                idEl.scrollIntoView();\n                return;\n            }\n            // If there's no element with the id, we check the `name` property\n            // To mirror browsers\n            const nameEl = document.getElementsByName(rawHash)[0];\n            if (nameEl) {\n                nameEl.scrollIntoView();\n            }\n        }, {\n            onlyHashChange: this.onlyAHashChange(as)\n        });\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ async prefetch(url, asPath, options) {\n        if (asPath === void 0) asPath = url;\n        if (options === void 0) options = {};\n        // Prefetch is not supported in development mode because it would trigger on-demand-entries\n        if (true) {\n            return;\n        }\n        if ( true && (0, _isbot.isBot)(window.navigator.userAgent)) {\n            // No prefetches for bots that render the link since they are typically navigating\n            // links via the equivalent of a hard navigation and hence never utilize these\n            // prefetches.\n            return;\n        }\n        let parsed = (0, _parserelativeurl.parseRelativeUrl)(url);\n        const urlPathname = parsed.pathname;\n        let { pathname, query } = parsed;\n        const originalPathname = pathname;\n        if (false) {}\n        const pages = await this.pageLoader.getPageList();\n        let resolvedAs = asPath;\n        const locale = typeof options.locale !== 'undefined' ? options.locale || undefined : this.locale;\n        const isMiddlewareMatch = await matchesMiddleware({\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        if (false) {}\n        parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n        if ((0, _isdynamic.isDynamicRoute)(parsed.pathname)) {\n            pathname = parsed.pathname;\n            parsed.pathname = pathname;\n            Object.assign(query, (0, _routematcher.getRouteMatcher)((0, _routeregex.getRouteRegex)(parsed.pathname))((0, _parsepath.parsePath)(asPath).pathname) || {});\n            if (!isMiddlewareMatch) {\n                url = (0, _formaturl.formatWithValidation)(parsed);\n            }\n        }\n        const data =  false ? 0 : await withMiddlewareEffects({\n            fetchData: ()=>fetchNextData({\n                    dataHref: this.pageLoader.getDataHref({\n                        href: (0, _formaturl.formatWithValidation)({\n                            pathname: originalPathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true\n                }),\n            asPath: asPath,\n            locale: locale,\n            router: this\n        });\n        /**\n     * If there was a rewrite we apply the effects of the rewrite on the\n     * current parameters for the prefetch.\n     */ if ((data == null ? void 0 : data.effect.type) === 'rewrite') {\n            parsed.pathname = data.effect.resolvedHref;\n            pathname = data.effect.resolvedHref;\n            query = {\n                ...query,\n                ...data.effect.parsedAs.query\n            };\n            resolvedAs = data.effect.parsedAs.pathname;\n            url = (0, _formaturl.formatWithValidation)(parsed);\n        }\n        /**\n     * If there is a redirect to an external destination then we don't have\n     * to prefetch content as it will be unused.\n     */ if ((data == null ? void 0 : data.effect.type) === 'redirect-external') {\n            return;\n        }\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        if (await this._bfl(asPath, resolvedAs, options.locale, true)) {\n            this.components[urlPathname] = {\n                __appRouter: true\n            };\n        }\n        await Promise.all([\n            this.pageLoader._isSsg(route).then((isSsg)=>{\n                return isSsg ? fetchNextData({\n                    dataHref: (data == null ? void 0 : data.json) ? data == null ? void 0 : data.dataHref : this.pageLoader.getDataHref({\n                        href: url,\n                        asPath: resolvedAs,\n                        locale: locale\n                    }),\n                    isServerRender: false,\n                    parseJSON: true,\n                    inflightCache: this.sdc,\n                    persistCache: !this.isPreview,\n                    isPrefetch: true,\n                    unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                }).then(()=>false).catch(()=>false) : false;\n            }),\n            this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route)\n        ]);\n    }\n    async fetchComponent(route) {\n        const handleCancelled = getCancelledHandler({\n            route,\n            router: this\n        });\n        try {\n            const componentResult = await this.pageLoader.loadPage(route);\n            handleCancelled();\n            return componentResult;\n        } catch (err) {\n            handleCancelled();\n            throw err;\n        }\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = Object.defineProperty(new Error('Loading initial props cancelled'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E405\",\n                    enumerable: false,\n                    configurable: true\n                });\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App } = this.components['/_app'];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils.loadGetInitialProps)(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname, query, as, { initialProps, pageLoader, App, wrapApp, Component, err, subscription, isFallback, locale, locales, defaultLocale, domainLocales, isPreview }){\n        // Server Data Cache (full data requests)\n        this.sdc = {};\n        // Server Background Cache (HEAD requests)\n        this.sbc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname, query } = this;\n                this.changeState('replaceState', (0, _formaturl.formatWithValidation)({\n                    pathname: (0, _addbasepath.addBasePath)(pathname),\n                    query\n                }), (0, _utils.getURL)());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url, as, options, key } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname } = (0, _parserelativeurl.parseRelativeUrl)(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addbasepath.addBasePath)(this.asPath) && pathname === (0, _addbasepath.addBasePath)(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change('replaceState', url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removetrailingslash.removeTrailingSlash)(pathname);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname !== '/_error') {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components['/_app'] = {\n            Component: App,\n            styleSheets: []\n        };\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isdynamic.isDynamicRoute)(pathname) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || '';\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.isExperimentalCompile || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname,\n            query,\n            asPath: autoExportDynamic ? pathname : as,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (true) {\n            // make sure \"as\" doesn't start with double slashes or else it can\n            // throw an error as it's considered invalid\n            if (!as.startsWith('//')) {\n                // in order for `e.state` to work on the `onpopstate` event\n                // we have to register the initial route upon initialization\n                const options = {\n                    locale\n                };\n                const asPath = (0, _utils.getURL)();\n                this._initialMatchesMiddlewarePromise = matchesMiddleware({\n                    router: this,\n                    locale,\n                    asPath\n                }).then((matches)=>{\n                    // if middleware matches we leave resolving to the change function\n                    // as the server needs to resolve for correct priority\n                    ;\n                    options._shouldResolveHref = as !== pathname;\n                    this.changeState('replaceState', matches ? asPath : (0, _formaturl.formatWithValidation)({\n                        pathname: (0, _addbasepath.addBasePath)(pathname),\n                        query\n                    }), asPath, options);\n                    return matches;\n                });\n            }\n            window.addEventListener('popstate', this.onPopState);\n            // enable custom scroll restoration handling when available\n            // otherwise fallback to browser's default handling\n            if (false) {}\n        }\n    }\n}\nRouter.events = (0, _mitt.default)(); //# sourceMappingURL=router.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/router.js\n"));

/***/ })

});