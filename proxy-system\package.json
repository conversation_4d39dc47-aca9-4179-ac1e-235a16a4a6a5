{"name": "proxy-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup-ui": "node -e \"console.log('请运行: scripts/setup-ui.bat (Windows) 或 scripts/setup-ui.sh (Linux/Mac)')\"", "setup-ui:win": "scripts/setup-ui.bat", "setup-ui:unix": "bash scripts/setup-ui.sh"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.10.1", "@types/axios": "^0.14.4", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.4", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}