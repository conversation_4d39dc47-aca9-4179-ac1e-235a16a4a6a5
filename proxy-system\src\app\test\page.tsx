import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-gray-900">样式测试页面</h1>
        
        {/* 颜色测试 */}
        <Card>
          <CardHeader>
            <CardTitle>颜色测试</CardTitle>
            <CardDescription>测试各种颜色是否正常显示</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-blue-500 text-white p-4 rounded">蓝色</div>
              <div className="bg-green-500 text-white p-4 rounded">绿色</div>
              <div className="bg-red-500 text-white p-4 rounded">红色</div>
              <div className="bg-purple-500 text-white p-4 rounded">紫色</div>
            </div>
          </CardContent>
        </Card>

        {/* 按钮测试 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮测试</CardTitle>
            <CardDescription>测试按钮组件是否正常工作</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-x-4">
              <Button>默认按钮</Button>
              <Button variant="outline">边框按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
              <Button className="bg-blue-600 hover:bg-blue-700">自定义按钮</Button>
            </div>
          </CardContent>
        </Card>

        {/* 布局测试 */}
        <Card>
          <CardHeader>
            <CardTitle>布局测试</CardTitle>
            <CardDescription>测试网格和弹性布局</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="bg-gray-100 p-4 rounded">网格项目 1</div>
              <div className="bg-gray-200 p-4 rounded">网格项目 2</div>
              <div className="bg-gray-300 p-4 rounded">网格项目 3</div>
            </div>
          </CardContent>
        </Card>

        {/* 文字测试 */}
        <Card>
          <CardHeader>
            <CardTitle>文字测试</CardTitle>
            <CardDescription>测试各种文字样式</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h1 className="text-4xl font-bold text-gray-900">大标题 H1</h1>
              <h2 className="text-3xl font-semibold text-gray-800">中标题 H2</h2>
              <h3 className="text-2xl font-medium text-gray-700">小标题 H3</h3>
              <p className="text-lg text-gray-600">这是一段普通的文字内容，用来测试文字样式是否正常显示。</p>
              <p className="text-sm text-gray-500">这是小号文字，通常用于说明或备注。</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
