import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { getUserFromToken, getUserFromApiKey, logApiCall } from '@/lib/auth';
import { ProxyAPIClient } from '@/lib/api-client';

export async function DELETE(request: NextRequest) {
  const startTime = Date.now();
  let user = null;
  
  try {
    // 认证
    const authHeader = request.headers.get('authorization');
    const apiKey = request.nextUrl.searchParams.get('api_key');

    if (authHeader) {
      user = await getUserFromToken(authHeader);
    } else if (apiKey) {
      user = await getUserFromApiKey(apiKey);
    }

    if (!user || !user.apiKey) {
      await logApiCall(
        null,
        'DELETE',
        '/api/proxy/delete',
        {},
        { error: '未授权访问或未设置API密钥' },
        401,
        Date.now() - startTime,
        request.ip,
        request.headers.get('user-agent')
      );
      
      return NextResponse.json(
        { error: '未授权访问或未设置API密钥' },
        { status: 401 }
      );
    }

    const { proxyIds } = await request.json();

    // 验证输入
    if (!proxyIds || !Array.isArray(proxyIds) || proxyIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要删除的代理' },
        { status: 400 }
      );
    }

    // 查找用户的代理
    const proxies = await db.proxy.findMany({
      where: {
        id: { in: proxyIds },
        userId: user.id,
      },
    });

    if (proxies.length === 0) {
      return NextResponse.json(
        { error: '未找到指定的代理' },
        { status: 404 }
      );
    }

    if (proxies.length !== proxyIds.length) {
      return NextResponse.json(
        { error: '部分代理不存在或不属于您' },
        { status: 400 }
      );
    }

    // 创建API客户端
    const proxyClient = new ProxyAPIClient(user.apiKey);

    // 构建外部ID列表
    const externalIds = proxies.map(p => p.externalId).join(',');

    try {
      // 从px6.me删除代理
      await proxyClient.deleteProxy({
        ids: externalIds,
      });
    } catch (error) {
      // 如果API删除失败，记录错误但继续删除本地记录
      console.warn('Failed to delete proxies from px6.me:', error);
    }

    // 开始数据库事务
    const result = await db.$transaction(async (tx) => {
      // 删除本地代理记录
      const deletedCount = await tx.proxy.deleteMany({
        where: {
          id: { in: proxyIds },
          userId: user.id,
        },
      });

      // 记录交易（如果有退款逻辑的话）
      const transaction = await tx.transaction.create({
        data: {
          userId: user.id,
          type: 'refund',
          amount: 0, // 这里可以根据业务逻辑计算退款金额
          currency: user.currency,
          description: `删除 ${deletedCount.count} 个代理`,
          status: 'completed',
        },
      });

      return { deletedCount: deletedCount.count, transaction };
    });

    const response = {
      message: `成功删除 ${result.deletedCount} 个代理`,
      deletedCount: result.deletedCount,
      transaction: result.transaction,
    };

    await logApiCall(
      user.id,
      'DELETE',
      '/api/proxy/delete',
      { proxyIds },
      response,
      200,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(response);

  } catch (error) {
    console.error('Delete proxy error:', error);
    
    let errorMessage = '删除代理失败';
    let statusCode = 500;

    if (error instanceof Error) {
      if (error.message.includes('API Error')) {
        errorMessage = error.message;
        statusCode = 400;
      }
    }

    await logApiCall(
      user?.id || null,
      'DELETE',
      '/api/proxy/delete',
      {},
      { error: errorMessage },
      statusCode,
      Date.now() - startTime,
      request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    );
  }
}
