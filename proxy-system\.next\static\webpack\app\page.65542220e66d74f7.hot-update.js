"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/layout/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/typography/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/avatar/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/row/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/col/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Button,Card,Col,Layout,Row,Space,Statistic,Typography!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/statistic/index.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/GlobalOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/SafetyOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ThunderboltOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/ApiOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlayCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/CheckCircleOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/LockOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ApiOutlined,BarChartOutlined,CheckCircleOutlined,EyeOutlined,GlobalOutlined,LockOutlined,PlayCircleOutlined,SafetyOutlined,ThunderboltOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/BarChartOutlined.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst { Header, Content, Footer } = _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"];\nconst { Title, Paragraph, Text } = _barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\nfunction Home() {\n    const stats = [\n        {\n            title: '覆盖国家',\n            value: 50,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 52\n            }, this)\n        },\n        {\n            title: '活跃用户',\n            value: 100000,\n            suffix: '+',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 56\n            }, this)\n        },\n        {\n            title: '稳定性',\n            value: 99.9,\n            suffix: '%',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 53\n            }, this)\n        },\n        {\n            title: '技术支持',\n            value: '24/7',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 43\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        style: {\n            minHeight: '100vh'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Header, {\n                style: {\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    padding: '0 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 40,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 4,\n                                            style: {\n                                                margin: 0,\n                                                color: '#2563eb'\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            type: \"secondary\",\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: \"middle\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        type: \"text\",\n                                        children: \"登录\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/register\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        type: \"primary\",\n                                        children: \"免费注册\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Content, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 1,\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '24px'\n                                    },\n                                    children: [\n                                        \"企业级\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 18\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#2563eb'\n                                            },\n                                            children: \"代理服务解决方案\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        color: '#6b7280',\n                                        marginBottom: '48px',\n                                        maxWidth: '800px',\n                                        margin: '0 auto 48px auto'\n                                    },\n                                    children: [\n                                        \"提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议，\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            strong: true,\n                                            style: {\n                                                color: '#2563eb'\n                                            },\n                                            children: \"99.9%稳定性保证\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"， 助力您的业务全球化发展\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        16\n                                    ],\n                                    style: {\n                                        marginBottom: '48px'\n                                    },\n                                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                size: \"small\",\n                                                style: {\n                                                    textAlign: 'center'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    title: stat.title,\n                                                    value: stat.value,\n                                                    suffix: stat.suffix,\n                                                    prefix: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '1.2rem',\n                                                            color: '#2563eb'\n                                                        },\n                                                        children: stat.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 31\n                                                    }, void 0),\n                                                    valueStyle: {\n                                                        color: '#2563eb',\n                                                        fontWeight: 'bold'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: \"large\",\n                                    wrap: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                type: \"primary\",\n                                                size: \"large\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px'\n                                                },\n                                                children: \"立即开始免费试用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: \"large\",\n                                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px'\n                                            },\n                                            children: \"观看演示\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: '32px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: \"large\",\n                                        wrap: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"无需信用卡\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"7天免费试用\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                                type: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        style: {\n                                                            color: '#059669',\n                                                            marginRight: '8px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"随时取消\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '80px 24px',\n                            background: '#fff'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '1200px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        marginBottom: '64px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 2,\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '16px'\n                                            },\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                            style: {\n                                                fontSize: '1.25rem',\n                                                color: '#6b7280',\n                                                maxWidth: '600px',\n                                                margin: '0 auto'\n                                            },\n                                            children: \"我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    gutter: [\n                                        32,\n                                        32\n                                    ],\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#2563eb'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"多协议支持\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#059669'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"全球节点覆盖\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#d97706'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"极速稳定\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#7c3aed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"企业级安全\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"采用军用级加密技术，保护您的数据传输安全和隐私不被泄露\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#dc2626'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"实时监控\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"提供详细的使用统计和实时监控面板，帮助您优化代理使用效率\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            xs: 24,\n                                            md: 12,\n                                            lg: 8,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                hoverable: true,\n                                                style: {\n                                                    height: '100%',\n                                                    textAlign: 'center'\n                                                },\n                                                bodyStyle: {\n                                                    padding: '32px 24px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '16px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            style: {\n                                                                fontSize: '2rem',\n                                                                color: '#0891b2'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                                        level: 4,\n                                                        style: {\n                                                            marginBottom: '12px'\n                                                        },\n                                                        children: \"API集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                                        style: {\n                                                            color: '#6b7280',\n                                                            lineHeight: '1.6'\n                                                        },\n                                                        children: \"完整的RESTful API接口，支持自动化管理和第三方系统无缝集成\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',\n                            padding: '80px 24px',\n                            textAlign: 'center',\n                            color: 'white'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                maxWidth: '800px',\n                                margin: '0 auto'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                    level: 2,\n                                    style: {\n                                        color: 'white',\n                                        fontSize: '2.5rem',\n                                        marginBottom: '16px'\n                                    },\n                                    children: \"准备开始了吗？\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        marginBottom: '32px',\n                                        opacity: 0.9,\n                                        color: 'white'\n                                    },\n                                    children: \"加入10万+用户的行列，体验专业的代理服务平台\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: \"large\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/register\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: \"large\",\n                                                style: {\n                                                    height: '48px',\n                                                    padding: '0 32px',\n                                                    fontSize: '16px',\n                                                    background: 'white',\n                                                    color: '#2563eb',\n                                                    border: 'none'\n                                                },\n                                                children: \"立即免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: \"large\",\n                                            ghost: true,\n                                            style: {\n                                                height: '48px',\n                                                padding: '0 32px',\n                                                fontSize: '16px',\n                                                borderColor: 'white',\n                                                color: 'white'\n                                            },\n                                            children: \"联系销售团队\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {\n                style: {\n                    background: '#001529',\n                    color: 'white',\n                    padding: '48px 24px 16px 24px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        textAlign: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '16px',\n                                marginBottom: '32px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Button_Card_Col_Layout_Row_Space_Statistic_Typography_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 48,\n                                    style: {\n                                        backgroundColor: '#2563eb'\n                                    },\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ApiOutlined_BarChartOutlined_CheckCircleOutlined_EyeOutlined_GlobalOutlined_LockOutlined_PlayCircleOutlined_SafetyOutlined_ThunderboltOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Title, {\n                                            level: 3,\n                                            style: {\n                                                color: '#2563eb',\n                                                margin: 0\n                                            },\n                                            children: \"ProxyHub\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                                            style: {\n                                                color: '#8c8c8c',\n                                                fontSize: '14px'\n                                            },\n                                            children: \"企业级代理服务平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Paragraph, {\n                            style: {\n                                color: '#8c8c8c',\n                                marginBottom: '32px'\n                            },\n                            children: \"为全球用户提供稳定、高速、安全的代理服务解决方案\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Text, {\n                            style: {\n                                color: '#8c8c8c',\n                                fontSize: '14px'\n                            },\n                            children: \"\\xa9 2024 ProxyHub. 保留所有权利.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 359,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\代码\\\\Proxy\\\\vscode_proxy\\\\proxy-system\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});