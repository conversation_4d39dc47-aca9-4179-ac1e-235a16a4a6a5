'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  Alert,
  Layout,
  Avatar,
  Divider,
  Checkbox,
  Row,
  Col
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  SafetyOutlined,
  LoginOutlined,
  HomeOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Content } = Layout;

interface LoginFormData {
  login: string;
  password: string;
}

export default function LoginPage() {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (values: LoginFormData) => {
    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (response.ok) {
        // 保存token到localStorage
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        
        // 跳转到仪表板
        router.push('/dashboard');
      } else {
        setError(data.error || '登录失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout style={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'
    }}>
      <Content style={{ 
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '24px'
      }}>
        <div style={{ width: '100%', maxWidth: '480px' }}>
          {/* Logo */}
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Link href="/" style={{ textDecoration: 'none' }}>
              <Space direction="vertical" size="small">
                <Avatar
                  size={64}
                  style={{ backgroundColor: '#2563eb' }}
                  icon={<SafetyOutlined />}
                />
                <div>
                  <Title level={2} style={{ margin: 0, color: '#2563eb' }}>
                    ProxyHub
                  </Title>
                  <Text type="secondary">企业级代理服务平台</Text>
                </div>
              </Space>
            </Link>
          </div>

          {/* Login Form */}
          <Card 
            style={{ 
              boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
              borderRadius: '12px'
            }}
            bodyStyle={{ padding: '32px' }}
          >
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <Title level={3} style={{ marginBottom: '8px', color: '#1890ff' }}>
                🔐 用户登录
              </Title>
              <Paragraph type="secondary" style={{ fontSize: '15px' }}>
                欢迎回来！请输入您的账户信息
              </Paragraph>
              <Text type="secondary" style={{ fontSize: '13px' }}>
                登录后即可访问专业的代理服务管理平台
              </Text>
            </div>

            {error && (
              <Alert
                message={error}
                type="error"
                showIcon
                style={{ marginBottom: '24px' }}
                closable
                onClose={() => setError('')}
              />
            )}

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              layout="vertical"
              size="large"
              autoComplete="off"
            >
              <Form.Item
                label={
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>
                    📧 用户名或邮箱
                  </span>
                }
                name="login"
                rules={[
                  { required: true, message: '请输入用户名或邮箱' },
                  { min: 3, message: '用户名至少3个字符' }
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请输入用户名或邮箱地址"
                  autoComplete="username"
                  style={{ height: '44px' }}
                />
              </Form.Item>

              <Form.Item
                label={
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>
                    🔒 登录密码
                  </span>
                }
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined style={{ color: '#8c8c8c' }} />}
                  placeholder="请输入您的登录密码"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  autoComplete="current-password"
                  style={{ height: '44px' }}
                />
              </Form.Item>

              {/* 记住我和忘记密码 */}
              <Form.Item style={{ marginBottom: '24px' }}>
                <Row justify="space-between" align="middle">
                  <Col>
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                      <Checkbox>记住我</Checkbox>
                    </Form.Item>
                  </Col>
                  <Col>
                    <Button type="link" style={{ padding: 0, fontSize: '14px' }}>
                      忘记密码？
                    </Button>
                  </Col>
                </Row>
              </Form.Item>

              <Form.Item style={{ marginBottom: '16px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  block
                  icon={<LoginOutlined />}
                  style={{ height: '48px', fontSize: '16px' }}
                >
                  {isLoading ? '登录中...' : '登录'}
                </Button>
              </Form.Item>
            </Form>

            <Divider plain>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                其他选项
              </Text>
            </Divider>

            <div style={{ textAlign: 'center' }}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                <Text type="secondary">
                  还没有账户？{' '}
                  <Link href="/register" style={{ color: '#2563eb', fontWeight: 500 }}>
                    立即注册
                  </Link>
                </Text>
                
                <Link href="/">
                  <Button 
                    type="text" 
                    icon={<HomeOutlined />}
                    style={{ color: '#8c8c8c' }}
                  >
                    返回首页
                  </Button>
                </Link>
              </Space>
            </div>
          </Card>

          {/* Login Benefits */}
          <Card
            size="small"
            style={{
              marginTop: '24px',
              background: '#f0f9ff',
              borderColor: '#91d5ff'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <Text strong style={{ color: '#1890ff', fontSize: '14px' }}>
                🚀 登录即可享受
              </Text>
              <div style={{ marginTop: '12px' }}>
                <Row gutter={[16, 8]}>
                  <Col span={12}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      ✓ 专业代理服务
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      ✓ 实时监控面板
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      ✓ API 接口调用
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                      ✓ 24/7 技术支持
                    </Text>
                  </Col>
                </Row>
              </div>
            </div>
          </Card>

          {/* Security Notice */}
          <Card 
            size="small"
            style={{ 
              marginTop: '24px',
              background: '#f6ffed',
              borderColor: '#b7eb8f'
            }}
          >
            <Space>
              <SafetyOutlined style={{ color: '#52c41a' }} />
              <div>
                <Text strong style={{ color: '#389e0d', fontSize: '14px' }}>
                  安全提示
                </Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  我们使用企业级加密技术保护您的账户安全
                </Text>
              </div>
            </Space>
          </Card>
        </div>
      </Content>
    </Layout>
  );
}
