import Link from "next/link";

export default function SimplePage() {
  return (
    <div style={{ minHeight: '100vh', fontFamily: 'system-ui, sans-serif' }}>
      {/* Header */}
      <header style={{ 
        borderBottom: '1px solid #e5e7eb', 
        backgroundColor: 'white',
        padding: '1rem 0',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 1rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{ 
              backgroundColor: '#2563eb', 
              padding: '0.75rem', 
              borderRadius: '0.5rem',
              color: 'white',
              fontSize: '1.25rem'
            }}>
              🛡️
            </div>
            <div>
              <h1 style={{ 
                fontSize: '1.75rem', 
                fontWeight: 'bold', 
                color: '#2563eb',
                margin: 0
              }}>
                ProxyHub
              </h1>
              <p style={{ 
                fontSize: '0.875rem', 
                color: '#6b7280',
                margin: 0
              }}>专业代理服务平台</p>
            </div>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <Link href="/login" style={{ 
              color: '#6b7280', 
              textDecoration: 'none',
              padding: '0.5rem 1rem',
              borderRadius: '0.375rem',
              border: '1px solid #d1d5db'
            }}>
              登录
            </Link>
            <Link href="/register" style={{ 
              backgroundColor: '#2563eb',
              color: 'white',
              textDecoration: 'none',
              padding: '0.5rem 1.5rem',
              borderRadius: '0.375rem',
              fontWeight: '500'
            }}>
              免费注册
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section style={{ 
        backgroundColor: '#f9fafb', 
        padding: '5rem 0',
        textAlign: 'center'
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 1rem'
        }}>
          <div style={{ 
            backgroundColor: '#dbeafe',
            color: '#1e40af',
            display: 'inline-block',
            padding: '0.5rem 1rem',
            borderRadius: '9999px',
            fontSize: '0.875rem',
            fontWeight: '500',
            marginBottom: '2rem'
          }}>
            ⭐ 全球领先的代理服务平台
          </div>

          <h1 style={{ 
            fontSize: '3rem', 
            fontWeight: 'bold', 
            color: '#111827',
            marginBottom: '1.5rem',
            lineHeight: '1.1'
          }}>
            企业级<br />
            <span style={{ color: '#2563eb' }}>代理服务解决方案</span>
          </h1>

          <p style={{ 
            fontSize: '1.25rem', 
            color: '#6b7280',
            marginBottom: '3rem',
            maxWidth: '800px',
            margin: '0 auto 3rem auto'
          }}>
            提供高质量的全球代理网络，支持HTTP/HTTPS和SOCKS5协议，
            <span style={{ color: '#2563eb', fontWeight: '600' }}>99.9%稳定性保证</span>，
            助力您的业务全球化发展
          </p>

          {/* Stats */}
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(4, 1fr)', 
            gap: '2rem',
            marginBottom: '3rem',
            maxWidth: '600px',
            margin: '0 auto 3rem auto'
          }}>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2563eb' }}>50+</div>
              <div style={{ color: '#6b7280' }}>覆盖国家</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#7c3aed' }}>10万+</div>
              <div style={{ color: '#6b7280' }}>活跃用户</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#059669' }}>99.9%</div>
              <div style={{ color: '#6b7280' }}>稳定性</div>
            </div>
            <div>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#dc2626' }}>24/7</div>
              <div style={{ color: '#6b7280' }}>技术支持</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', marginBottom: '3rem' }}>
            <Link href="/register" style={{ 
              backgroundColor: '#2563eb',
              color: 'white',
              textDecoration: 'none',
              padding: '1rem 2rem',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              ▶️ 立即开始免费试用
            </Link>
            <Link href="#demo" style={{ 
              border: '2px solid #d1d5db',
              color: '#374151',
              textDecoration: 'none',
              padding: '1rem 2rem',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}>
              观看演示 →
            </Link>
          </div>

          {/* Trust indicators */}
          <div style={{ 
            display: 'flex', 
            gap: '2rem', 
            justifyContent: 'center',
            color: '#6b7280',
            fontSize: '0.875rem'
          }}>
            <div>✅ 无需信用卡</div>
            <div>✅ 7天免费试用</div>
            <div>✅ 随时取消</div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section style={{ padding: '5rem 0', backgroundColor: 'white' }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 1rem'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
            <h2 style={{ 
              fontSize: '2.5rem', 
              fontWeight: 'bold', 
              color: '#111827',
              marginBottom: '1rem'
            }}>
              为什么选择我们？
            </h2>
            <p style={{ 
              fontSize: '1.25rem', 
              color: '#6b7280',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              我们提供业界领先的代理服务，助力您的业务在全球范围内稳定运行
            </p>
          </div>

          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
            gap: '2rem'
          }}>
            {/* Feature Cards */}
            {[
              { icon: '🖥️', title: '多协议支持', desc: '支持HTTP/HTTPS和SOCKS5协议，满足不同应用场景的需求，兼容性强', color: '#2563eb' },
              { icon: '🌍', title: '全球节点覆盖', desc: '覆盖50+个国家和地区，提供低延迟、高速度的网络访问体验', color: '#059669' },
              { icon: '⚡', title: '极速稳定', desc: '99.9%稳定性保证，优质的网络基础设施，确保服务高速稳定运行', color: '#d97706' },
              { icon: '🔒', title: '企业级安全', desc: '采用军用级加密技术，保护您的数据传输安全和隐私不被泄露', color: '#7c3aed' },
              { icon: '📊', title: '实时监控', desc: '提供详细的使用统计和实时监控面板，帮助您优化代理使用效率', color: '#dc2626' },
              { icon: '🛡️', title: 'API集成', desc: '完整的RESTful API接口，支持自动化管理和第三方系统无缝集成', color: '#0891b2' }
            ].map((feature, index) => (
              <div key={index} style={{ 
                backgroundColor: 'white',
                border: '1px solid #e5e7eb',
                borderRadius: '0.75rem',
                padding: '2rem',
                textAlign: 'center',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                transition: 'transform 0.2s, box-shadow 0.2s'
              }}>
                <div style={{ 
                  width: '4rem',
                  height: '4rem',
                  backgroundColor: feature.color,
                  borderRadius: '0.75rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 1rem auto',
                  fontSize: '1.5rem'
                }}>
                  {feature.icon}
                </div>
                <h3 style={{ 
                  fontSize: '1.25rem', 
                  fontWeight: 'bold', 
                  color: '#111827',
                  marginBottom: '0.5rem'
                }}>
                  {feature.title}
                </h3>
                <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                  {feature.desc}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section style={{ 
        background: 'linear-gradient(135deg, #2563eb 0%, #7c3aed 100%)',
        padding: '5rem 0',
        textAlign: 'center',
        color: 'white'
      }}>
        <div style={{ 
          maxWidth: '800px', 
          margin: '0 auto', 
          padding: '0 1rem'
        }}>
          <h2 style={{ 
            fontSize: '2.5rem', 
            fontWeight: 'bold', 
            marginBottom: '1rem'
          }}>
            准备开始了吗？
          </h2>
          <p style={{ 
            fontSize: '1.25rem', 
            marginBottom: '2rem',
            opacity: 0.9
          }}>
            加入10万+用户的行列，体验专业的代理服务平台
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
            <Link href="/register" style={{ 
              backgroundColor: 'white',
              color: '#2563eb',
              textDecoration: 'none',
              padding: '1rem 2rem',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem'
            }}>
              立即免费注册
            </Link>
            <Link href="#contact" style={{ 
              border: '2px solid white',
              color: 'white',
              textDecoration: 'none',
              padding: '1rem 2rem',
              borderRadius: '0.5rem',
              fontWeight: '600',
              fontSize: '1.125rem'
            }}>
              联系销售团队
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer style={{ 
        backgroundColor: '#111827',
        color: 'white',
        padding: '3rem 0 1rem 0'
      }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 1rem',
          textAlign: 'center'
        }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            gap: '1rem',
            marginBottom: '2rem'
          }}>
            <div style={{ 
              backgroundColor: '#2563eb', 
              padding: '0.5rem', 
              borderRadius: '0.5rem',
              fontSize: '1.25rem'
            }}>
              🛡️
            </div>
            <div>
              <h3 style={{ 
                fontSize: '1.5rem', 
                fontWeight: 'bold', 
                color: '#2563eb',
                margin: 0
              }}>
                ProxyHub
              </h3>
              <p style={{ 
                fontSize: '0.875rem', 
                color: '#9ca3af',
                margin: 0
              }}>专业代理服务平台</p>
            </div>
          </div>
          
          <p style={{ 
            color: '#9ca3af',
            marginBottom: '2rem'
          }}>
            为全球用户提供稳定、高速、安全的代理服务解决方案
          </p>
          
          <div style={{ 
            borderTop: '1px solid #374151',
            paddingTop: '1rem',
            color: '#9ca3af',
            fontSize: '0.875rem'
          }}>
            © 2024 ProxyHub. 保留所有权利.
          </div>
        </div>
      </footer>
    </div>
  );
}
