import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RestFilledSvg from "@ant-design/icons-svg/es/asn/RestFilled";
import AntdIcon from "../components/AntdIcon";
var RestFilled = function RestFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RestFilledSvg
  }));
};

/**![rest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiAyNTZoLTI4LjFsLTM1LjctMTIwLjljLTQtMTMuNy0xNi41LTIzLjEtMzAuNy0yMy4xaC00NTFjLTE0LjMgMC0yNi44IDkuNC0zMC43IDIzLjFMMjIwLjEgMjU2SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjhjMCA0LjQgMy42IDggOCA4aDQ1LjhsNDcuNyA1NTguN2EzMiAzMiAwIDAwMzEuOSAyOS4zaDQyOS4yYTMyIDMyIDAgMDAzMS45LTI5LjNMODAyLjIgMzI0SDg1NmM0LjQgMCA4LTMuNiA4LTh2LTI4YzAtMTcuNy0xNC4zLTMyLTMyLTMyek01MDggNzA0Yy03OS41IDAtMTQ0LTY0LjUtMTQ0LTE0NHM2NC41LTE0NCAxNDQtMTQ0IDE0NCA2NC41IDE0NCAxNDQtNjQuNSAxNDQtMTQ0IDE0NHpNMjkxIDI1NmwyMi40LTc2aDM5Ny4ybDIyLjQgNzZIMjkxem0xMzcgMzA0YTgwIDgwIDAgMTAxNjAgMCA4MCA4MCAwIDEwLTE2MCAweiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(RestFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RestFilled';
}
export default RefIcon;